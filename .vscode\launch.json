{"version": "0.2.0", "configurations": [{"name": "Launch IndustrialHMI (x64)", "type": "clr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/IndustrialHMI.exe", "args": [], "cwd": "${workspaceFolder}/bin/Debug", "stopAtEntry": false, "console": "internalConsole", "enableStepFiltering": true, "justMyCode": true, "requireExactSource": false, "symbolOptions": {"searchPaths": ["${workspaceFolder}/bin/Debug"], "searchMicrosoftSymbolServer": false, "searchNuGetOrgSymbolServer": false}, "preLaunchTask": "build-x64"}, {"name": "Launch DeviceModule Tests (x64)", "type": "clr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/Tests/DeviceModule.Tests.exe", "args": [], "cwd": "${workspaceFolder}/bin/Debug/Tests", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build-tests-x64"}, {"name": "Launch System Acceptance Test (x64)", "type": "clr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/Tests/SystemAcceptanceTest.exe", "args": [], "cwd": "${workspaceFolder}/bin/Debug/Tests", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build-tests-x64"}, {"name": "Attach to IndustrialHMI Process", "type": "clr", "request": "attach", "processName": "IndustrialHMI.exe"}]}