# 工业HMI框架部署指南

## 版本信息

- **软件版本**: 1.0.0
- **文档版本**: 1.0.0
- **发布日期**: 2025-08-13
- **目标读者**: 系统管理员、运维工程师

## 目录

1. [部署概述](#部署概述)
2. [环境准备](#环境准备)
3. [部署方式](#部署方式)
4. [配置管理](#配置管理)
5. [安全配置](#安全配置)
6. [监控和维护](#监控和维护)
7. [故障排除](#故障排除)

## 部署概述

### 系统架构

工业HMI框架采用模块化架构，包含以下核心组件：

- **主程序**: IndustrialHMI.exe
- **核心库**: Contracts.dll, Services.dll
- **日志组件**: Serilog相关DLL
- **功能模块**: 位于Modules文件夹的各个模块DLL

### 部署目标

- 确保系统在目标环境中稳定运行
- 提供完整的功能模块支持
- 建立有效的监控和维护机制
- 保证系统安全性和可靠性

### 部署策略

- **单机部署**: 适用于独立工作站
- **网络部署**: 适用于多用户环境
- **集群部署**: 适用于高可用性要求

## 环境准备

### 硬件要求

**最低配置**:

- CPU: Intel Core i3 或同等性能
- 内存: 4GB RAM
- 存储: 1GB 可用空间
- 网络: 100Mbps 以太网

**推荐配置**:

- CPU: Intel Core i5 或更高
- 内存: 8GB RAM 或更多
- 存储: 10GB 可用空间（包含日志和数据）
- 网络: 1Gbps 以太网

### 软件要求

**操作系统**:

- Windows 10 Professional (64位)
- Windows 11 Professional (64位)
- Windows Server 2016/2019/2022

**运行时环境**:

- .NET Framework 4.8 或更高版本
- Visual C++ Redistributable 2015-2022

**可选组件**:

- Windows PowerShell 5.1 或更高版本（用于部署脚本）
- Windows Management Framework 5.1

### 网络配置

**端口要求**:

- 应用程序默认不需要特定端口
- 如需远程访问，请配置防火墙规则
- 建议为日志和监控预留端口

**防火墙设置**:

```powershell
# 允许应用程序通过防火墙
New-NetFirewallRule -DisplayName "Industrial HMI Framework" -Direction Inbound -Program "C:\IndustrialHMI\IndustrialHMI.exe" -Action Allow
```

## 部署方式

### 方式一：自动化部署（推荐）

**步骤1: 准备部署包**

```
部署包结构:
├── Deploy.ps1              # 部署脚本
├── Uninstall.ps1          # 卸载脚本
├── bin\Debug\              # 应用程序文件
│   ├── IndustrialHMI.exe
│   ├── *.dll
│   └── Modules\
└── 交付文档\               # 文档文件
```

**步骤2: 执行部署**

```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 导航到部署包目录
cd "部署包路径"

# 执行标准部署
.\Deploy.ps1

# 或自定义部署路径
.\Deploy.ps1 -TargetPath "D:\IndustrialHMI" -Verbose
```

**步骤3: 验证部署**

```powershell
# 检查安装目录
Test-Path "C:\IndustrialHMI\IndustrialHMI.exe"

# 检查桌面快捷方式
Test-Path "$env:USERPROFILE\Desktop\工业HMI框架.lnk"

# 启动应用程序测试
Start-Process "C:\IndustrialHMI\IndustrialHMI.exe"
```

### 方式二：手动部署

**步骤1: 创建目录结构**

```powershell
$installPath = "C:\IndustrialHMI"
New-Item -ItemType Directory -Path $installPath -Force
New-Item -ItemType Directory -Path "$installPath\Modules" -Force
New-Item -ItemType Directory -Path "$installPath\logs" -Force
New-Item -ItemType Directory -Path "$installPath\config" -Force
New-Item -ItemType Directory -Path "$installPath\backup" -Force
```

**步骤2: 复制应用程序文件**

```powershell
# 复制主程序文件
Copy-Item "源路径\IndustrialHMI.exe" "$installPath\"
Copy-Item "源路径\*.dll" "$installPath\"
Copy-Item "源路径\*.config" "$installPath\"

# 复制模块文件
Copy-Item "源路径\Modules\*" "$installPath\Modules\" -Recurse
```

**步骤3: 配置权限**

```powershell
# 设置目录权限
$acl = Get-Acl $installPath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Users","FullControl","ContainerInherit,ObjectInherit","None","Allow")
$acl.SetAccessRule($accessRule)
Set-Acl $installPath $acl
```

### 方式三：网络部署

**步骤1: 准备网络共享**

```powershell
# 创建网络共享
New-SmbShare -Name "IndustrialHMI" -Path "C:\IndustrialHMI" -ReadAccess "Everyone"
```

**步骤2: 客户端部署脚本**

```powershell
# 客户端部署脚本
$networkPath = "\\服务器IP\IndustrialHMI"
$localPath = "C:\IndustrialHMI"

# 复制文件
robocopy $networkPath $localPath /E /R:3 /W:5

# 创建快捷方式
$shell = New-Object -ComObject WScript.Shell
$shortcut = $shell.CreateShortcut("$env:USERPROFILE\Desktop\工业HMI框架.lnk")
$shortcut.TargetPath = "$localPath\IndustrialHMI.exe"
$shortcut.Save()
```

## 配置管理

### 应用程序配置

**主配置文件**: `IndustrialHMI.exe.config`

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- 模块路径配置 -->
    <add key="ModulesPath" value="Modules" />
    
    <!-- 日志配置 -->
    <add key="LogLevel" value="Information" />
    <add key="LogPath" value="logs" />
    <add key="LogRetentionDays" value="30" />
    
    <!-- 性能配置 -->
    <add key="PerformanceMonitoringEnabled" value="true" />
    <add key="PerformanceMonitoringInterval" value="5000" />
    
    <!-- 模块配置 -->
    <add key="AutoStartModules" value="true" />
    <add key="ModuleLoadTimeout" value="30000" />
  </appSettings>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
</configuration>
```

**日志配置**: `config\logging.config`

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <logSettings>
    <minimumLevel>Information</minimumLevel>
    <filePath>logs\application.log</filePath>
    <rollingInterval>Day</rollingInterval>
    <retainedFileCountLimit>30</retainedFileCountLimit>
    <outputTemplate>[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level:u3}] {Message:lj}{NewLine}{Exception}</outputTemplate>
  </logSettings>
</configuration>
```

### 模块配置

**模块配置文件**: `config\modules.config`

```xml
<?xml version="1.0" encoding="utf-8"?>
<modules>
  <module name="DeviceModule" enabled="true" autoStart="true" />
  <module name="AlarmModule" enabled="true" autoStart="true" />
  <module name="CommunicationTestModule" enabled="true" autoStart="false" />
  <module name="TestFrameworkModule" enabled="false" autoStart="false" />
</modules>
```

### 环境变量配置

```powershell
# 设置系统环境变量
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_HOME", "C:\IndustrialHMI", "Machine")
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_LOG_LEVEL", "Information", "Machine")
```

## 安全配置

### 文件系统权限

```powershell
# 设置安装目录权限
$installPath = "C:\IndustrialHMI"

# 管理员完全控制
$adminRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Administrators","FullControl","ContainerInherit,ObjectInherit","None","Allow")

# 用户读取和执行权限
$userRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Users","ReadAndExecute","ContainerInherit,ObjectInherit","None","Allow")

# 应用权限
$acl = Get-Acl $installPath
$acl.SetAccessRule($adminRule)
$acl.SetAccessRule($userRule)
Set-Acl $installPath $acl
```

### 应用程序权限

**UAC配置**: 创建应用程序清单文件

```xml
<!-- IndustrialHMI.exe.manifest -->
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="requireAdministrator" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>
```

### 网络安全

**防火墙规则**:

```powershell
# 创建入站规则
New-NetFirewallRule -DisplayName "Industrial HMI - Inbound" -Direction Inbound -Program "C:\IndustrialHMI\IndustrialHMI.exe" -Action Allow

# 创建出站规则
New-NetFirewallRule -DisplayName "Industrial HMI - Outbound" -Direction Outbound -Program "C:\IndustrialHMI\IndustrialHMI.exe" -Action Allow
```

## 监控和维护

### 性能监控

**系统性能监控脚本**:

```powershell
# 监控脚本: Monitor-IndustrialHMI.ps1
$processName = "IndustrialHMI"
$logPath = "C:\IndustrialHMI\logs\performance.log"

while ($true) {
    $process = Get-Process -Name $processName -ErrorAction SilentlyContinue
    if ($process) {
        $cpuUsage = $process.CPU
        $memoryUsage = $process.WorkingSet64 / 1MB
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        
        $logEntry = "$timestamp - CPU: $cpuUsage, Memory: $memoryUsage MB"
        Add-Content -Path $logPath -Value $logEntry
    }
    
    Start-Sleep -Seconds 60
}
```

### 日志管理

**日志清理脚本**:

```powershell
# 清理30天前的日志文件
$logPath = "C:\IndustrialHMI\logs"
$retentionDays = 30

Get-ChildItem -Path $logPath -Filter "*.log" | 
    Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-$retentionDays) } |
    Remove-Item -Force
```

### 自动备份

**备份脚本**:

```powershell
# 备份配置和日志
$sourcePath = "C:\IndustrialHMI"
$backupPath = "D:\Backup\IndustrialHMI_$(Get-Date -Format 'yyyyMMdd')"

# 创建备份目录
New-Item -ItemType Directory -Path $backupPath -Force

# 备份配置文件
Copy-Item "$sourcePath\config" "$backupPath\config" -Recurse -Force

# 备份最近7天的日志
$recentLogs = Get-ChildItem "$sourcePath\logs" -Filter "*.log" | 
    Where-Object { $_.LastWriteTime -gt (Get-Date).AddDays(-7) }
$recentLogs | Copy-Item -Destination "$backupPath\logs" -Force
```

### 健康检查

**健康检查脚本**:

```powershell
# 健康检查脚本
function Test-IndustrialHMIHealth {
    $results = @{}
    
    # 检查进程状态
    $process = Get-Process -Name "IndustrialHMI" -ErrorAction SilentlyContinue
    $results.ProcessRunning = $process -ne $null
    
    # 检查文件完整性
    $requiredFiles = @(
        "C:\IndustrialHMI\IndustrialHMI.exe",
        "C:\IndustrialHMI\Contracts.dll",
        "C:\IndustrialHMI\Services.dll"
    )
    $results.FilesIntact = ($requiredFiles | ForEach-Object { Test-Path $_ }) -notcontains $false
    
    # 检查日志文件
    $latestLog = Get-ChildItem "C:\IndustrialHMI\logs" -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $results.LogsActive = $latestLog -and $latestLog.LastWriteTime -gt (Get-Date).AddHours(-1)
    
    return $results
}
```

## 故障排除

### 常见部署问题

**问题1: .NET Framework版本不兼容**

```powershell
# 检查.NET Framework版本
Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release

# 安装.NET Framework 4.8
# 下载并运行官方安装程序
```

**问题2: 权限不足**

```powershell
# 检查当前用户权限
whoami /priv

# 以管理员身份重新运行部署脚本
Start-Process PowerShell -Verb RunAs -ArgumentList "-File Deploy.ps1"
```

**问题3: 模块加载失败**

```powershell
# 检查模块文件完整性
Get-ChildItem "C:\IndustrialHMI\Modules" -Filter "*.dll" | ForEach-Object {
    try {
        [System.Reflection.Assembly]::LoadFrom($_.FullName)
        Write-Host "$($_.Name) - OK" -ForegroundColor Green
    } catch {
        Write-Host "$($_.Name) - ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
}
```

### 日志分析

**查看部署日志**:

```powershell
# 查看部署日志
Get-Content "deploy.log" -Tail 50

# 搜索错误信息
Select-String -Path "deploy.log" -Pattern "ERROR|FAILED"
```

**查看应用程序日志**:

```powershell
# 查看最新的应用程序日志
Get-Content "C:\IndustrialHMI\logs\application*.log" -Tail 100

# 搜索特定错误
Select-String -Path "C:\IndustrialHMI\logs\*.log" -Pattern "Exception|Error"
```

## 配置管理部署

### 配置文件结构

部署后的配置文件结构：

```
C:\IndustrialHMI\
├── IndustrialHMI.exe.config          # 默认配置文件
├── config\
│   ├── application.config            # 自定义配置文件
│   ├── production.config             # 生产环境配置
│   └── development.config            # 开发环境配置
└── logs\                             # 日志目录
```

### 配置文件部署

**1. 创建配置目录**:

```powershell
# 创建配置目录
New-Item -ItemType Directory -Path "C:\IndustrialHMI\config" -Force

# 设置配置目录权限
$acl = Get-Acl "C:\IndustrialHMI\config"
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("Users", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl "C:\IndustrialHMI\config" $acl
```

**2. 部署配置文件**:

```powershell
# 复制配置文件模板
Copy-Item "templates\application.config" "C:\IndustrialHMI\config\application.config"
Copy-Item "templates\production.config" "C:\IndustrialHMI\config\production.config"

# 根据环境选择配置文件
$environment = $env:DEPLOYMENT_ENVIRONMENT ?? "production"
Copy-Item "C:\IndustrialHMI\config\$environment.config" "C:\IndustrialHMI\config\application.config" -Force
```

### 环境变量配置

**生产环境变量设置**:

```powershell
# 设置系统环境变量
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_Environment", "Production", "Machine")
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_LogLevel", "Info", "Machine")
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_Database_Host", "prod-db-server", "Machine")
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_Database_Port", "5432", "Machine")

# 验证环境变量
Get-ChildItem Env: | Where-Object Name -like "INDUSTRIAL_HMI_*"
```

**开发环境变量设置**:

```powershell
# 设置用户环境变量（开发环境）
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_Environment", "Development", "User")
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_LogLevel", "Debug", "User")
[Environment]::SetEnvironmentVariable("INDUSTRIAL_HMI_Database_Host", "localhost", "User")
```

### 配置验证

**配置文件验证脚本**:

```powershell
function Test-ConfigurationFiles {
    $configPath = "C:\IndustrialHMI\config"
    $errors = @()

    # 检查配置目录
    if (-not (Test-Path $configPath)) {
        $errors += "配置目录不存在: $configPath"
    }

    # 检查主配置文件
    $mainConfig = "$configPath\application.config"
    if (-not (Test-Path $mainConfig)) {
        $errors += "主配置文件不存在: $mainConfig"
    } else {
        # 验证XML格式
        try {
            [xml]$xml = Get-Content $mainConfig
            Write-Host "配置文件格式正确: $mainConfig" -ForegroundColor Green
        } catch {
            $errors += "配置文件格式错误: $mainConfig - $($_.Exception.Message)"
        }
    }

    # 检查环境变量
    $requiredEnvVars = @("INDUSTRIAL_HMI_Environment")
    foreach ($envVar in $requiredEnvVars) {
        if (-not $env:$envVar) {
            $errors += "缺少环境变量: $envVar"
        }
    }

    if ($errors.Count -eq 0) {
        Write-Host "配置验证通过" -ForegroundColor Green
        return $true
    } else {
        Write-Host "配置验证失败:" -ForegroundColor Red
        $errors | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
        return $false
    }
}

# 运行配置验证
Test-ConfigurationFiles
```

### 配置热更新测试

**热更新测试脚本**:

```powershell
function Test-ConfigurationHotReload {
    $configFile = "C:\IndustrialHMI\config\application.config"

    Write-Host "测试配置热更新功能..." -ForegroundColor Yellow

    # 备份原配置
    $backup = "$configFile.backup"
    Copy-Item $configFile $backup

    try {
        # 修改配置文件
        $xml = [xml](Get-Content $configFile)
        $testKey = $xml.configuration.appSettings.SelectSingleNode("add[@key='TestHotReload']")

        if ($testKey) {
            $testKey.value = "Updated_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        } else {
            $newElement = $xml.CreateElement("add")
            $newElement.SetAttribute("key", "TestHotReload")
            $newElement.SetAttribute("value", "HotReload_$(Get-Date -Format 'yyyyMMdd_HHmmss')")
            $xml.configuration.appSettings.AppendChild($newElement)
        }

        $xml.Save($configFile)
        Write-Host "配置文件已更新，等待热更新生效..." -ForegroundColor Green

        # 等待文件监控生效
        Start-Sleep -Seconds 2

        Write-Host "热更新测试完成" -ForegroundColor Green

    } finally {
        # 恢复原配置
        Copy-Item $backup $configFile -Force
        Remove-Item $backup
    }
}

# 运行热更新测试
Test-ConfigurationHotReload
```

### 回滚部署

**回滚脚本**:

```powershell
# 停止应用程序
Stop-Process -Name "IndustrialHMI" -Force -ErrorAction SilentlyContinue

# 恢复备份
$backupPath = "D:\Backup\IndustrialHMI_Previous"
$installPath = "C:\IndustrialHMI"

if (Test-Path $backupPath) {
    Remove-Item $installPath -Recurse -Force
    Copy-Item $backupPath $installPath -Recurse -Force
    Write-Host "回滚完成" -ForegroundColor Green
} else {
    Write-Host "备份不存在，无法回滚" -ForegroundColor Red
}
```

---

**版本历史**:

- v1.0.0 (2025-08-13): 初始版本

**注意事项**:

- 部署前请务必备份现有系统
- 在生产环境部署前，请在测试环境充分验证
- 定期检查系统健康状态和性能指标
- 保持部署文档和脚本的版本同步

**相关文档**:

- [用户操作手册](../用户手册/用户操作手册.md)
- [开发者文档](../开发者文档/开发者文档.md)
- [故障排除和维护指南](../维护指南/故障排除和维护指南.md)
