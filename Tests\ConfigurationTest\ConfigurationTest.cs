using System;
using System.IO;
using System.Linq;
using Contracts.Services;
using Services;

namespace ConfigurationTest
{
    /// <summary>
    /// 配置管理系统测试程序
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("配置管理系统测试");
            Console.WriteLine("================");
            Console.WriteLine();

            try
            {
                // 创建配置服务
                var configService = new ConfigurationService();

                // 添加配置源
                Console.WriteLine("1. 添加配置源...");

                // 环境变量配置源（最高优先级）
                configService.AddSource(new EnvironmentConfigurationSource("INDUSTRIAL_HMI", 100, true, "Environment"));
                Console.WriteLine("   ✅ 环境变量配置源已添加");

                // 自定义配置文件（中等优先级）
                var customConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "application.config");
                configService.AddSource(new FileConfigurationSource(customConfigPath, 50, true, "CustomConfig"));
                Console.WriteLine($"   ✅ 自定义配置文件已添加: {customConfigPath}");

                // 默认配置文件（最低优先级）
                var defaultConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "IndustrialHMI.exe.config");
                configService.AddSource(new FileConfigurationSource(defaultConfigPath, 10, false, "DefaultConfig"));
                Console.WriteLine($"   ✅ 默认配置文件已添加: {defaultConfigPath}");

                // 启用热更新
                configService.EnableHotReload();
                Console.WriteLine("   ✅ 热更新已启用");
                Console.WriteLine();

                // 测试配置读取
                Console.WriteLine("2. 测试配置读取...");
                TestConfigurationReading(configService);
                Console.WriteLine();

                // 测试配置写入
                Console.WriteLine("3. 测试配置写入...");
                TestConfigurationWriting(configService);
                Console.WriteLine();

                // 测试配置源优先级
                Console.WriteLine("4. 测试配置源优先级...");
                TestConfigurationPriority(configService);
                Console.WriteLine();

                // 测试配置节读取
                Console.WriteLine("5. 测试配置节读取...");
                TestConfigurationSections(configService);
                Console.WriteLine();

                // 测试热更新（如果用户想要测试）
                Console.WriteLine("6. 测试热更新功能...");
                Console.WriteLine("   请修改配置文件 config/application.config 中的任意值，然后按任意键继续...");
                Console.ReadKey();

                // 重新读取配置，验证热更新
                var updatedValue = configService.GetString("CustomSetting", "未找到");
                Console.WriteLine($"   热更新后的配置值: {updatedValue}");
                Console.WriteLine();

                Console.WriteLine("✅ 所有测试完成！配置管理系统工作正常。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void TestConfigurationReading(IConfigurationService configService)
        {
            // 测试字符串配置
            var stringValue = configService.GetString("CustomSetting", "默认值");
            Console.WriteLine($"   字符串配置 (CustomSetting): {stringValue}");

            // 测试整数配置
            var intValue = configService.GetInt("MaxModules", 5);
            Console.WriteLine($"   整数配置 (MaxModules): {intValue}");

            // 测试布尔配置
            var boolValue = configService.GetBool("EnableAdvancedFeatures", false);
            Console.WriteLine($"   布尔配置 (EnableAdvancedFeatures): {boolValue}");

            // 测试不存在的配置
            var missingValue = configService.GetString("NonExistentKey", "默认值");
            Console.WriteLine($"   不存在的配置 (NonExistentKey): {missingValue}");
        }

        static void TestConfigurationWriting(IConfigurationService configService)
        {
            // 设置新的配置值
            configService.SetValue("TestKey", "测试值");
            Console.WriteLine("   ✅ 设置配置: TestKey = 测试值");

            // 读取刚设置的配置
            var readValue = configService.GetString("TestKey", "未找到");
            Console.WriteLine($"   读取配置: TestKey = {readValue}");

            // 更新现有配置
            configService.SetValue("MaxModules", 20);
            Console.WriteLine("   ✅ 更新配置: MaxModules = 20");

            var updatedValue = configService.GetInt("MaxModules", 0);
            Console.WriteLine($"   读取更新后的配置: MaxModules = {updatedValue}");
        }

        static void TestConfigurationPriority(IConfigurationService configService)
        {
            // 显示配置源信息
            var sources = configService.GetSources();
            Console.WriteLine("   配置源列表（按优先级排序）:");
            foreach (var source in sources.OrderByDescending(s => s.Priority))
            {
                Console.WriteLine($"     - {source.Name} (优先级: {source.Priority}, 可写: {source.CanWrite}, 热更新: {source.SupportsHotReload})");
            }

            // 测试环境变量优先级（如果设置了的话）
            Environment.SetEnvironmentVariable("INDUSTRIAL_HMI_TestPriority", "环境变量值", EnvironmentVariableTarget.Process);

            // 重新加载配置以获取环境变量
            configService.Reload();

            var priorityValue = configService.GetString("TestPriority", "未找到");
            Console.WriteLine($"   优先级测试 (TestPriority): {priorityValue}");
        }

        static void TestConfigurationSections(IConfigurationService configService)
        {
            // 获取性能配置节
            var performanceSection = configService.GetSection("performance");
            Console.WriteLine("   性能配置节:");
            foreach (var kvp in performanceSection)
            {
                Console.WriteLine($"     {kvp.Key} = {kvp.Value}");
            }

            // 获取模块配置节
            var modulesSection = configService.GetSection("modules");
            Console.WriteLine("   模块配置节:");
            foreach (var kvp in modulesSection)
            {
                Console.WriteLine($"     {kvp.Key} = {kvp.Value}");
            }

            // 获取所有配置键
            var allKeys = configService.GetAllKeys();
            Console.WriteLine($"   总配置项数量: {allKeys.Count()}");
        }
    }
}
