using System;

namespace Contracts
{
    /// <summary>
    /// MVP模式中的表示器接口
    /// </summary>
    /// <remarks>
    /// Presenter负责处理业务逻辑，协调View和Model之间的交互，
    /// 不直接操作UI控件，通过View接口与UI交互
    /// </remarks>
    public interface IPresenter : IDisposable
    {
        /// <summary>
        /// 关联的视图
        /// </summary>
        /// <remarks>由依赖注入容器设置</remarks>
        IView View { get; set; }

        /// <summary>
        /// 初始化Presenter
        /// </summary>
        /// <remarks>
        /// 在此方法中进行：
        /// 1. 验证View是否已设置
        /// 2. 订阅View的事件
        /// 3. 初始化业务逻辑
        /// 4. 加载初始数据
        /// </remarks>
        void Initialize();

        /// <summary>
        /// 加载数据
        /// </summary>
        /// <remarks>
        /// 从数据源加载数据并更新View显示
        /// </remarks>
        void LoadData();

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <returns>保存是否成功</returns>
        /// <remarks>
        /// 将View中的数据保存到数据源
        /// </remarks>
        bool SaveData();

        /// <summary>
        /// 验证数据
        /// </summary>
        /// <returns>验证是否通过</returns>
        /// <remarks>
        /// 验证View中的数据是否符合业务规则
        /// </remarks>
        bool ValidateData();
    }

    /// <summary>
    /// 泛型Presenter接口，用于强类型的View绑定
    /// </summary>
    /// <typeparam name="TView">关联的View类型</typeparam>
    public interface IPresenter<TView> : IPresenter where TView : IView
    {
        /// <summary>
        /// 强类型的关联视图
        /// </summary>
        /// <remarks>提供类型安全的View访问</remarks>
        new TView View { get; set; }
    }
}
