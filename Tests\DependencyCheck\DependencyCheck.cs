using System;
using System.IO;
using System.Reflection;

namespace DependencyCheck
{
    /// <summary>
    /// 依赖检查程序
    /// </summary>
    /// <remarks>
    /// 用于检查工业HMI框架的依赖问题
    /// </remarks>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("工业HMI框架依赖检查");
            Console.WriteLine("==================");
            Console.WriteLine();

            try
            {
                // 检查主程序目录
                var mainDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
                Console.WriteLine($"主程序目录: {mainDir}");
                Console.WriteLine($"目录存在: {Directory.Exists(mainDir)}");
                Console.WriteLine();

                if (!Directory.Exists(mainDir))
                {
                    Console.WriteLine("❌ 主程序目录不存在");
                    return;
                }

                // 检查主程序文件
                var exePath = Path.Combine(mainDir, "IndustrialHMI.exe");
                Console.WriteLine($"主程序路径: {exePath}");
                Console.WriteLine($"文件存在: {File.Exists(exePath)}");
                Console.WriteLine();

                if (!File.Exists(exePath))
                {
                    Console.WriteLine("❌ 主程序文件不存在");
                    return;
                }

                // 检查依赖文件
                Console.WriteLine("=== 依赖文件检查 ===");
                var dependencies = new[]
                {
                    "Contracts.dll",
                    "Services.dll", 
                    "Serilog.dll",
                    "Serilog.Sinks.Console.dll",
                    "Serilog.Sinks.File.dll",
                    "System.Diagnostics.DiagnosticSource.dll"
                };

                foreach (var dep in dependencies)
                {
                    var depPath = Path.Combine(mainDir, dep);
                    var exists = File.Exists(depPath);
                    Console.WriteLine($"{dep}: {(exists ? "✅" : "❌")}");
                    
                    if (exists)
                    {
                        try
                        {
                            var assembly = Assembly.LoadFrom(depPath);
                            Console.WriteLine($"  版本: {assembly.GetName().Version}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"  ❌ 加载失败: {ex.Message}");
                        }
                    }
                }
                Console.WriteLine();

                // 尝试加载主程序集
                Console.WriteLine("=== 主程序集检查 ===");
                try
                {
                    var mainAssembly = Assembly.LoadFrom(exePath);
                    Console.WriteLine($"✅ 主程序集加载成功: {mainAssembly.FullName}");
                    
                    // 检查引用的程序集
                    var referencedAssemblies = mainAssembly.GetReferencedAssemblies();
                    Console.WriteLine($"引用的程序集数量: {referencedAssemblies.Length}");
                    
                    foreach (var refAssembly in referencedAssemblies)
                    {
                        Console.WriteLine($"  - {refAssembly.Name} v{refAssembly.Version}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 主程序集加载失败: {ex.Message}");
                    Console.WriteLine($"详细信息: {ex}");
                }
                Console.WriteLine();

                // 尝试加载Serilog
                Console.WriteLine("=== Serilog检查 ===");
                try
                {
                    var serilogPath = Path.Combine(mainDir, "Serilog.dll");
                    if (File.Exists(serilogPath))
                    {
                        var serilogAssembly = Assembly.LoadFrom(serilogPath);
                        Console.WriteLine($"✅ Serilog加载成功: {serilogAssembly.FullName}");
                        
                        // 检查Serilog的依赖
                        var serilogRefs = serilogAssembly.GetReferencedAssemblies();
                        foreach (var refAssembly in serilogRefs)
                        {
                            if (refAssembly.Name.Contains("DiagnosticSource"))
                            {
                                Console.WriteLine($"  需要依赖: {refAssembly.Name} v{refAssembly.Version}");
                                
                                var depPath = Path.Combine(mainDir, refAssembly.Name + ".dll");
                                Console.WriteLine($"  依赖文件存在: {File.Exists(depPath)}");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ Serilog.dll不存在");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Serilog检查失败: {ex.Message}");
                }
                Console.WriteLine();

                // 检查模块目录
                Console.WriteLine("=== 模块目录检查 ===");
                var modulesDir = Path.Combine(mainDir, "Modules");
                Console.WriteLine($"模块目录: {modulesDir}");
                Console.WriteLine($"目录存在: {Directory.Exists(modulesDir)}");
                
                if (Directory.Exists(modulesDir))
                {
                    var moduleFiles = Directory.GetFiles(modulesDir, "*.dll");
                    Console.WriteLine($"模块文件数量: {moduleFiles.Length}");
                    foreach (var moduleFile in moduleFiles)
                    {
                        Console.WriteLine($"  - {Path.GetFileName(moduleFile)}");
                    }
                }
                Console.WriteLine();

                Console.WriteLine("依赖检查完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检查失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
