using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using Contracts.Services;

namespace Services
{
    /// <summary>
    /// 文件配置源实现
    /// </summary>
    /// <remarks>
    /// 基于XML文件的配置源，支持热更新监控
    /// </remarks>
    public class FileConfigurationSource : IConfigurationSource
    {
        private readonly string _filePath;
        private readonly bool _canWrite;
        private FileSystemWatcher _watcher;
        private bool _isWatching;
        private bool _disposed;

        /// <summary>
        /// 配置源名称
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; }

        /// <summary>
        /// 是否支持写入
        /// </summary>
        public bool CanWrite => _canWrite;

        /// <summary>
        /// 是否支持热更新
        /// </summary>
        public bool SupportsHotReload => true;

        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> Changed;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <param name="priority">优先级</param>
        /// <param name="canWrite">是否支持写入</param>
        /// <param name="name">配置源名称</param>
        public FileConfigurationSource(string filePath, int priority = 0, bool canWrite = true, string name = null)
        {
            _filePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
            Priority = priority;
            _canWrite = canWrite;
            Name = name ?? $"File:{Path.GetFileName(filePath)}";
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <returns>配置字典</returns>
        public Dictionary<string, object> Load()
        {
            var configurations = new Dictionary<string, object>();

            if (!File.Exists(_filePath))
            {
                return configurations;
            }

            try
            {
                var doc = new XmlDocument();
                doc.Load(_filePath);

                // 读取appSettings节点
                var appSettingsNode = doc.SelectSingleNode("//appSettings");
                if (appSettingsNode != null)
                {
                    foreach (XmlNode node in appSettingsNode.ChildNodes)
                    {
                        if (node.Name == "add" && node.Attributes != null)
                        {
                            var keyAttr = node.Attributes["key"];
                            var valueAttr = node.Attributes["value"];
                            
                            if (keyAttr != null && valueAttr != null)
                            {
                                configurations[keyAttr.Value] = valueAttr.Value;
                            }
                        }
                    }
                }

                // 读取自定义配置节点
                var configNode = doc.SelectSingleNode("//configuration");
                if (configNode != null)
                {
                    foreach (XmlNode section in configNode.ChildNodes)
                    {
                        if (section.Name != "appSettings" && section.NodeType == XmlNodeType.Element)
                        {
                            LoadSection(section, configurations, section.Name);
                        }
                    }
                }
            }
            catch (Exception)
            {
                // 文件读取失败，返回空配置
            }

            return configurations;
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="configurations">配置字典</param>
        public void Save(Dictionary<string, object> configurations)
        {
            if (!_canWrite || configurations == null)
                return;

            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(_filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var doc = new XmlDocument();
                
                // 如果文件存在，加载现有内容
                if (File.Exists(_filePath))
                {
                    doc.Load(_filePath);
                }
                else
                {
                    // 创建基本结构
                    doc.LoadXml("<?xml version=\"1.0\" encoding=\"utf-8\"?><configuration><appSettings></appSettings></configuration>");
                }

                var appSettingsNode = doc.SelectSingleNode("//appSettings");
                if (appSettingsNode == null)
                {
                    var configNode = doc.SelectSingleNode("//configuration");
                    if (configNode == null)
                    {
                        configNode = doc.CreateElement("configuration");
                        doc.AppendChild(configNode);
                    }
                    appSettingsNode = doc.CreateElement("appSettings");
                    configNode.AppendChild(appSettingsNode);
                }

                // 清空现有的appSettings
                appSettingsNode.RemoveAll();

                // 添加新的配置项
                foreach (var kvp in configurations)
                {
                    var addElement = doc.CreateElement("add");
                    addElement.SetAttribute("key", kvp.Key);
                    addElement.SetAttribute("value", kvp.Value?.ToString() ?? "");
                    appSettingsNode.AppendChild(addElement);
                }

                // 保存文件
                doc.Save(_filePath);
            }
            catch (Exception)
            {
                // 保存失败，忽略错误
            }
        }

        /// <summary>
        /// 开始监控配置变更
        /// </summary>
        public void StartWatching()
        {
            if (_isWatching || _disposed)
                return;

            try
            {
                var directory = Path.GetDirectoryName(_filePath);
                var fileName = Path.GetFileName(_filePath);

                if (string.IsNullOrEmpty(directory) || string.IsNullOrEmpty(fileName))
                    return;

                _watcher = new FileSystemWatcher(directory, fileName)
                {
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                    EnableRaisingEvents = true
                };

                _watcher.Changed += OnFileChanged;
                _isWatching = true;
            }
            catch (Exception)
            {
                // 监控启动失败，忽略错误
            }
        }

        /// <summary>
        /// 停止监控配置变更
        /// </summary>
        public void StopWatching()
        {
            if (!_isWatching)
                return;

            try
            {
                if (_watcher != null)
                {
                    _watcher.Changed -= OnFileChanged;
                    _watcher.EnableRaisingEvents = false;
                    _watcher.Dispose();
                    _watcher = null;
                }
                _isWatching = false;
            }
            catch (Exception)
            {
                // 停止监控失败，忽略错误
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            StopWatching();
            _disposed = true;
        }

        /// <summary>
        /// 文件变更事件处理
        /// </summary>
        private void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 延迟一下，避免文件正在写入时读取
                System.Threading.Thread.Sleep(100);
                
                Changed?.Invoke(this, new ConfigurationChangedEventArgs("*", null, null, Name));
            }
            catch (Exception)
            {
                // 忽略错误
            }
        }

        /// <summary>
        /// 递归加载配置节
        /// </summary>
        private void LoadSection(XmlNode node, Dictionary<string, object> configurations, string prefix)
        {
            if (node.Attributes != null)
            {
                foreach (XmlAttribute attr in node.Attributes)
                {
                    var key = string.IsNullOrEmpty(prefix) ? attr.Name : $"{prefix}.{attr.Name}";
                    configurations[key] = attr.Value;
                }
            }

            if (node.HasChildNodes)
            {
                foreach (XmlNode child in node.ChildNodes)
                {
                    if (child.NodeType == XmlNodeType.Element)
                    {
                        var key = string.IsNullOrEmpty(prefix) ? child.Name : $"{prefix}.{child.Name}";
                        
                        if (child.HasChildNodes && child.FirstChild.NodeType == XmlNodeType.Text)
                        {
                            configurations[key] = child.InnerText;
                        }
                        else
                        {
                            LoadSection(child, configurations, key);
                        }
                    }
                }
            }
        }
    }
}
