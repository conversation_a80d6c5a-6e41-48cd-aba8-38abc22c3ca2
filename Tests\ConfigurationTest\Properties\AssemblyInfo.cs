using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("ConfigurationTest")]
[assembly: AssemblyDescription("配置管理系统测试程序")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Industrial HMI Framework")]
[assembly: AssemblyCopyright("Copyright ©  2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]
[assembly: Guid("c1d2e3f4-a5b6-7890-1234-567890abcdef")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
