using System;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using TestFrameworkModule.Views;
using TestFrameworkModule.Presenters;
using TestFrameworkModule.Models;
using TestFrameworkModule.Tests;

namespace TestFrameworkModule
{
    /// <summary>
    /// 测试框架模块主类
    /// </summary>
    /// <remarks>
    /// 提供系统集成测试、性能测试和内存泄漏检测功能
    /// </remarks>
    public class TestFrameworkModuleMain : IModule
    {
        #region 依赖注入属性

        /// <summary>
        /// 事件聚合器
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public ILogger Logger { get; set; }

        /// <summary>
        /// 配置服务
        /// </summary>
        /// <remarks>由ModuleLoader注入，可选</remarks>
        public IConfigurationService ConfigurationService { get; set; }

        #endregion

        #region 私有字段

        private TestFrameworkView _view;
        private TestFrameworkPresenter _presenter;
        private TestFrameworkModel _model;
        private IntegrationTestSuite _integrationTests;
        private PerformanceTestSuite _performanceTests;
        private MemoryLeakTestSuite _memoryLeakTests;
        private bool _disposed = false;

        #endregion

        #region IModule 实现

        /// <summary>
        /// 模块名称
        /// </summary>
        public string Name => "测试框架模块";

        /// <summary>
        /// 模块版本
        /// </summary>
        public string Version => "1.0.0";

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description => "提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块";

        /// <summary>
        /// 初始化模块
        /// </summary>
        public void Initialize()
        {
            try
            {
                Logger?.Info($"开始初始化{Name}");

                // 验证依赖注入
                ValidateDependencies();

                // 创建模型
                _model = new TestFrameworkModel();

                // 创建视图
                _view = new TestFrameworkView();

                // 创建表示器
                _presenter = new TestFrameworkPresenter(_view, _model, EventAggregator, Logger);

                // 创建测试套件
                _integrationTests = new IntegrationTestSuite(EventAggregator, Logger);
                _performanceTests = new PerformanceTestSuite(EventAggregator, Logger);
                _memoryLeakTests = new MemoryLeakTestSuite(EventAggregator, Logger);

                // 初始化表示器
                _presenter.Initialize();

                // 订阅系统事件
                SubscribeToEvents();

                Logger?.Info($"{Name}初始化完成");
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 启动模块
        /// </summary>
        public void Start()
        {
            try
            {
                Logger?.Info($"启动{Name}");

                // 启动模型
                _model?.Start();

                // 启动表示器
                _presenter?.LoadData();

                // 启动测试套件
                _integrationTests?.Initialize();
                _performanceTests?.Initialize();
                _memoryLeakTests?.Initialize();

                Logger?.Info($"{Name}启动完成");
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}启动失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止模块
        /// </summary>
        public void Stop()
        {
            try
            {
                Logger?.Info($"停止{Name}");

                // 停止测试套件
                _memoryLeakTests?.Stop();
                _performanceTests?.Stop();
                _integrationTests?.Stop();

                // 停止模型
                _model?.Stop();

                Logger?.Info($"{Name}停止完成");
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}停止失败", ex);
            }
        }

        /// <summary>
        /// 获取模块的用户控件
        /// </summary>
        /// <returns>模块的用户控件</returns>
        public UserControl GetUserControl()
        {
            return _view;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证依赖注入
        /// </summary>
        private void ValidateDependencies()
        {
            if (EventAggregator == null)
                throw new InvalidOperationException("EventAggregator未注入");

            if (Logger == null)
                throw new InvalidOperationException("Logger未注入");

            // ConfigurationService是可选的
            if (ConfigurationService != null)
                Logger.Debug("ConfigurationService注入成功");
            else
                Logger.Debug("ConfigurationService未注入（可选）");
        }

        /// <summary>
        /// 订阅系统事件
        /// </summary>
        private void SubscribeToEvents()
        {
            try
            {
                // 订阅系统启动事件
                EventAggregator.GetEvent<SystemStartupEvent>()
                    .Subscribe(OnSystemStartup, keepSubscriberReferenceAlive: false);

                // 订阅系统关闭事件
                EventAggregator.GetEvent<SystemShutdownEvent>()
                    .Subscribe(OnSystemShutdown, keepSubscriberReferenceAlive: false);

                // 订阅模块加载事件
                EventAggregator.GetEvent<ModuleLoadedEvent>()
                    .Subscribe(OnModuleLoaded, keepSubscriberReferenceAlive: false);

                // 订阅模块卸载事件
                EventAggregator.GetEvent<ModuleUnloadedEvent>()
                    .Subscribe(OnModuleUnloaded, keepSubscriberReferenceAlive: false);

                Logger?.Debug($"{Name}事件订阅完成");
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}事件订阅失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 处理系统启动事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemStartup(SystemStartupEvent eventData)
        {
            try
            {
                Logger?.Info($"{Name}收到系统启动事件");
                _model?.OnSystemEvent("SystemStartup", eventData.Version ?? "Unknown");
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}处理系统启动事件失败", ex);
            }
        }

        /// <summary>
        /// 处理系统关闭事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemShutdown(SystemShutdownEvent eventData)
        {
            try
            {
                Logger?.Info($"{Name}收到系统关闭事件，原因: {eventData.Reason}");
                _model?.OnSystemEvent("SystemShutdown", eventData.Reason.ToString());
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}处理系统关闭事件失败", ex);
            }
        }

        /// <summary>
        /// 处理模块加载事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleLoaded(ModuleLoadedEvent eventData)
        {
            try
            {
                Logger?.Info($"{Name}收到模块加载事件: {eventData.ModuleName}");
                _model?.OnModuleEvent("ModuleLoaded", eventData.ModuleName);
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}处理模块加载事件失败", ex);
            }
        }

        /// <summary>
        /// 处理模块卸载事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleUnloaded(ModuleUnloadedEvent eventData)
        {
            try
            {
                Logger?.Info($"{Name}收到模块卸载事件: {eventData.ModuleName}");
                _model?.OnModuleEvent("ModuleUnloaded", eventData.ModuleName);
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}处理模块卸载事件失败", ex);
            }
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Logger?.Info($"开始释放{Name}资源");

                // 取消事件订阅
                if (EventAggregator != null)
                {
                    EventAggregator.GetEvent<SystemStartupEvent>().Unsubscribe(OnSystemStartup);
                    EventAggregator.GetEvent<SystemShutdownEvent>().Unsubscribe(OnSystemShutdown);
                    EventAggregator.GetEvent<ModuleLoadedEvent>().Unsubscribe(OnModuleLoaded);
                    EventAggregator.GetEvent<ModuleUnloadedEvent>().Unsubscribe(OnModuleUnloaded);
                }

                // 释放测试套件
                _memoryLeakTests?.Dispose();
                _performanceTests?.Dispose();
                _integrationTests?.Dispose();

                // 释放表示器
                _presenter?.Dispose();

                // 释放模型
                _model?.Dispose();

                // 释放视图
                _view?.Dispose();

                _disposed = true;
                Logger?.Info($"{Name}资源释放完成");
            }
            catch (Exception ex)
            {
                Logger?.Error($"{Name}资源释放失败", ex);
            }
        }

        #endregion
    }
}
