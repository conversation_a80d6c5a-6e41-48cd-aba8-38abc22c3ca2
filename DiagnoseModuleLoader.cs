using System;
using System.IO;
using System.Linq;
using System.Reflection;

namespace DiagnoseModuleLoader
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== 模块加载器诊断程序 ===");
                Console.WriteLine();

                var modulesPath = @"bin\Debug\Modules";
                Console.WriteLine($"检查模块目录: {Path.GetFullPath(modulesPath)}");
                
                if (!Directory.Exists(modulesPath))
                {
                    Console.WriteLine("❌ 模块目录不存在！");
                    Console.ReadKey();
                    return;
                }

                var dllFiles = Directory.GetFiles(modulesPath, "*.dll");
                Console.WriteLine($"找到 {dllFiles.Length} 个DLL文件:");
                foreach (var file in dllFiles)
                {
                    Console.WriteLine($"  - {Path.GetFileName(file)}");
                }
                Console.WriteLine();

                // 检查每个DLL文件
                foreach (var dllFile in dllFiles)
                {
                    Console.WriteLine($"=== 分析 {Path.GetFileName(dllFile)} ===");
                    
                    try
                    {
                        var assembly = Assembly.LoadFrom(dllFile);
                        Console.WriteLine($"✅ 程序集加载成功: {assembly.FullName}");
                        
                        var types = assembly.GetTypes();
                        Console.WriteLine($"程序集包含 {types.Length} 个类型");
                        
                        // 检查每个类型
                        foreach (var type in types.Where(t => !t.IsInterface && !t.IsAbstract))
                        {
                            Console.WriteLine($"\n类型: {type.FullName}");
                            
                            var interfaces = type.GetInterfaces();
                            Console.WriteLine($"实现的接口数量: {interfaces.Length}");
                            
                            foreach (var iface in interfaces)
                            {
                                Console.WriteLine($"  - {iface.FullName} (来自: {iface.Assembly.FullName})");
                                
                                // 检查是否是IModule接口
                                if (iface.Name == "IModule")
                                {
                                    Console.WriteLine($"    ✅ 找到IModule接口！");
                                    
                                    // 尝试创建实例
                                    try
                                    {
                                        var instance = Activator.CreateInstance(type);
                                        Console.WriteLine($"    ✅ 实例创建成功");
                                        
                                        // 尝试获取Name属性
                                        var nameProperty = type.GetProperty("Name");
                                        if (nameProperty != null)
                                        {
                                            var name = nameProperty.GetValue(instance);
                                            Console.WriteLine($"    模块名称: {name}");
                                        }
                                        
                                        // 尝试获取Description属性
                                        var descProperty = type.GetProperty("Description");
                                        if (descProperty != null)
                                        {
                                            var desc = descProperty.GetValue(instance);
                                            Console.WriteLine($"    模块描述: {desc}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"    ❌ 实例创建失败: {ex.Message}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 程序集加载失败: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                        }
                    }
                    
                    Console.WriteLine();
                }

                Console.WriteLine("=== 诊断完成 ===");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序异常: {ex}");
                Console.ReadKey();
            }
        }
    }
}
