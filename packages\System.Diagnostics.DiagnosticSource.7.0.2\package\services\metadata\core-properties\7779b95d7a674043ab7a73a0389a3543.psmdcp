<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides Classes that allow you to decouple code logging rich (unserializable) diagnostics/telemetry (e.g. framework) from code that consumes it (e.g. tools)

Commonly Used Types:
System.Diagnostics.DiagnosticListener
System.Diagnostics.DiagnosticSource</dc:description>
  <dc:identifier>System.Diagnostics.DiagnosticSource</dc:identifier>
  <version>7.0.2</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Build.Tasks.Pack, Version=*********, Culture=neutral, PublicKeyToken=31bf3856ad364e35;</lastModifiedBy>
</coreProperties>