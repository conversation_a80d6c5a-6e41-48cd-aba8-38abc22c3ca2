# 焊接机器人视觉识别定位软件需求文档

## 一、项目概述

本软件旨在基于 Halcon 机器视觉库与 C# 开发环境，实现焊接机器人的视觉识别与定位功能。通过视觉标定建立像素坐标与机器人物理坐标的映射关系，创建可自定义定位点的产品模板，最终实现对产品焊接定位点的精准识别与坐标输出，为焊接机器人提供精准的位置参考，现阶段以 UI 显示定位结果为核心目标，预留后期机器人通讯扩展能力。

## 二、核心功能需求

### （一）视觉标定功能



1.  **标定板参数定义**：标定板包含 3 个实心圆形标记点，分别定义为 A、B、C，三点组成直角三角形，**B 为直角顶点**，其中 A 与 B 的圆心距为 120mm，B 与 C 的圆心距为 180mm。

2.  **图像采集与圆心检测**：软件需支持采集标定板图像，通过 Halcon 视觉算法（如`find_circles`系列算子）自动检测 A、B、C 三点的像素坐标，检测结果需在 UI 界面实时显示，允许用户手动微调圆心像素坐标（如通过鼠标点击修正）。

3.  **机器人坐标输入**：现阶段支持手动输入 A、B、C 三点对应的机器人物理坐标（X、Y、Z 轴），输入界面需包含清晰的点标记（A/B/C）与坐标输入框，支持数值校验（如非负数、合理范围提示）。

4.  **坐标映射模型建立**：基于 A、B、C 三点的像素坐标与机器人物理坐标，通过 Halcon 标定函数建立映射模型（如透视变换或多项式拟合模型），并在 UI 显示标定误差（如平均像素偏差）。

### （二）模板创建功能



1.  **轮廓提取**：支持导入产品图像或实时采集图像，通过 Halcon 边缘检测算法（如`edges_sub_pix`）提取产品轮廓，轮廓提取结果需在 UI 高亮显示，允许用户调整轮廓提取参数（如阈值、平滑系数）。

2.  **定位点管理**：基于提取的轮廓，支持用户通过鼠标交互添加定位点（点击轮廓上的目标位置），每个定位点需记录序号（按添加顺序）、像素坐标；支持定位点删除（选中定位点后点击删除按钮）与坐标调整（拖动定位点或手动输入像素坐标修正）。

3.  **产品高度设置**：在模板创建过程中，提供产品高度输入框，用户输入的产品高度值作为预设值保存到模板中。

4.  **模板保存与加载**：创建完成的模板（含轮廓特征、定位点信息、产品高度预设值）需支持以自定义格式（如.xml 或二进制文件）保存，保存内容需包含模板名称、轮廓数据、定位点序号及像素坐标、产品高度预设值；支持模板加载功能，加载后需在 UI 还原轮廓、定位点显示及产品高度预设值。

### （三）视觉识别定位功能



1.  **图像采集触发**：支持手动触发或外部信号触发（预留接口）采集待识别产品图像，采集图像需在 UI 实时显示。

2.  **模板匹配参数设置**：UI 界面需提供模板轮廓识别精度设置区域，包括最大匹配偏移量（可分别设置像素和毫米单位）、最小匹配得分阈值（0-100 分）的输入框或滑动条，参数设置后实时应用于模板匹配过程。

3.  **模板匹配**：基于选择的模板和设置的匹配参数，通过 Halcon 模板匹配算法（如`find_scaled_shape_model`）在采集图像中匹配产品轮廓，匹配结果需在 UI 框选产品位置，并显示匹配得分（如 0-100 分），当匹配得分低于设置的最小阈值时提示匹配失败。

4.  **坐标转换与输出**：匹配成功后，根据模板中定位点的相对位置与匹配到的产品轮廓位置，计算各定位点的实际像素坐标；基于视觉标定阶段建立的映射模型，将像素坐标转换为机器人物理坐标（X、Y 轴）；Z 轴物理坐标按 “平台标定 Z 值 + 产品高度” 计算（产品高度为模板中保存的预设值或测试后更新的值）。

5.  **结果显示**：在 UI 界面按定位点添加顺序，列表显示各定位点的序号、像素坐标（X\_pix、Y\_pix）、物理坐标（X\_mm、Y\_mm、Z\_mm），支持结果截图或导出为 Excel 文件。

6.  **模板测试与 Z 轴高度更新**：在模板测试界面，支持通过操作将机器人移动到焊接点 XY 坐标的预设高度正上方（比预设高度高出 10mm 处），通过下降 Z 轴获取精确的 Z 轴高度后，可将该精确值更新并保存到模板中。若加载的模板未进行测试，UI 界面需显示红色警告文字 “模板未测试，请测试”。

## 三、注意事项



1.  **Z 轴坐标计算逻辑**：明确 Z 轴物理坐标计算公式为 “Z\_物理 = Z\_平台标定 + H\_产品高度”，其中 Z\_平台标定为视觉标定时记录的平台 Z 轴坐标，H\_产品高度为模板中保存的预设值或测试后更新的精确值（需在 UI 清晰显示产品高度的来源状态）。

2.  **机器人坐标输入方式**：现阶段采用手动输入机器人坐标（用于标定），软件需提供独立的坐标输入模块，输入框需区分 A、B、C 三点及 X、Y、Z 轴；后期需预留机器人通讯接口（如 TCP/IP、Modbus），接口设计需支持读取机器人实时坐标（替换手动输入功能）。

3.  **定位结果输出方式**：当前版本无需向机器人发送定位结果，仅需在 UI 界面显示定位点坐标列表；后期扩展时需预留通讯输出接口，接口需支持按定位点顺序发送物理坐标数据（格式可自定义，如 “序号，X,Y,Z”）。

## 四、后续扩展规划



1.  **机器人通讯模块**：预留与主流工业机器人（如 KUKA、ABB、Fanuc）的通讯接口，支持通过标准协议（如 Profinet、EtherCAT）读取机器人坐标（用于标定）与发送定位点坐标（用于焊接）。

2.  **自动化触发功能**：支持与生产线传感器联动，实现图像采集、识别定位的全自动触发（如通过光电传感器检测产品到位信号）。

3.  **误差分析功能**：添加定位误差统计模块，记录多次识别的定位点坐标偏差，生成误差趋势图表（如 X/Y 轴偏差分布），辅助优化标定与匹配参数。

## 五、补充事项



1.  建议在视觉标定功能中添加 “标定模型验证” 模块，支持用户采集标定板任意位置的图像，检测圆心像素坐标并通过映射模型计算物理坐标，与实际测量值对比显示验证误差，确保标定准确性。

2.  建议模板创建时保存定位点的 “相对轮廓坐标”（而非绝对像素坐标），确保模板在不同分辨率图像中匹配时，定位点坐标可自适应转换。

3.  建议 UI 界面添加 “日志记录” 区域，实时记录标定、模板创建、识别定位的关键操作与结果（如时间、操作类型、误差值），便于后期问题排查。

4.  建议在模板管理界面显示各模板的测试状态（已测试 / 未测试），方便用户快速识别需要测试的模板。

> （注：文档部分内容可能由 AI 生成）