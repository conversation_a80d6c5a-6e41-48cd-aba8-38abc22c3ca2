using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Contracts;
using Contracts.Events;
using Contracts.Services;

namespace DeviceModule.Models
{
    /// <summary>
    /// 设备模块数据模型
    /// </summary>
    /// <remarks>
    /// 管理设备数据、状态和业务逻辑，处理设备相关的事件
    /// </remarks>
    public class DeviceModel : IDisposable
    {
        private readonly IDeviceService _deviceService;
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private List<DeviceViewModel> _devices;
        private bool _disposed = false;

        /// <summary>
        /// 设备列表变化事件
        /// </summary>
        public event EventHandler<DeviceListChangedEventArgs> DeviceListChanged;

        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        public event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceService">设备服务</param>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public DeviceModel(IDeviceService deviceService, IEventAggregator eventAggregator, ILogger logger)
        {
            _deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _devices = new List<DeviceViewModel>();
            
            // 订阅系统事件
            SubscribeToEvents();
            
            _logger.Debug("DeviceModel 初始化完成");
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            _eventAggregator.GetEvent<SystemStartupEvent>()
                .Subscribe(OnSystemStartup, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<DeviceConnectionEvent>()
                .Subscribe(OnDeviceConnectionChanged, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<DeviceDataUpdateEvent>()
                .Subscribe(OnDeviceDataUpdate, ThreadOption.UIThread, false);
        }

        /// <summary>
        /// 系统启动事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemStartup(SystemStartupEvent eventData)
        {
            _logger.Info("收到系统启动事件，开始加载设备列表");
            LoadDeviceList();
        }

        /// <summary>
        /// 设备连接状态变化事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceConnectionChanged(DeviceConnectionEvent eventData)
        {
            var status = eventData.Status;
            _logger.Debug($"设备连接状态变化: {status.DeviceId} - {(status.IsConnected ? "已连接" : "已断开")}");
            
            var device = _devices.FirstOrDefault(d => d.DeviceId == status.DeviceId);
            if (device != null)
            {
                device.Status = status.IsConnected ? "已连接" : "已断开";
                device.LastUpdateTime = status.Timestamp;
                
                // 触发设备状态变化事件
                DeviceStatusChanged?.Invoke(this, new DeviceStatusChangedEventArgs(device));
            }
        }

        /// <summary>
        /// 设备数据更新事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceDataUpdate(DeviceDataUpdateEvent eventData)
        {
            var dataInfo = eventData.DataInfo;
            var device = _devices.FirstOrDefault(d => d.DeviceId == dataInfo.DeviceId);
            if (device != null)
            {
                device.CurrentValue = dataInfo.Value?.ToString() ?? "无数据";
                device.LastUpdateTime = dataInfo.Timestamp;
                
                // 触发设备状态变化事件
                DeviceStatusChanged?.Invoke(this, new DeviceStatusChangedEventArgs(device));
            }
        }

        /// <summary>
        /// 加载设备列表
        /// </summary>
        public void LoadDeviceList()
        {
            try
            {
                _logger.Info("开始加载设备列表");
                
                var deviceInfos = _deviceService.GetDevices();
                var newDevices = deviceInfos.Select(info => new DeviceViewModel
                {
                    DeviceId = info.Id,
                    Name = info.Name,
                    Type = info.Type,
                    Address = info.Address,
                    Status = info.IsConnected ? "已连接" : "未连接",
                    CurrentValue = info.CurrentValue ?? "无数据",
                    LastUpdateTime = info.LastUpdateTime
                }).ToList();

                _devices.Clear();
                _devices.AddRange(newDevices);
                
                _logger.Info($"设备列表加载完成，共 {_devices.Count} 个设备");
                
                // 触发设备列表变化事件
                DeviceListChanged?.Invoke(this, new DeviceListChangedEventArgs(_devices));
            }
            catch (Exception ex)
            {
                _logger.Error("加载设备列表时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 连接设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        public void ConnectDevice(string deviceId)
        {
            try
            {
                _logger.Info($"开始连接设备: {deviceId}");
                
                var success = _deviceService.ConnectDevice(deviceId);
                if (success)
                {
                    _logger.Info($"设备连接请求已发送: {deviceId}");
                    
                    // 发布设备连接事件
                    var device = _devices.FirstOrDefault(d => d.DeviceId == deviceId);
                    if (device != null)
                    {
                        var status = new DeviceConnectionStatus(deviceId, device.Name, true);
                        _eventAggregator.GetEvent<DeviceConnectionEvent>()
                            .Publish(new DeviceConnectionEvent(status));
                    }
                }
                else
                {
                    _logger.Warning($"设备连接请求失败: {deviceId}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"连接设备时发生错误: {deviceId}", ex);
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        public void DisconnectDevice(string deviceId)
        {
            try
            {
                _logger.Info($"开始断开设备: {deviceId}");
                
                var success = _deviceService.DisconnectDevice(deviceId);
                if (success)
                {
                    _logger.Info($"设备已断开: {deviceId}");
                    
                    // 发布设备断开事件
                    var device = _devices.FirstOrDefault(d => d.DeviceId == deviceId);
                    if (device != null)
                    {
                        var status = new DeviceConnectionStatus(deviceId, device.Name, false);
                        _eventAggregator.GetEvent<DeviceConnectionEvent>()
                            .Publish(new DeviceConnectionEvent(status));
                    }
                }
                else
                {
                    _logger.Warning($"设备断开失败: {deviceId}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"断开设备时发生错误: {deviceId}", ex);
            }
        }

        /// <summary>
        /// 连接所有设备
        /// </summary>
        public void ConnectAllDevices()
        {
            try
            {
                _logger.Info("开始连接所有设备");
                _deviceService.ConnectAllDevices();
            }
            catch (Exception ex)
            {
                _logger.Error("连接所有设备时发生错误", ex);
            }
        }

        /// <summary>
        /// 断开所有设备
        /// </summary>
        public void DisconnectAllDevices()
        {
            try
            {
                _logger.Info("开始断开所有设备");
                _deviceService.DisconnectAllDevices();
            }
            catch (Exception ex)
            {
                _logger.Error("断开所有设备时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取设备列表
        /// </summary>
        /// <returns>设备视图模型列表</returns>
        public List<DeviceViewModel> GetDevices()
        {
            return new List<DeviceViewModel>(_devices);
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            try
            {
                _logger.Info("开始设备监控");
                _deviceService.StartMonitoring();
            }
            catch (Exception ex)
            {
                _logger.Error("启动设备监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            try
            {
                _logger.Info("停止设备监控");
                _deviceService.StopMonitoring();
            }
            catch (Exception ex)
            {
                _logger.Error("停止设备监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 取消事件订阅
                _eventAggregator?.GetEvent<SystemStartupEvent>()
                    .Unsubscribe(OnSystemStartup);
                    
                _eventAggregator?.GetEvent<DeviceConnectionEvent>()
                    .Unsubscribe(OnDeviceConnectionChanged);
                    
                _eventAggregator?.GetEvent<DeviceDataUpdateEvent>()
                    .Unsubscribe(OnDeviceDataUpdate);

                // 停止监控
                StopMonitoring();

                _logger?.Debug("DeviceModel 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 DeviceModel 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 设备视图模型
    /// </summary>
    public class DeviceViewModel : INotifyPropertyChanged
    {
        private string _status;
        private string _currentValue;
        private DateTime _lastUpdateTime;

        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 连接地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 连接状态
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        /// <summary>
        /// 当前值
        /// </summary>
        public string CurrentValue
        {
            get => _currentValue;
            set
            {
                if (_currentValue != value)
                {
                    _currentValue = value;
                    OnPropertyChanged(nameof(CurrentValue));
                }
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                if (_lastUpdateTime != value)
                {
                    _lastUpdateTime = value;
                    OnPropertyChanged(nameof(LastUpdateTime));
                }
            }
        }

        /// <summary>
        /// 属性变化事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性变化事件
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 设备列表变化事件参数
    /// </summary>
    public class DeviceListChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 设备列表
        /// </summary>
        public List<DeviceViewModel> Devices { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="devices">设备列表</param>
        public DeviceListChangedEventArgs(List<DeviceViewModel> devices)
        {
            Devices = devices;
        }
    }

    /// <summary>
    /// 设备状态变化事件参数
    /// </summary>
    public class DeviceStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 设备视图模型
        /// </summary>
        public DeviceViewModel Device { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="device">设备视图模型</param>
        public DeviceStatusChangedEventArgs(DeviceViewModel device)
        {
            Device = device;
        }
    }
}
