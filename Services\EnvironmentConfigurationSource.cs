using System;
using System.Collections.Generic;
using System.Linq;
using Contracts.Services;

namespace Services
{
    /// <summary>
    /// 环境变量配置源实现
    /// </summary>
    /// <remarks>
    /// 从环境变量中读取配置，支持前缀过滤
    /// </remarks>
    public class EnvironmentConfigurationSource : IConfigurationSource
    {
        private readonly string _prefix;
        private readonly bool _removePrefix;

        /// <summary>
        /// 配置源名称
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; }

        /// <summary>
        /// 是否支持写入
        /// </summary>
        public bool CanWrite => false;

        /// <summary>
        /// 是否支持热更新
        /// </summary>
        public bool SupportsHotReload => false;

        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> Changed;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="prefix">环境变量前缀</param>
        /// <param name="priority">优先级</param>
        /// <param name="removePrefix">是否移除前缀</param>
        /// <param name="name">配置源名称</param>
        public EnvironmentConfigurationSource(string prefix = null, int priority = 100, bool removePrefix = true, string name = null)
        {
            _prefix = prefix;
            _removePrefix = removePrefix;
            Priority = priority;
            Name = name ?? $"Environment:{prefix ?? "All"}";
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <returns>配置字典</returns>
        public Dictionary<string, object> Load()
        {
            var configurations = new Dictionary<string, object>();

            try
            {
                var environmentVariables = Environment.GetEnvironmentVariables();
                
                foreach (var key in environmentVariables.Keys)
                {
                    var keyStr = key.ToString();
                    var value = environmentVariables[key];

                    // 如果指定了前缀，只处理匹配的环境变量
                    if (!string.IsNullOrEmpty(_prefix))
                    {
                        if (!keyStr.StartsWith(_prefix, StringComparison.OrdinalIgnoreCase))
                            continue;

                        // 如果需要移除前缀
                        if (_removePrefix)
                        {
                            keyStr = keyStr.Substring(_prefix.Length);
                            
                            // 移除前导的分隔符
                            if (keyStr.StartsWith("_") || keyStr.StartsWith("."))
                            {
                                keyStr = keyStr.Substring(1);
                            }
                        }
                    }

                    // 转换键名格式（将下划线转换为点号）
                    keyStr = keyStr.Replace("__", ".").Replace("_", ".");

                    if (!string.IsNullOrEmpty(keyStr))
                    {
                        configurations[keyStr] = value;
                    }
                }
            }
            catch (Exception)
            {
                // 读取环境变量失败，返回空配置
            }

            return configurations;
        }

        /// <summary>
        /// 保存配置（不支持）
        /// </summary>
        /// <param name="configurations">配置字典</param>
        public void Save(Dictionary<string, object> configurations)
        {
            // 环境变量配置源不支持写入
            throw new NotSupportedException("环境变量配置源不支持写入操作");
        }

        /// <summary>
        /// 开始监控配置变更（不支持）
        /// </summary>
        public void StartWatching()
        {
            // 环境变量配置源不支持热更新
        }

        /// <summary>
        /// 停止监控配置变更（不支持）
        /// </summary>
        public void StopWatching()
        {
            // 环境变量配置源不支持热更新
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 无需释放资源
        }
    }
}
