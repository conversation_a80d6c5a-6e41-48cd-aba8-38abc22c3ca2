using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;

namespace SystemAcceptanceTest
{
    /// <summary>
    /// 系统验收测试程序
    /// </summary>
    /// <remarks>
    /// 对工业HMI框架进行全面的系统验收测试
    /// </remarks>
    class Program
    {
        private static int _passedTests = 0;
        private static int _failedTests = 0;
        private static int _totalTests = 0;

        static void Main(string[] args)
        {
            Console.WriteLine("工业HMI框架系统验收测试");
            Console.WriteLine("========================");
            Console.WriteLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            try
            {
                // 测试1: 环境验证测试
                RunTest("环境验证测试", TestEnvironment);

                // 测试2: 文件完整性测试
                RunTest("文件完整性测试", TestFileIntegrity);

                // 测试3: 依赖项测试
                RunTest("依赖项测试", TestDependencies);

                // 测试4: 应用程序启动测试
                RunTest("应用程序启动测试", TestApplicationStartup);

                // 测试5: 模块加载测试
                RunTest("模块加载测试", TestModuleLoading);

                // 测试6: 性能基准测试
                RunTest("性能基准测试", TestPerformanceBenchmark);

                // 测试7: 内存泄漏测试
                RunTest("内存泄漏测试", TestMemoryLeak);

                // 测试8: 日志功能测试
                RunTest("日志功能测试", TestLogging);

                // 测试9: 配置文件测试
                RunTest("配置文件测试", TestConfiguration);

                // 测试10: 用户界面测试
                RunTest("用户界面测试", TestUserInterface);

                // 生成测试报告
                GenerateTestReport();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试执行失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void RunTest(string testName, Func<bool> testMethod)
        {
            _totalTests++;
            Console.Write($"执行 {testName}... ");

            try
            {
                var stopwatch = Stopwatch.StartNew();
                bool result = testMethod();
                stopwatch.Stop();

                if (result)
                {
                    _passedTests++;
                    Console.WriteLine($"✅ 通过 ({stopwatch.ElapsedMilliseconds}ms)");
                }
                else
                {
                    _failedTests++;
                    Console.WriteLine($"❌ 失败 ({stopwatch.ElapsedMilliseconds}ms)");
                }
            }
            catch (Exception ex)
            {
                _failedTests++;
                Console.WriteLine($"❌ 异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试环境验证
        /// </summary>
        static bool TestEnvironment()
        {
            // 检查操作系统
            var osVersion = Environment.OSVersion;
            if (osVersion.Platform != PlatformID.Win32NT)
            {
                Console.WriteLine("  ❌ 不支持的操作系统");
                return false;
            }

            // 检查.NET Framework版本
            try
            {
                var netVersion = Environment.Version;
                // .NET Framework 4.8 对应的CLR版本是4.0.30319
                if (netVersion.Major >= 4)
                {
                    Console.WriteLine($"  ✅ .NET Framework版本: {netVersion}");
                }
                else
                {
                    Console.WriteLine($"  ❌ .NET Framework版本过低: {netVersion}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 无法检测.NET Framework版本: {ex.Message}");
                return false;
            }

            // 检查内存
            var totalMemory = GC.GetTotalMemory(false);
            if (totalMemory > 1024 * 1024 * 1024) // 1GB
            {
                Console.WriteLine("  ❌ 内存使用过高");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 测试文件完整性
        /// </summary>
        static bool TestFileIntegrity()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
            var requiredFiles = new[]
            {
                "IndustrialHMI.exe",
                "Contracts.dll",
                "Services.dll",
                "Serilog.dll",
                "System.Diagnostics.DiagnosticSource.dll"
            };

            foreach (var file in requiredFiles)
            {
                var filePath = Path.Combine(basePath, file);
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"  ❌ 缺少文件: {file}");
                    return false;
                }

                // 检查文件大小
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    Console.WriteLine($"  ❌ 文件为空: {file}");
                    return false;
                }
            }

            // 检查模块目录
            var modulesPath = Path.Combine(basePath, "Modules");
            if (!Directory.Exists(modulesPath))
            {
                Console.WriteLine("  ❌ 模块目录不存在");
                return false;
            }

            var moduleFiles = Directory.GetFiles(modulesPath, "*.dll");
            if (moduleFiles.Length == 0)
            {
                Console.WriteLine("  ❌ 没有发现模块文件");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 测试依赖项
        /// </summary>
        static bool TestDependencies()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");

            try
            {
                // 测试加载主要程序集
                var contractsPath = Path.Combine(basePath, "Contracts.dll");
                var contractsAssembly = System.Reflection.Assembly.LoadFrom(contractsPath);

                var servicesPath = Path.Combine(basePath, "Services.dll");
                var servicesAssembly = System.Reflection.Assembly.LoadFrom(servicesPath);

                // 测试Serilog依赖
                var serilogPath = Path.Combine(basePath, "Serilog.dll");
                var serilogAssembly = System.Reflection.Assembly.LoadFrom(serilogPath);

                // 测试DiagnosticSource依赖
                var diagnosticPath = Path.Combine(basePath, "System.Diagnostics.DiagnosticSource.dll");
                var diagnosticAssembly = System.Reflection.Assembly.LoadFrom(diagnosticPath);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 依赖加载失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试应用程序启动
        /// </summary>
        static bool TestApplicationStartup()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
            var exePath = Path.Combine(basePath, "IndustrialHMI.exe");

            if (!File.Exists(exePath))
            {
                Console.WriteLine("  ❌ 主程序文件不存在");
                return false;
            }

            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = exePath,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                var stopwatch = Stopwatch.StartNew();
                process.Start();

                // 等待进程启动
                Thread.Sleep(3000);

                stopwatch.Stop();

                if (process.HasExited)
                {
                    if (process.ExitCode == 0)
                    {
                        Console.WriteLine($"  ✅ 应用程序正常启动并退出 ({stopwatch.ElapsedMilliseconds}ms)");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine($"  ❌ 应用程序异常退出，退出代码: {process.ExitCode}");
                        return false;
                    }
                }
                else
                {
                    // 程序仍在运行，强制关闭
                    try
                    {
                        process.CloseMainWindow();
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                        }
                    }
                    catch { }

                    Console.WriteLine($"  ✅ 应用程序成功启动 ({stopwatch.ElapsedMilliseconds}ms)");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 启动测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试模块加载
        /// </summary>
        static bool TestModuleLoading()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
            var modulesPath = Path.Combine(basePath, "Modules");

            if (!Directory.Exists(modulesPath))
            {
                Console.WriteLine("  ❌ 模块目录不存在");
                return false;
            }

            var moduleFiles = Directory.GetFiles(modulesPath, "*.dll");
            var loadedModules = 0;

            foreach (var moduleFile in moduleFiles)
            {
                try
                {
                    var assembly = System.Reflection.Assembly.LoadFrom(moduleFile);
                    var types = assembly.GetTypes();

                    // 查找IModule接口的实现
                    foreach (var type in types)
                    {
                        if (!type.IsInterface && !type.IsAbstract)
                        {
                            var interfaces = type.GetInterfaces();
                            if (interfaces.Any(i => i.Name == "IModule"))
                            {
                                // 尝试创建实例
                                var constructor = type.GetConstructor(Type.EmptyTypes);
                                if (constructor != null)
                                {
                                    var instance = Activator.CreateInstance(type);
                                    loadedModules++;
                                    break;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  ❌ 模块加载失败: {Path.GetFileName(moduleFile)} - {ex.Message}");
                    return false;
                }
            }

            if (loadedModules >= 3) // 期望至少加载3个模块
            {
                Console.WriteLine($"  ✅ 成功加载 {loadedModules} 个模块");
                return true;
            }
            else
            {
                Console.WriteLine($"  ❌ 只加载了 {loadedModules} 个模块，期望至少3个");
                return false;
            }
        }

        /// <summary>
        /// 测试性能基准
        /// </summary>
        static bool TestPerformanceBenchmark()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
            var exePath = Path.Combine(basePath, "IndustrialHMI.exe");

            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = exePath,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                var stopwatch = Stopwatch.StartNew();
                process.Start();

                // 等待启动完成
                Thread.Sleep(2000);

                if (!process.HasExited)
                {
                    process.Refresh();
                    var memoryUsage = process.WorkingSet64 / 1024 / 1024; // MB

                    // 关闭进程
                    try
                    {
                        process.CloseMainWindow();
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                        }
                    }
                    catch { }

                    stopwatch.Stop();
                    var startupTime = stopwatch.ElapsedMilliseconds;

                    // 验收标准
                    bool startupOk = startupTime < 10000; // 10秒
                    bool memoryOk = memoryUsage < 200; // 200MB

                    if (startupOk && memoryOk)
                    {
                        Console.WriteLine($"  ✅ 启动时间: {startupTime}ms, 内存: {memoryUsage}MB");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine($"  ❌ 性能不达标 - 启动时间: {startupTime}ms, 内存: {memoryUsage}MB");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("  ❌ 进程启动后立即退出");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 性能测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试内存泄漏
        /// </summary>
        static bool TestMemoryLeak()
        {
            // 简单的内存泄漏测试
            var initialMemory = GC.GetTotalMemory(true);

            // 模拟一些操作
            for (int i = 0; i < 1000; i++)
            {
                var data = new byte[1024]; // 1KB
                // 让GC有机会回收
                if (i % 100 == 0)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }

            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;

            // 如果内存增长超过10MB，认为可能有内存泄漏
            if (memoryIncrease > 10 * 1024 * 1024)
            {
                Console.WriteLine($"  ❌ 可能存在内存泄漏，内存增长: {memoryIncrease / 1024 / 1024}MB");
                return false;
            }

            Console.WriteLine($"  ✅ 内存使用正常，增长: {memoryIncrease / 1024}KB");
            return true;
        }

        /// <summary>
        /// 测试日志功能
        /// </summary>
        static bool TestLogging()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
            var logsPath = Path.Combine(basePath, "logs");

            // 检查日志目录是否存在
            if (!Directory.Exists(logsPath))
            {
                Console.WriteLine("  ❌ 日志目录不存在");
                return false;
            }

            // 检查是否有日志文件
            var logFiles = Directory.GetFiles(logsPath, "*.log");
            if (logFiles.Length == 0)
            {
                Console.WriteLine("  ❌ 没有发现日志文件");
                return false;
            }

            // 检查最新日志文件的内容
            var latestLog = logFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();

            try
            {
                // 使用FileShare.ReadWrite来避免文件被占用的问题
                using (var fileStream = new FileStream(latestLog, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (var reader = new StreamReader(fileStream))
                {
                    var logContent = reader.ReadToEnd();

                    if (string.IsNullOrEmpty(logContent))
                    {
                        Console.WriteLine("  ❌ 日志文件为空");
                        return false;
                    }

                    Console.WriteLine($"  ✅ 日志功能正常，最新日志: {Path.GetFileName(latestLog)}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 读取日志文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试配置文件
        /// </summary>
        static bool TestConfiguration()
        {
            var basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..");
            var configFile = Path.Combine(basePath, "IndustrialHMI.exe.config");

            if (!File.Exists(configFile))
            {
                Console.WriteLine("  ❌ 配置文件不存在");
                return false;
            }

            try
            {
                var configContent = File.ReadAllText(configFile);
                if (string.IsNullOrEmpty(configContent))
                {
                    Console.WriteLine("  ❌ 配置文件为空");
                    return false;
                }

                // 检查是否包含必要的配置节
                if (!configContent.Contains("<configuration>") || !configContent.Contains("<startup>"))
                {
                    Console.WriteLine("  ❌ 配置文件格式不正确");
                    return false;
                }

                Console.WriteLine("  ✅ 配置文件格式正确");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 配置文件读取失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试用户界面
        /// </summary>
        static bool TestUserInterface()
        {
            // 这里只能做基本的UI相关测试
            // 实际的UI测试需要专门的UI自动化工具

            try
            {
                // 检查是否可以创建Windows Forms应用程序
                var assembly = System.Reflection.Assembly.LoadFrom(
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "IndustrialHMI.exe"));

                // 检查是否引用了Windows Forms
                var referencedAssemblies = assembly.GetReferencedAssemblies();
                bool hasWinForms = referencedAssemblies.Any(a => a.Name.Contains("System.Windows.Forms"));

                if (hasWinForms)
                {
                    Console.WriteLine("  ✅ UI框架引用正确");
                    return true;
                }
                else
                {
                    Console.WriteLine("  ❌ 缺少UI框架引用");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ UI测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        static void GenerateTestReport()
        {
            Console.WriteLine();
            Console.WriteLine("=== 系统验收测试报告 ===");
            Console.WriteLine($"测试完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"总测试数: {_totalTests}");
            Console.WriteLine($"通过测试: {_passedTests}");
            Console.WriteLine($"失败测试: {_failedTests}");
            Console.WriteLine($"成功率: {(_passedTests * 100.0 / _totalTests):F1}%");
            Console.WriteLine();

            if (_failedTests == 0)
            {
                Console.WriteLine("🎉 所有测试通过！系统验收成功！");
                Console.WriteLine("✅ 系统已准备好进行生产部署");
            }
            else
            {
                Console.WriteLine("❌ 部分测试失败，需要修复问题后重新测试");
                Console.WriteLine("⚠️ 系统暂不建议进行生产部署");
            }

            // 保存测试报告到文件
            var reportPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SystemAcceptanceTestReport.txt");
            var report = $@"工业HMI框架系统验收测试报告
=====================================
测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
总测试数: {_totalTests}
通过测试: {_passedTests}
失败测试: {_failedTests}
成功率: {(_passedTests * 100.0 / _totalTests):F1}%

测试结果: {(_failedTests == 0 ? "✅ 验收通过" : "❌ 验收失败")}
";

            File.WriteAllText(reportPath, report);
            Console.WriteLine($"测试报告已保存到: {reportPath}");
        }
    }
}
