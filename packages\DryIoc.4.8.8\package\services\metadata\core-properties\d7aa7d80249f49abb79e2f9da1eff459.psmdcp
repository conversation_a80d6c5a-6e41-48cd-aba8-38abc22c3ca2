<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator><PERSON><PERSON><PERSON></dc:creator>
  <dc:description>DryIoc is fast, small, full-featured IoC Container for .NET</dc:description>
  <dc:identifier>DryIoc</dc:identifier>
  <version>4.8.8</version>
  <keywords>IoC Container Inversion-of-Control DI Dependency-Injection DRY Service-Provider Factory</keywords>
  <lastModifiedBy>NuGet, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;Microsoft Windows NT 6.2.9200.0;.NET Framework 4.7.2</lastModifiedBy>
</coreProperties>