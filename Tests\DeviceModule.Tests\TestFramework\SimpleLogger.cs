using System;
using Contracts;

namespace DeviceModule.Tests.TestFramework
{
    /// <summary>
    /// 简单的测试日志记录器
    /// </summary>
    /// <remarks>
    /// 用于测试环境，避免Serilog依赖问题
    /// </remarks>
    public class SimpleLogger : ILogger
    {
        /// <summary>
        /// 是否启用调试日志
        /// </summary>
        public bool DebugEnabled { get; set; } = false;

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        public void Debug(string message)
        {
            if (DebugEnabled)
            {
                WriteLog("DEBUG", message);
            }
        }

        /// <summary>
        /// 记录调试信息（带异常）
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        public void Debug(string message, Exception exception)
        {
            if (DebugEnabled)
            {
                WriteLog("DEBUG", $"{message} - Exception: {exception?.Message}");
            }
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        public void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录信息（带异常）
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        public void Info(string message, Exception exception)
        {
            WriteLog("INFO", $"{message} - Exception: {exception?.Message}");
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        public void Warning(string message)
        {
            WriteLog("WARNING", message);
        }

        /// <summary>
        /// 记录警告（带异常）
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        public void Warning(string message, Exception exception)
        {
            WriteLog("WARNING", $"{message} - Exception: {exception?.Message}");
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        public void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录错误和异常
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        public void Error(string message, Exception exception)
        {
            WriteLog("ERROR", $"{message} - Exception: {exception?.Message}");
        }

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        public void Fatal(string message)
        {
            WriteLog("FATAL", message);
        }

        /// <summary>
        /// 记录致命错误（带异常）
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        public void Fatal(string message, Exception exception)
        {
            WriteLog("FATAL", $"{message} - Exception: {exception?.Message}");
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息</param>
        private void WriteLog(string level, string message)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            Console.WriteLine($"[{timestamp}] [{level}] {message}");
        }
    }
}
