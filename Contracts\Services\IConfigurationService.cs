using System;
using System.Collections.Generic;

namespace Contracts.Services
{
    /// <summary>
    /// 配置服务接口
    /// </summary>
    /// <remarks>
    /// 提供应用程序配置的读取和写入功能，
    /// 支持不同类型的配置值和配置文件管理
    /// </remarks>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取字符串配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        string GetString(string key, string defaultValue = "");

        /// <summary>
        /// 获取整数配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        int GetInt(string key, int defaultValue = 0);

        /// <summary>
        /// 获取布尔配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        bool GetBool(string key, bool defaultValue = false);

        /// <summary>
        /// 获取双精度浮点配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        double GetDouble(string key, double defaultValue = 0.0);

        /// <summary>
        /// 获取泛型配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue = default(T));

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetValue(string key, object value);

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        bool ContainsKey(string key);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        bool RemoveKey(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键列表</returns>
        IEnumerable<string> GetAllKeys();

        /// <summary>
        /// 获取指定前缀的所有配置
        /// </summary>
        /// <param name="prefix">键前缀</param>
        /// <returns>配置字典</returns>
        Dictionary<string, object> GetSection(string prefix);

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <returns>是否保存成功</returns>
        bool Save();

        /// <summary>
        /// 从文件重新加载配置
        /// </summary>
        /// <returns>是否加载成功</returns>
        bool Reload();

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        string ConfigFilePath { get; }

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// 添加配置源
        /// </summary>
        /// <param name="source">配置源</param>
        void AddSource(IConfigurationSource source);

        /// <summary>
        /// 移除配置源
        /// </summary>
        /// <param name="source">配置源</param>
        void RemoveSource(IConfigurationSource source);

        /// <summary>
        /// 获取所有配置源
        /// </summary>
        /// <returns>配置源列表</returns>
        IEnumerable<IConfigurationSource> GetSources();

        /// <summary>
        /// 启用热更新监控
        /// </summary>
        void EnableHotReload();

        /// <summary>
        /// 禁用热更新监控
        /// </summary>
        void DisableHotReload();

        /// <summary>
        /// 是否启用热更新
        /// </summary>
        bool IsHotReloadEnabled { get; }
    }

    /// <summary>
    /// 配置源接口
    /// </summary>
    public interface IConfigurationSource : IDisposable
    {
        /// <summary>
        /// 配置源名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 优先级（数值越大优先级越高）
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 是否支持写入
        /// </summary>
        bool CanWrite { get; }

        /// <summary>
        /// 是否支持热更新
        /// </summary>
        bool SupportsHotReload { get; }

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> Changed;

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <returns>配置字典</returns>
        Dictionary<string, object> Load();

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="configurations">配置字典</param>
        void Save(Dictionary<string, object> configurations);

        /// <summary>
        /// 开始监控配置变更
        /// </summary>
        void StartWatching();

        /// <summary>
        /// 停止监控配置变更
        /// </summary>
        void StopWatching();
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变更的配置键
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 旧值
        /// </summary>
        public object OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public object NewValue { get; set; }

        /// <summary>
        /// 配置源名称
        /// </summary>
        public string SourceName { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationChangedEventArgs()
        {
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        public ConfigurationChangedEventArgs(string key, object oldValue, object newValue)
            : this()
        {
            Key = key;
            OldValue = oldValue;
            NewValue = newValue;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        /// <param name="sourceName">配置源名称</param>
        public ConfigurationChangedEventArgs(string key, object oldValue, object newValue, string sourceName)
            : this(key, oldValue, newValue)
        {
            SourceName = sourceName;
        }
    }
}
