using System;
using System.Threading.Tasks;
using Contracts;
using Contracts.Events;
using TestFrameworkModule.Views;
using TestFrameworkModule.Models;

namespace TestFrameworkModule.Presenters
{
    /// <summary>
    /// 测试框架模块表示器
    /// </summary>
    /// <remarks>
    /// 协调视图和模型之间的交互，处理用户操作和业务逻辑
    /// </remarks>
    public class TestFrameworkPresenter : IPresenter
    {
        #region 私有字段

        private readonly TestFrameworkView _view;
        private readonly TestFrameworkModel _model;
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private bool _disposed = false;

        #endregion

        #region IPresenter 属性

        /// <summary>
        /// 关联的视图
        /// </summary>
        public IView View
        {
            get => _view;
            set => throw new NotSupportedException("View在构造函数中设置，不支持重新设置");
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="view">视图</param>
        /// <param name="model">模型</param>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public TestFrameworkPresenter(TestFrameworkView view, TestFrameworkModel model, 
            IEventAggregator eventAggregator, ILogger logger)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _model = model ?? throw new ArgumentNullException(nameof(model));
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region IPresenter 实现

        /// <summary>
        /// 初始化表示器
        /// </summary>
        public void Initialize()
        {
            try
            {
                _logger.Debug("初始化TestFrameworkPresenter");

                // 绑定视图事件
                BindViewEvents();

                // 绑定模型事件
                BindModelEvents();

                // 初始化视图状态
                _view.SetLoadingState(false);
                _view.AppendLog("[系统] 测试框架模块已初始化", System.Drawing.Color.Green);

                _logger.Debug("TestFrameworkPresenter初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error("TestFrameworkPresenter初始化失败", ex);
                _view.ShowError($"初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        public void LoadData()
        {
            try
            {
                _logger.Debug("加载TestFramework数据");

                // 加载模型数据
                _model.LoadData();

                // 更新视图
                _view.RefreshData();

                _logger.Debug("TestFramework数据加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error("TestFramework数据加载失败", ex);
                _view.ShowError($"数据加载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <returns>保存是否成功</returns>
        public bool SaveData()
        {
            try
            {
                _logger.Debug("保存TestFramework数据");

                // 保存模型数据
                var success = _model.SaveData();

                if (success)
                {
                    _view.AppendLog("[系统] 数据保存成功", System.Drawing.Color.Green);
                    _logger.Debug("TestFramework数据保存成功");
                }
                else
                {
                    _view.AppendLog("[系统] 数据保存失败", System.Drawing.Color.Red);
                    _logger.Warning("TestFramework数据保存失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error("TestFramework数据保存失败", ex);
                _view.ShowError($"数据保存失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证数据
        /// </summary>
        /// <returns>验证是否通过</returns>
        public bool ValidateData()
        {
            try
            {
                _logger.Debug("验证TestFramework数据");

                // 验证模型数据
                var isValid = _model.ValidateData();

                if (isValid)
                {
                    _view.AppendLog("[系统] 数据验证通过", System.Drawing.Color.Green);
                    _logger.Debug("TestFramework数据验证通过");
                }
                else
                {
                    _view.AppendLog("[系统] 数据验证失败", System.Drawing.Color.Red);
                    _logger.Warning("TestFramework数据验证失败");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.Error("TestFramework数据验证失败", ex);
                _view.ShowError($"数据验证失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 绑定视图事件
        /// </summary>
        private void BindViewEvents()
        {
            _view.RunIntegrationTestsClicked += OnRunIntegrationTestsClicked;
            _view.RunPerformanceTestsClicked += OnRunPerformanceTestsClicked;
            _view.RunMemoryLeakTestsClicked += OnRunMemoryLeakTestsClicked;
            _view.ClearLogClicked += OnClearLogClicked;
            _view.ExportReportClicked += OnExportReportClicked;
        }

        /// <summary>
        /// 绑定模型事件
        /// </summary>
        private void BindModelEvents()
        {
            _model.DataChanged += OnModelDataChanged;
            _model.StatusChanged += OnModelStatusChanged;
        }

        /// <summary>
        /// 处理运行集成测试事件
        /// </summary>
        private async void OnRunIntegrationTestsClicked(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("开始运行集成测试");
                _view.AppendLog("[测试] 开始运行集成测试", System.Drawing.Color.Blue);
                _view.SetLoadingState(true);

                await Task.Run(() =>
                {
                    _model.RunIntegrationTests((progress, status) =>
                    {
                        _view.UpdateProgress(progress, status);
                        _view.AppendLog($"[集成测试] {status}", System.Drawing.Color.Cyan);
                    });
                });

                _view.AppendLog("[测试] 集成测试完成", System.Drawing.Color.Green);
                _logger.Info("集成测试完成");
            }
            catch (Exception ex)
            {
                _logger.Error("运行集成测试失败", ex);
                _view.ShowError($"集成测试失败: {ex.Message}");
                _view.AppendLog($"[错误] 集成测试失败: {ex.Message}", System.Drawing.Color.Red);
            }
            finally
            {
                _view.SetLoadingState(false);
            }
        }

        /// <summary>
        /// 处理运行性能测试事件
        /// </summary>
        private async void OnRunPerformanceTestsClicked(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("开始运行性能测试");
                _view.AppendLog("[测试] 开始运行性能测试", System.Drawing.Color.Blue);
                _view.SetLoadingState(true);

                await Task.Run(() =>
                {
                    _model.RunPerformanceTests((progress, status) =>
                    {
                        _view.UpdateProgress(progress, status);
                        _view.AppendLog($"[性能测试] {status}", System.Drawing.Color.Yellow);
                    });
                });

                _view.AppendLog("[测试] 性能测试完成", System.Drawing.Color.Green);
                _logger.Info("性能测试完成");
            }
            catch (Exception ex)
            {
                _logger.Error("运行性能测试失败", ex);
                _view.ShowError($"性能测试失败: {ex.Message}");
                _view.AppendLog($"[错误] 性能测试失败: {ex.Message}", System.Drawing.Color.Red);
            }
            finally
            {
                _view.SetLoadingState(false);
            }
        }

        /// <summary>
        /// 处理运行内存泄漏测试事件
        /// </summary>
        private async void OnRunMemoryLeakTestsClicked(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("开始运行内存泄漏测试");
                _view.AppendLog("[测试] 开始运行内存泄漏测试", System.Drawing.Color.Blue);
                _view.SetLoadingState(true);

                await Task.Run(() =>
                {
                    _model.RunMemoryLeakTests((progress, status) =>
                    {
                        _view.UpdateProgress(progress, status);
                        _view.AppendLog($"[内存测试] {status}", System.Drawing.Color.Orange);
                    });
                });

                _view.AppendLog("[测试] 内存泄漏测试完成", System.Drawing.Color.Green);
                _logger.Info("内存泄漏测试完成");
            }
            catch (Exception ex)
            {
                _logger.Error("运行内存泄漏测试失败", ex);
                _view.ShowError($"内存泄漏测试失败: {ex.Message}");
                _view.AppendLog($"[错误] 内存泄漏测试失败: {ex.Message}", System.Drawing.Color.Red);
            }
            finally
            {
                _view.SetLoadingState(false);
            }
        }

        /// <summary>
        /// 处理清除日志事件
        /// </summary>
        private void OnClearLogClicked(object sender, EventArgs e)
        {
            try
            {
                _view.ClearLog();
                _logger.Debug("日志已清除");
            }
            catch (Exception ex)
            {
                _logger.Error("清除日志失败", ex);
                _view.ShowError($"清除日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理导出报告事件
        /// </summary>
        private void OnExportReportClicked(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("开始导出测试报告");
                _view.AppendLog("[系统] 开始导出测试报告", System.Drawing.Color.Blue);

                var success = _model.ExportTestReport();
                if (success)
                {
                    _view.AppendLog("[系统] 测试报告导出成功", System.Drawing.Color.Green);
                    _view.ShowMessage("测试报告导出成功");
                    _logger.Info("测试报告导出成功");
                }
                else
                {
                    _view.AppendLog("[系统] 测试报告导出失败", System.Drawing.Color.Red);
                    _view.ShowWarning("测试报告导出失败");
                    _logger.Warning("测试报告导出失败");
                }
            }
            catch (Exception ex)
            {
                _logger.Error("导出测试报告失败", ex);
                _view.ShowError($"导出测试报告失败: {ex.Message}");
                _view.AppendLog($"[错误] 导出测试报告失败: {ex.Message}", System.Drawing.Color.Red);
            }
        }

        /// <summary>
        /// 处理模型数据变化事件
        /// </summary>
        private void OnModelDataChanged(object sender, EventArgs e)
        {
            try
            {
                _view.RefreshData();
                _logger.Debug("模型数据已更新，视图已刷新");
            }
            catch (Exception ex)
            {
                _logger.Error("处理模型数据变化失败", ex);
            }
        }

        /// <summary>
        /// 处理模型状态变化事件
        /// </summary>
        private void OnModelStatusChanged(object sender, string status)
        {
            try
            {
                _view.AppendLog($"[模型] {status}", System.Drawing.Color.Gray);
                _logger.Debug($"模型状态变化: {status}");
            }
            catch (Exception ex)
            {
                _logger.Error("处理模型状态变化失败", ex);
            }
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 解绑视图事件
                if (_view != null)
                {
                    _view.RunIntegrationTestsClicked -= OnRunIntegrationTestsClicked;
                    _view.RunPerformanceTestsClicked -= OnRunPerformanceTestsClicked;
                    _view.RunMemoryLeakTestsClicked -= OnRunMemoryLeakTestsClicked;
                    _view.ClearLogClicked -= OnClearLogClicked;
                    _view.ExportReportClicked -= OnExportReportClicked;
                }

                // 解绑模型事件
                if (_model != null)
                {
                    _model.DataChanged -= OnModelDataChanged;
                    _model.StatusChanged -= OnModelStatusChanged;
                }

                _disposed = true;
                _logger?.Debug("TestFrameworkPresenter资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("TestFrameworkPresenter资源释放失败", ex);
            }
        }

        #endregion
    }
}
