# 工业HMI框架部署脚本
# Industrial HMI Framework Deployment Script
# 版本: 1.0.0
# 日期: 2025-08-13

param(
    [Parameter(Mandatory=$false)]
    [string]$TargetPath = "C:\IndustrialHMI",
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateShortcut = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$RegisterService = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    if ($Verbose) {
        Add-Content -Path "$PSScriptRoot\deploy.log" -Value $logMessage
    }
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查.NET Framework版本
function Test-DotNetFramework {
    try {
        $version = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction Stop
        if ($version.Release -ge 528040) {
            Write-Log ".NET Framework 4.8 或更高版本已安装"
            return $true
        } else {
            Write-Log ".NET Framework 版本过低，需要4.8或更高版本" "ERROR"
            return $false
        }
    } catch {
        Write-Log "无法检测.NET Framework版本" "ERROR"
        return $false
    }
}

# 创建目录结构
function New-DirectoryStructure {
    param([string]$BasePath)
    
    $directories = @(
        "$BasePath",
        "$BasePath\Modules",
        "$BasePath\logs",
        "$BasePath\config",
        "$BasePath\backup"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "创建目录: $dir"
        }
    }
}

# 复制文件
function Copy-ApplicationFiles {
    param([string]$SourcePath, [string]$TargetPath)
    
    Write-Log "开始复制应用程序文件..."
    
    # 主程序文件
    $mainFiles = @(
        "IndustrialHMI.exe",
        "IndustrialHMI.exe.config",
        "Contracts.dll",
        "Services.dll",
        "Serilog.dll",
        "Serilog.Sinks.Console.dll",
        "Serilog.Sinks.File.dll",
        "System.Diagnostics.DiagnosticSource.dll"
    )
    
    foreach ($file in $mainFiles) {
        $sourcePath = Join-Path $SourcePath $file
        $targetPath = Join-Path $TargetPath $file
        
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $targetPath -Force
            Write-Log "复制文件: $file"
        } else {
            Write-Log "文件不存在: $file" "WARNING"
        }
    }
    
    # 模块文件
    $modulesSource = Join-Path $SourcePath "Modules"
    $modulesTarget = Join-Path $TargetPath "Modules"
    
    if (Test-Path $modulesSource) {
        Copy-Item "$modulesSource\*" $modulesTarget -Recurse -Force
        Write-Log "复制模块文件到: $modulesTarget"
    }
}

# 创建桌面快捷方式
function New-DesktopShortcut {
    param([string]$TargetPath)
    
    $shell = New-Object -ComObject WScript.Shell
    $shortcutPath = Join-Path ([Environment]::GetFolderPath("Desktop")) "工业HMI框架.lnk"
    $shortcut = $shell.CreateShortcut($shortcutPath)
    $shortcut.TargetPath = Join-Path $TargetPath "IndustrialHMI.exe"
    $shortcut.WorkingDirectory = $TargetPath
    $shortcut.Description = "工业HMI框架"
    $shortcut.Save()
    
    Write-Log "创建桌面快捷方式: $shortcutPath"
}

# 创建配置文件
function New-ConfigurationFiles {
    param([string]$TargetPath)
    
    $configPath = Join-Path $TargetPath "config"
    
    # 创建默认配置文件
    $defaultConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="ModulesPath" value="Modules" />
    <add key="LogLevel" value="Information" />
    <add key="LogPath" value="logs" />
    <add key="AutoStartModules" value="true" />
  </appSettings>
</configuration>
"@
    
    $configFile = Join-Path $configPath "default.config"
    Set-Content -Path $configFile -Value $defaultConfig -Encoding UTF8
    Write-Log "创建默认配置文件: $configFile"
}

# 主部署流程
function Start-Deployment {
    Write-Log "开始部署工业HMI框架..."
    Write-Log "目标路径: $TargetPath"
    
    # 检查管理员权限
    if (!(Test-Administrator)) {
        Write-Log "需要管理员权限来执行部署" "ERROR"
        exit 1
    }
    
    # 检查.NET Framework
    if (!(Test-DotNetFramework)) {
        Write-Log "请先安装.NET Framework 4.8或更高版本" "ERROR"
        exit 1
    }
    
    # 确定源路径
    $sourcePath = Join-Path $PSScriptRoot "..\bin\Debug"
    if (!(Test-Path $sourcePath)) {
        Write-Log "源文件路径不存在: $sourcePath" "ERROR"
        exit 1
    }
    
    try {
        # 创建目录结构
        New-DirectoryStructure -BasePath $TargetPath
        
        # 复制应用程序文件
        Copy-ApplicationFiles -SourcePath $sourcePath -TargetPath $TargetPath
        
        # 创建配置文件
        New-ConfigurationFiles -TargetPath $TargetPath
        
        # 创建桌面快捷方式
        if ($CreateShortcut) {
            New-DesktopShortcut -TargetPath $TargetPath
        }
        
        Write-Log "部署完成！" "SUCCESS"
        Write-Log "应用程序已安装到: $TargetPath"
        
        # 询问是否立即启动
        $response = Read-Host "是否立即启动应用程序？(Y/N)"
        if ($response -eq "Y" -or $response -eq "y") {
            $exePath = Join-Path $TargetPath "IndustrialHMI.exe"
            Start-Process $exePath
            Write-Log "应用程序已启动"
        }
        
    } catch {
        Write-Log "部署失败: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
工业HMI框架部署脚本

用法:
    .\Deploy.ps1 [-TargetPath <路径>] [-CreateShortcut] [-RegisterService] [-Verbose]

参数:
    -TargetPath      目标安装路径 (默认: C:\IndustrialHMI)
    -CreateShortcut  创建桌面快捷方式 (默认: true)
    -RegisterService 注册为Windows服务 (默认: false)
    -Verbose         详细日志输出 (默认: false)

示例:
    .\Deploy.ps1
    .\Deploy.ps1 -TargetPath "D:\MyHMI" -Verbose
    .\Deploy.ps1 -TargetPath "C:\Program Files\IndustrialHMI" -CreateShortcut:$false

"@
}

# 主入口
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    exit 0
}

Write-Host "工业HMI框架部署脚本 v1.0.0" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

Start-Deployment

Write-Host "部署脚本执行完成。" -ForegroundColor Green
