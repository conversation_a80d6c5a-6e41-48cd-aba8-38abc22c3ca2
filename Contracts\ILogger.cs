using System;

namespace Contracts
{
    /// <summary>
    /// 日志记录器接口
    /// </summary>
    /// <remarks>
    /// 提供统一的日志记录接口，支持不同级别的日志记录
    /// </remarks>
    public interface ILogger
    {
        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <remarks>用于开发调试，生产环境通常不输出</remarks>
        void Debug(string message);

        /// <summary>
        /// 记录调试信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        void Debug(string message, Exception exception);

        /// <summary>
        /// 记录一般信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <remarks>用于记录系统正常运行的重要信息</remarks>
        void Info(string message);

        /// <summary>
        /// 记录一般信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        void Info(string message, Exception exception);

        /// <summary>
        /// 记录警告信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <remarks>用于记录可能的问题，但不影响系统运行</remarks>
        void Warning(string message);

        /// <summary>
        /// 记录警告信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        void Warning(string message, Exception exception);

        /// <summary>
        /// 记录错误信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <remarks>用于记录系统错误，需要关注和处理</remarks>
        void Error(string message);

        /// <summary>
        /// 记录错误信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        void Error(string message, Exception exception);

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <remarks>用于记录导致系统无法继续运行的严重错误</remarks>
        void Fatal(string message);

        /// <summary>
        /// 记录致命错误（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        void Fatal(string message, Exception exception);
    }
}
