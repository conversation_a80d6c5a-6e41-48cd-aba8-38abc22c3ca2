using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Contracts.Events;
using Contracts.Services;

namespace AlarmModule.Services
{
    /// <summary>
    /// 模拟报警服务实现
    /// </summary>
    /// <remarks>
    /// 提供模拟的报警管理功能，用于演示和测试
    /// </remarks>
    public class MockAlarmService : IAlarmService
    {
        private readonly List<AlarmInfo> _activeAlarms;
        private readonly List<AlarmInfo> _alarmHistory;
        private readonly List<AlarmRule> _alarmRules;
        private readonly Random _random;
        private Timer _ruleEvaluationTimer;
        private bool _isMonitoring;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public MockAlarmService()
        {
            _activeAlarms = new List<AlarmInfo>();
            _alarmHistory = new List<AlarmInfo>();
            _alarmRules = new List<AlarmRule>();
            _random = new Random();
            
            // 初始化默认报警规则
            InitializeDefaultRules();
            
            // 生成一些模拟历史报警
            GenerateMockHistoryAlarms();
        }

        /// <summary>
        /// 初始化默认报警规则
        /// </summary>
        private void InitializeDefaultRules()
        {
            var rules = new[]
            {
                new AlarmRule
                {
                    Name = "温度过高报警",
                    Description = "传感器温度超过30度时触发报警",
                    DeviceId = "SENSOR001",
                    DataPoint = "Temperature",
                    Level = AlarmLevel.Warning,
                    RuleType = AlarmRuleType.Threshold,
                    Operator = ComparisonOperator.GreaterThan,
                    Threshold = 30.0,
                    MessageTemplate = "设备 {DeviceId} 温度过高：{Value}°C"
                },
                new AlarmRule
                {
                    Name = "电机转速异常",
                    Description = "电机转速超出正常范围时触发报警",
                    DeviceId = "MOTOR001",
                    DataPoint = "Speed",
                    Level = AlarmLevel.Error,
                    RuleType = AlarmRuleType.Threshold,
                    Operator = ComparisonOperator.GreaterThan,
                    Threshold = 1500.0,
                    MessageTemplate = "设备 {DeviceId} 转速异常：{Value} RPM"
                },
                new AlarmRule
                {
                    Name = "设备离线报警",
                    Description = "设备连接状态为离线时触发报警",
                    DataPoint = "ConnectionStatus",
                    Level = AlarmLevel.Critical,
                    RuleType = AlarmRuleType.Status,
                    ExpectedValue = false,
                    MessageTemplate = "设备 {DeviceId} 连接中断"
                },
                new AlarmRule
                {
                    Name = "阀门开度异常",
                    Description = "阀门开度超过90%时触发报警",
                    DeviceId = "VALVE001",
                    DataPoint = "Position",
                    Level = AlarmLevel.Warning,
                    RuleType = AlarmRuleType.Threshold,
                    Operator = ComparisonOperator.GreaterThan,
                    Threshold = 90.0,
                    MessageTemplate = "设备 {DeviceId} 阀门开度过大：{Value}%"
                },
                new AlarmRule
                {
                    Name = "PLC CPU使用率高",
                    Description = "PLC CPU使用率超过80%时触发报警",
                    DeviceId = "PLC001",
                    DataPoint = "CpuUsage",
                    Level = AlarmLevel.Error,
                    RuleType = AlarmRuleType.Threshold,
                    Operator = ComparisonOperator.GreaterThan,
                    Threshold = 80.0,
                    MessageTemplate = "设备 {DeviceId} CPU使用率过高：{Value}%"
                }
            };

            _alarmRules.AddRange(rules);
        }

        /// <summary>
        /// 生成模拟历史报警
        /// </summary>
        private void GenerateMockHistoryAlarms()
        {
            var deviceIds = new[] { "PLC001", "SCADA001", "SENSOR001", "MOTOR001", "VALVE001" };
            var alarmTypes = new[] { "温度异常", "连接中断", "数据超限", "设备故障", "通信错误" };
            
            for (int i = 0; i < 20; i++)
            {
                var deviceId = deviceIds[_random.Next(deviceIds.Length)];
                var alarmType = alarmTypes[_random.Next(alarmTypes.Length)];
                var level = (AlarmLevel)_random.Next(1, 5); // 1-4对应Info到Critical
                var timestamp = DateTime.Now.AddHours(-_random.Next(1, 72)); // 过去3天内
                
                var alarm = new AlarmInfo(
                    Guid.NewGuid().ToString(),
                    alarmType,
                    $"设备 {deviceId} 发生 {alarmType}",
                    level,
                    deviceId)
                {
                    OccurredTime = timestamp,
                    Status = AlarmStatus.Cleared,
                    AcknowledgedBy = "System",
                    AcknowledgedTime = timestamp.AddMinutes(_random.Next(1, 30))
                };
                
                _alarmHistory.Add(alarm);
            }

            // 按时间倒序排列
            _alarmHistory.Sort((a, b) => b.OccurredTime.CompareTo(a.OccurredTime));
        }

        public IList<AlarmInfo> GetActiveAlarms()
        {
            lock (_lockObject)
            {
                return new List<AlarmInfo>(_activeAlarms);
            }
        }

        public IList<AlarmInfo> GetAlarmHistory(DateTime? startTime = null, DateTime? endTime = null)
        {
            lock (_lockObject)
            {
                var query = _alarmHistory.AsQueryable();
                
                if (startTime.HasValue)
                    query = query.Where(a => a.OccurredTime >= startTime.Value);

                if (endTime.HasValue)
                    query = query.Where(a => a.OccurredTime <= endTime.Value);

                return query.OrderByDescending(a => a.OccurredTime).ToList();
            }
        }

        public bool AcknowledgeAlarm(string alarmId, string acknowledgedBy = "System")
        {
            lock (_lockObject)
            {
                var alarm = _activeAlarms.FirstOrDefault(a => a.AlarmId == alarmId);
                if (alarm != null && alarm.Status == AlarmStatus.Active)
                {
                    alarm.Status = AlarmStatus.Acknowledged;
                    alarm.AcknowledgedBy = acknowledgedBy;
                    alarm.AcknowledgedTime = DateTime.Now;
                    return true;
                }
                return false;
            }
        }

        public int AcknowledgeAllAlarms(string acknowledgedBy = "System")
        {
            lock (_lockObject)
            {
                var activeAlarms = _activeAlarms.Where(a => a.Status == AlarmStatus.Active).ToList();
                foreach (var alarm in activeAlarms)
                {
                    alarm.Status = AlarmStatus.Acknowledged;
                    alarm.AcknowledgedBy = acknowledgedBy;
                    alarm.AcknowledgedTime = DateTime.Now;
                }
                return activeAlarms.Count;
            }
        }

        public bool ClearAlarm(string alarmId)
        {
            lock (_lockObject)
            {
                var alarm = _activeAlarms.FirstOrDefault(a => a.AlarmId == alarmId);
                if (alarm != null)
                {
                    alarm.Status = AlarmStatus.Cleared;
                    alarm.RecoveredTime = DateTime.Now;
                    
                    // 移动到历史记录
                    _activeAlarms.Remove(alarm);
                    _alarmHistory.Insert(0, alarm);
                    
                    return true;
                }
                return false;
            }
        }

        public int ClearAcknowledgedAlarms()
        {
            lock (_lockObject)
            {
                var acknowledgedAlarms = _activeAlarms.Where(a => a.Status == AlarmStatus.Acknowledged).ToList();
                foreach (var alarm in acknowledgedAlarms)
                {
                    alarm.Status = AlarmStatus.Cleared;
                    alarm.RecoveredTime = DateTime.Now;
                    
                    _activeAlarms.Remove(alarm);
                    _alarmHistory.Insert(0, alarm);
                }
                return acknowledgedAlarms.Count;
            }
        }

        public bool AddAlarmRule(AlarmRule rule)
        {
            if (rule == null) return false;
            
            lock (_lockObject)
            {
                _alarmRules.Add(rule);
                return true;
            }
        }

        public bool RemoveAlarmRule(string ruleId)
        {
            lock (_lockObject)
            {
                var rule = _alarmRules.FirstOrDefault(r => r.Id == ruleId);
                if (rule != null)
                {
                    _alarmRules.Remove(rule);
                    return true;
                }
                return false;
            }
        }

        public IList<AlarmRule> GetAlarmRules()
        {
            lock (_lockObject)
            {
                return new List<AlarmRule>(_alarmRules);
            }
        }

        public void EvaluateRules(string deviceId, string dataPoint, object value)
        {
            if (!_isMonitoring) return;
            
            lock (_lockObject)
            {
                var applicableRules = _alarmRules.Where(r => 
                    r.IsEnabled && 
                    (string.IsNullOrEmpty(r.DeviceId) || r.DeviceId == deviceId) &&
                    r.DataPoint == dataPoint).ToList();
                
                foreach (var rule in applicableRules)
                {
                    if (EvaluateRule(rule, deviceId, dataPoint, value))
                    {
                        TriggerAlarmFromRule(rule, deviceId, value);
                    }
                }
            }
        }

        /// <summary>
        /// 评估单个规则
        /// </summary>
        /// <param name="rule">报警规则</param>
        /// <param name="deviceId">设备ID</param>
        /// <param name="dataPoint">数据点</param>
        /// <param name="value">数据值</param>
        /// <returns>是否触发报警</returns>
        private bool EvaluateRule(AlarmRule rule, string deviceId, string dataPoint, object value)
        {
            try
            {
                switch (rule.RuleType)
                {
                    case AlarmRuleType.Threshold:
                        return EvaluateThresholdRule(rule, value);
                    case AlarmRuleType.Status:
                        return EvaluateStatusRule(rule, value);
                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 评估阈值规则
        /// </summary>
        private bool EvaluateThresholdRule(AlarmRule rule, object value)
        {
            if (!rule.Threshold.HasValue || !double.TryParse(value?.ToString(), out double numValue))
                return false;
            
            switch (rule.Operator)
            {
                case ComparisonOperator.GreaterThan:
                    return numValue > rule.Threshold.Value;
                case ComparisonOperator.GreaterThanOrEqual:
                    return numValue >= rule.Threshold.Value;
                case ComparisonOperator.LessThan:
                    return numValue < rule.Threshold.Value;
                case ComparisonOperator.LessThanOrEqual:
                    return numValue <= rule.Threshold.Value;
                case ComparisonOperator.Equal:
                    return Math.Abs(numValue - rule.Threshold.Value) < 0.001;
                case ComparisonOperator.NotEqual:
                    return Math.Abs(numValue - rule.Threshold.Value) >= 0.001;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 评估状态规则
        /// </summary>
        private bool EvaluateStatusRule(AlarmRule rule, object value)
        {
            if (rule.ExpectedValue == null) return false;
            
            return !value.Equals(rule.ExpectedValue);
        }

        /// <summary>
        /// 根据规则触发报警
        /// </summary>
        private void TriggerAlarmFromRule(AlarmRule rule, string deviceId, object value)
        {
            // 检查是否已存在相同的活动报警
            var existingAlarm = _activeAlarms.FirstOrDefault(a =>
                a.Source == deviceId &&
                a.AlarmName == rule.Name &&
                a.Status == AlarmStatus.Active);
            
            if (existingAlarm != null) return; // 避免重复报警
            
            var message = rule.MessageTemplate
                .Replace("{DeviceId}", deviceId)
                .Replace("{Value}", value?.ToString() ?? "N/A");
            
            var alarm = new AlarmInfo(
                Guid.NewGuid().ToString(),
                rule.Name,
                message,
                rule.Level,
                deviceId)
            {
                OccurredTime = DateTime.Now,
                Status = AlarmStatus.Active
            };
            
            _activeAlarms.Add(alarm);
        }

        public bool TriggerAlarm(AlarmInfo alarmInfo)
        {
            if (alarmInfo == null) return false;
            
            lock (_lockObject)
            {
                if (string.IsNullOrEmpty(alarmInfo.AlarmId))
                    alarmInfo.AlarmId = Guid.NewGuid().ToString();
                
                if (alarmInfo.OccurredTime == default(DateTime))
                    alarmInfo.OccurredTime = DateTime.Now;
                
                if (alarmInfo.Status == default(AlarmStatus))
                    alarmInfo.Status = AlarmStatus.Active;
                
                _activeAlarms.Add(alarmInfo);
                return true;
            }
        }

        public AlarmStatistics GetAlarmStatistics()
        {
            lock (_lockObject)
            {
                var now = DateTime.Now;
                var today = now.Date;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var monthStart = new DateTime(now.Year, now.Month, 1);
                
                var allAlarms = _activeAlarms.Concat(_alarmHistory).ToList();
                
                var stats = new AlarmStatistics
                {
                    ActiveAlarmCount = _activeAlarms.Count,
                    TodayAlarmCount = allAlarms.Count(a => a.OccurredTime >= today),
                    WeekAlarmCount = allAlarms.Count(a => a.OccurredTime >= weekStart),
                    MonthAlarmCount = allAlarms.Count(a => a.OccurredTime >= monthStart),
                    LastAlarmTime = allAlarms.OrderByDescending(a => a.OccurredTime).FirstOrDefault()?.OccurredTime
                };
                
                // 统计各级别报警数量
                foreach (AlarmLevel level in Enum.GetValues(typeof(AlarmLevel)))
                {
                    stats.AlarmCountByLevel[level] = allAlarms.Count(a => a.Level == level);
                }
                
                return stats;
            }
        }

        public void StartMonitoring()
        {
            if (_isMonitoring) return;
            
            _isMonitoring = true;
            
            // 启动定时器，模拟随机报警生成
            _ruleEvaluationTimer = new Timer(GenerateRandomAlarm, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        public void StopMonitoring()
        {
            _isMonitoring = false;
            _ruleEvaluationTimer?.Dispose();
            _ruleEvaluationTimer = null;
        }

        /// <summary>
        /// 生成随机报警（用于演示）
        /// </summary>
        private void GenerateRandomAlarm(object state)
        {
            if (!_isMonitoring) return;
            
            // 30%概率生成新报警
            if (_random.NextDouble() > 0.3) return;
            
            var deviceIds = new[] { "PLC001", "SCADA001", "SENSOR001", "MOTOR001", "VALVE001" };
            var alarmTypes = new[] { "数据异常", "通信超时", "参数越限", "状态异常" };
            var levels = new[] { AlarmLevel.Info, AlarmLevel.Warning, AlarmLevel.Error };
            
            var deviceId = deviceIds[_random.Next(deviceIds.Length)];
            var alarmType = alarmTypes[_random.Next(alarmTypes.Length)];
            var level = levels[_random.Next(levels.Length)];
            
            var alarm = new AlarmInfo(
                Guid.NewGuid().ToString(),
                alarmType,
                $"设备 {deviceId} 发生 {alarmType}",
                level,
                deviceId)
            {
                OccurredTime = DateTime.Now,
                Status = AlarmStatus.Active
            };
            
            lock (_lockObject)
            {
                _activeAlarms.Add(alarm);
            }
        }
    }
}
