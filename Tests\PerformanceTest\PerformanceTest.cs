using System;
using System.Diagnostics;
using System.IO;
using System.Threading;

namespace PerformanceTest
{
    /// <summary>
    /// 性能测试程序
    /// </summary>
    /// <remarks>
    /// 用于测试工业HMI框架的性能表现
    /// </remarks>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("工业HMI框架性能测试");
            Console.WriteLine("==================");
            Console.WriteLine();

            try
            {
                // 测试1: 启动时间测试
                TestStartupTime();

                // 测试2: 内存使用测试
                TestMemoryUsage();

                // 测试3: 模块加载性能测试
                TestModuleLoadingPerformance();

                Console.WriteLine("性能测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"性能测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试启动时间
        /// </summary>
        static void TestStartupTime()
        {
            Console.WriteLine("=== 启动时间测试 ===");
            
            var exePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "IndustrialHMI.exe");
            if (!File.Exists(exePath))
            {
                Console.WriteLine($"❌ 找不到主程序: {exePath}");
                return;
            }

            var results = new double[5]; // 测试5次取平均值
            
            for (int i = 0; i < 5; i++)
            {
                Console.Write($"第{i + 1}次测试... ");
                
                var stopwatch = Stopwatch.StartNew();
                
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = exePath,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();

                // 等待进程启动完成或退出
                var timeout = TimeSpan.FromSeconds(10);
                var startTime = DateTime.Now;

                while (process.MainWindowHandle == IntPtr.Zero &&
                       DateTime.Now - startTime < timeout &&
                       !process.HasExited)
                {
                    Thread.Sleep(10);
                    process.Refresh();
                }

                stopwatch.Stop();

                // 检查进程状态
                if (process.HasExited)
                {
                    Console.WriteLine($"进程已退出，退出代码: {process.ExitCode}");
                }
                else if (process.MainWindowHandle != IntPtr.Zero)
                {
                    Console.WriteLine($"进程正常启动，窗口句柄: {process.MainWindowHandle}");
                }
                else
                {
                    Console.WriteLine($"进程启动超时");
                }
                
                if (!process.HasExited)
                {
                    // 关闭进程
                    try
                    {
                        process.CloseMainWindow();
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                        }
                    }
                    catch { }
                }

                results[i] = stopwatch.Elapsed.TotalMilliseconds;
                Console.WriteLine($"{results[i]:F2}ms");
                
                // 等待一下再进行下次测试
                Thread.Sleep(1000);
            }

            var avgStartupTime = 0.0;
            for (int i = 0; i < results.Length; i++)
            {
                avgStartupTime += results[i];
            }
            avgStartupTime /= results.Length;

            Console.WriteLine($"平均启动时间: {avgStartupTime:F2}ms");
            Console.WriteLine($"启动时间评估: {(avgStartupTime < 10000 ? "✅ 优秀" : "❌ 需要优化")} (< 10000ms)");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试内存使用
        /// </summary>
        static void TestMemoryUsage()
        {
            Console.WriteLine("=== 内存使用测试 ===");
            
            var exePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "IndustrialHMI.exe");
            if (!File.Exists(exePath))
            {
                Console.WriteLine($"❌ 找不到主程序: {exePath}");
                return;
            }

            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    UseShellExecute = false,
                    CreateNoWindow = false
                }
            };

            try
            {
                process.Start();
                
                // 等待进程完全启动
                Thread.Sleep(2000);
                
                if (!process.HasExited)
                {
                    process.Refresh();
                    
                    var workingSet = process.WorkingSet64 / 1024 / 1024; // MB
                    var virtualMemory = process.VirtualMemorySize64 / 1024 / 1024; // MB
                    var privateMemory = process.PrivateMemorySize64 / 1024 / 1024; // MB
                    
                    Console.WriteLine($"工作集内存: {workingSet:F2} MB");
                    Console.WriteLine($"虚拟内存: {virtualMemory:F2} MB");
                    Console.WriteLine($"私有内存: {privateMemory:F2} MB");
                    
                    // 内存使用评估
                    Console.WriteLine($"内存使用评估: {(workingSet < 100 ? "✅ 优秀" : workingSet < 200 ? "⚠️ 良好" : "❌ 需要优化")} (工作集 < 100MB)");
                    
                    // 关闭进程
                    process.CloseMainWindow();
                    if (!process.WaitForExit(5000))
                    {
                        process.Kill();
                    }
                }
                else
                {
                    Console.WriteLine("❌ 进程启动后立即退出");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 内存测试失败: {ex.Message}");
            }
            finally
            {
                if (!process.HasExited)
                {
                    try { process.Kill(); } catch { }
                }
                process.Dispose();
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 测试模块加载性能
        /// </summary>
        static void TestModuleLoadingPerformance()
        {
            Console.WriteLine("=== 模块加载性能测试 ===");
            
            var modulesDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "Modules");
            if (!Directory.Exists(modulesDir))
            {
                Console.WriteLine($"❌ 找不到模块目录: {modulesDir}");
                return;
            }

            var dllFiles = Directory.GetFiles(modulesDir, "*.dll");
            Console.WriteLine($"发现 {dllFiles.Length} 个DLL文件");
            
            foreach (var dllFile in dllFiles)
            {
                var fileName = Path.GetFileName(dllFile);
                Console.Write($"测试 {fileName}... ");
                
                var stopwatch = Stopwatch.StartNew();
                
                try
                {
                    // 模拟模块加载过程
                    var assembly = System.Reflection.Assembly.LoadFrom(dllFile);
                    var types = assembly.GetTypes();
                    
                    // 查找IModule接口的实现
                    var moduleTypes = 0;
                    foreach (var type in types)
                    {
                        if (!type.IsInterface && !type.IsAbstract)
                        {
                            var interfaces = type.GetInterfaces();
                            foreach (var iface in interfaces)
                            {
                                if (iface.Name == "IModule" || iface.FullName == "Contracts.IModule")
                                {
                                    moduleTypes++;
                                    break;
                                }
                            }
                        }
                    }
                    
                    stopwatch.Stop();
                    Console.WriteLine($"{stopwatch.Elapsed.TotalMilliseconds:F2}ms (发现{moduleTypes}个模块)");
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    Console.WriteLine($"失败 - {ex.Message}");
                }
            }
            
            Console.WriteLine();
        }
    }
}
