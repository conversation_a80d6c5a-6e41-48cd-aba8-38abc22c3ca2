using System;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using DeviceModule.Models;
using DeviceModule.Presenters;
using DeviceModule.Services;
using DeviceModule.Views;

namespace DeviceModule
{
    /// <summary>
    /// 设备监控模块主类
    /// </summary>
    /// <remarks>
    /// 实现设备连接管理、状态监控和实时数据更新功能的模块
    /// </remarks>
    public class DeviceModuleMain : IModule
    {
        private DeviceView _view;
        private DevicePresenter _presenter;
        private DeviceModel _model;
        private IDeviceService _deviceService;
        private bool _disposed = false;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string Name => "设备监控";

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description => "实时监控设备连接状态和运行参数，提供设备管理和控制功能";

        /// <summary>
        /// 模块版本
        /// </summary>
        public string Version => "1.0.0";

        /// <summary>
        /// 事件聚合器（由ModuleLoader注入）
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器（由ModuleLoader注入）
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public ILogger Logger { get; set; }

        /// <summary>
        /// 配置服务（由ModuleLoader注入）
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public IConfigurationService ConfigurationService { get; set; }

        /// <summary>
        /// 无参构造函数
        /// </summary>
        /// <remarks>模块加载器要求必须有无参构造函数</remarks>
        public DeviceModuleMain()
        {
            // 模块加载器要求的无参构造函数
        }

        /// <summary>
        /// 模块初始化
        /// </summary>
        /// <remarks>
        /// 验证依赖注入，创建MVP组件，订阅系统事件
        /// </remarks>
        public void Initialize()
        {
            try
            {
                // 验证依赖注入
                if (EventAggregator == null)
                    throw new InvalidOperationException("EventAggregator 未注入");
                if (Logger == null)
                    throw new InvalidOperationException("Logger 未注入");

                Logger.Info("开始初始化设备监控模块");

                // 创建设备服务（使用模拟实现）
                _deviceService = new MockDeviceService();
                Logger.Debug("设备服务创建完成");

                // 创建MVP组件
                CreateMVPComponents();

                // 订阅系统事件
                SubscribeToSystemEvents();

                Logger.Info("设备监控模块初始化完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("初始化设备监控模块时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 创建MVP组件
        /// </summary>
        private void CreateMVPComponents()
        {
            try
            {
                // 创建视图
                _view = new DeviceView();
                Logger.Debug("设备视图创建完成");

                // 创建模型
                _model = new DeviceModel(_deviceService, EventAggregator, Logger);
                Logger.Debug("设备模型创建完成");

                // 创建表示器
                _presenter = new DevicePresenter(_view, _model, Logger);
                Logger.Debug("设备表示器创建完成");

                Logger.Info("MVP组件创建完成");
            }
            catch (Exception ex)
            {
                Logger.Error("创建MVP组件时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 订阅系统事件
        /// </summary>
        private void SubscribeToSystemEvents()
        {
            try
            {
                // 订阅系统启动事件
                EventAggregator.GetEvent<SystemStartupEvent>()
                    .Subscribe(OnSystemStartup, ThreadOption.UIThread, false);

                // 订阅系统关闭事件
                EventAggregator.GetEvent<SystemShutdownEvent>()
                    .Subscribe(OnSystemShutdown, ThreadOption.UIThread, false);

                // 订阅模块加载事件
                EventAggregator.GetEvent<ModuleLoadedEvent>()
                    .Subscribe(OnModuleLoaded, ThreadOption.UIThread, false);

                // 订阅模块卸载事件
                EventAggregator.GetEvent<ModuleUnloadedEvent>()
                    .Subscribe(OnModuleUnloaded, ThreadOption.UIThread, false);

                Logger.Debug("系统事件订阅完成");
            }
            catch (Exception ex)
            {
                Logger.Error("订阅系统事件时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 系统启动事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemStartup(SystemStartupEvent eventData)
        {
            try
            {
                Logger.Info("收到系统启动事件，设备监控模块开始工作");
                
                // 初始化表示器
                _presenter?.Initialize();
                
                Logger.Info("设备监控模块响应系统启动完成");
            }
            catch (Exception ex)
            {
                Logger.Error("处理系统启动事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 系统关闭事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemShutdown(SystemShutdownEvent eventData)
        {
            try
            {
                Logger.Info("收到系统关闭事件，设备监控模块开始清理");
                
                // 停止监控
                _presenter?.StopMonitoring();
                
                Logger.Info("设备监控模块响应系统关闭完成");
            }
            catch (Exception ex)
            {
                Logger.Error("处理系统关闭事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 模块加载事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleLoaded(ModuleLoadedEvent eventData)
        {
            Logger.Debug($"模块已加载: {eventData.ModuleName}");
        }

        /// <summary>
        /// 模块卸载事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleUnloaded(ModuleUnloadedEvent eventData)
        {
            Logger.Debug($"模块已卸载: {eventData.ModuleName}");
        }

        /// <summary>
        /// 获取模块的用户控件
        /// </summary>
        /// <returns>用户控件</returns>
        public UserControl GetUserControl()
        {
            return _view;
        }

        /// <summary>
        /// 启动模块
        /// </summary>
        /// <remarks>系统就绪后调用，开始设备监控</remarks>
        public void Start()
        {
            try
            {
                Logger.Info("启动设备监控模块");
                
                // 启动设备监控
                _presenter?.StartMonitoring();
                
                Logger.Info("设备监控模块启动完成");
            }
            catch (Exception ex)
            {
                Logger.Error("启动设备监控模块时发生错误", ex);
            }
        }

        /// <summary>
        /// 停止模块
        /// </summary>
        public void Stop()
        {
            try
            {
                Logger.Info("停止设备监控模块");
                
                // 停止设备监控
                _presenter?.StopMonitoring();
                
                Logger.Info("设备监控模块停止完成");
            }
            catch (Exception ex)
            {
                Logger.Error("停止设备监控模块时发生错误", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Logger?.Info("开始释放设备监控模块资源");

                // 取消系统事件订阅
                EventAggregator?.GetEvent<SystemStartupEvent>()
                    .Unsubscribe(OnSystemStartup);

                EventAggregator?.GetEvent<SystemShutdownEvent>()
                    .Unsubscribe(OnSystemShutdown);

                EventAggregator?.GetEvent<ModuleLoadedEvent>()
                    .Unsubscribe(OnModuleLoaded);

                EventAggregator?.GetEvent<ModuleUnloadedEvent>()
                    .Unsubscribe(OnModuleUnloaded);

                // 释放MVP组件
                _presenter?.Dispose();
                _model?.Dispose();
                _view?.Dispose();

                Logger?.Info("设备监控模块资源释放完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("释放设备监控模块资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
