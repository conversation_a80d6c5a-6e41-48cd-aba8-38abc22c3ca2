using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Linq;
using Contracts;
using Contracts.Events;

namespace TestFrameworkModule.Tests
{
    /// <summary>
    /// 内存泄漏测试套件
    /// </summary>
    /// <remarks>
    /// 提供内存泄漏检测和分析功能
    /// </remarks>
    public class MemoryLeakTestSuite : IDisposable
    {
        #region 私有字段

        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private readonly List<MemoryLeakTestResult> _testResults;
        private readonly Timer _memoryMonitorTimer;
        private readonly List<long> _memorySnapshots;
        private bool _disposed = false;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public MemoryLeakTestSuite(IEventAggregator eventAggregator, ILogger logger)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _testResults = new List<MemoryLeakTestResult>();
            _memorySnapshots = new List<long>();

            // 创建内存监控定时器（每秒记录一次内存使用）
            _memoryMonitorTimer = new Timer(RecordMemorySnapshot, null, Timeout.Infinite, 1000);
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 测试结果
        /// </summary>
        public IReadOnlyList<MemoryLeakTestResult> TestResults => _testResults;

        /// <summary>
        /// 内存快照
        /// </summary>
        public IReadOnlyList<long> MemorySnapshots => _memorySnapshots;

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化测试套件
        /// </summary>
        public void Initialize()
        {
            if (_disposed) return;

            try
            {
                _logger.Info("初始化内存泄漏测试套件");
                _testResults.Clear();
                _memorySnapshots.Clear();

                // 启动内存监控
                _memoryMonitorTimer.Change(0, 1000);

                _logger.Info("内存泄漏测试套件初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error("内存泄漏测试套件初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止测试套件
        /// </summary>
        public void Stop()
        {
            if (_disposed) return;

            try
            {
                _logger.Info("停止内存泄漏测试套件");

                // 停止内存监控
                _memoryMonitorTimer.Change(Timeout.Infinite, Timeout.Infinite);

                _logger.Info("内存泄漏测试套件已停止");
            }
            catch (Exception ex)
            {
                _logger.Error("停止内存泄漏测试套件失败", ex);
            }
        }

        /// <summary>
        /// 运行所有内存泄漏测试
        /// </summary>
        /// <returns>测试结果</returns>
        public MemoryLeakTestSuiteResult RunAllTests()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(MemoryLeakTestSuite));

            try
            {
                _logger.Info("开始运行内存泄漏测试");
                _testResults.Clear();

                var stopwatch = Stopwatch.StartNew();

                // 运行各项内存泄漏测试
                RunEventSubscriptionLeakTest();
                RunLargeObjectAllocationTest();
                RunRepeatedOperationTest();
                RunGarbageCollectionEfficiencyTest();
                RunLongTermMemoryMonitoringTest();

                stopwatch.Stop();

                var result = new MemoryLeakTestSuiteResult
                {
                    SuiteName = "内存泄漏测试套件",
                    TotalTests = _testResults.Count,
                    ExecutionTime = stopwatch.Elapsed,
                    TestResults = new List<MemoryLeakTestResult>(_testResults),
                    MemorySnapshots = new List<long>(_memorySnapshots),
                    FinalMemoryUsage = GC.GetTotalMemory(false) / (1024 * 1024) // MB
                };

                _logger.Info($"内存泄漏测试完成: {result.TotalTests} 个测试, 耗时: {result.ExecutionTime.TotalSeconds:F2}秒");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error("运行内存泄漏测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 运行事件订阅泄漏测试
        /// </summary>
        public void RunEventSubscriptionLeakTest()
        {
            var testName = "事件订阅内存泄漏测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                // 记录初始内存
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                var initialMemory = GC.GetTotalMemory(false);

                var subscriptions = new List<Action<SystemEvent>>();

                // 创建大量事件订阅
                for (int i = 0; i < 1000; i++)
                {
                    var subscription = new Action<SystemEvent>(evt => { /* 空处理 */ });
                    subscriptions.Add(subscription);

                    _eventAggregator.GetEvent<SystemEvent>()
                        .Subscribe(subscription, keepSubscriberReferenceAlive: false);
                }

                // 发布一些事件
                for (int i = 0; i < 100; i++)
                {
                    _eventAggregator.GetEvent<SystemEvent>()
                        .Publish(new SystemEvent("LeakTest", $"Event_{i}"));
                }

                var afterSubscriptionMemory = GC.GetTotalMemory(false);

                // 取消订阅
                for (int i = 0; i < subscriptions.Count; i++)
                {
                    _eventAggregator.GetEvent<SystemEvent>()
                        .Unsubscribe(subscriptions[i]);
                }

                subscriptions.Clear();
                subscriptions = null;

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(false);

                stopwatch.Stop();

                var memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024.0); // MB
                var subscriptionMemory = (afterSubscriptionMemory - initialMemory) / (1024 * 1024.0); // MB

                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    InitialMemoryMB = initialMemory / (1024 * 1024.0),
                    FinalMemoryMB = finalMemory / (1024 * 1024.0),
                    MemoryLeakMB = memoryIncrease,
                    PeakMemoryMB = afterSubscriptionMemory / (1024 * 1024.0),
                    Passed = memoryIncrease < 5, // 内存增长小于5MB认为通过
                    Message = $"订阅内存: {subscriptionMemory:F2}MB, 泄漏: {memoryIncrease:F2}MB"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行大对象分配测试
        /// </summary>
        public void RunLargeObjectAllocationTest()
        {
            var testName = "大对象分配测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                // 记录初始内存
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                var initialMemory = GC.GetTotalMemory(false);

                var largeObjects = new List<byte[]>();

                // 分配大对象（每个10MB）
                for (int i = 0; i < 50; i++)
                {
                    largeObjects.Add(new byte[10 * 1024 * 1024]); // 10MB
                    Thread.Sleep(10); // 短暂延迟
                }

                var peakMemory = GC.GetTotalMemory(false);

                // 释放大对象
                largeObjects.Clear();
                largeObjects = null;

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(false);

                stopwatch.Stop();

                var allocatedMemory = (peakMemory - initialMemory) / (1024 * 1024.0); // MB
                var releasedMemory = (peakMemory - finalMemory) / (1024 * 1024.0); // MB
                var memoryLeak = (finalMemory - initialMemory) / (1024 * 1024.0); // MB

                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    InitialMemoryMB = initialMemory / (1024 * 1024.0),
                    FinalMemoryMB = finalMemory / (1024 * 1024.0),
                    PeakMemoryMB = peakMemory / (1024 * 1024.0),
                    MemoryLeakMB = memoryLeak,
                    AllocatedMemoryMB = allocatedMemory,
                    ReleasedMemoryMB = releasedMemory,
                    Passed = memoryLeak < 10 && releasedMemory > allocatedMemory * 0.9, // 90%内存释放
                    Message = $"分配: {allocatedMemory:F1}MB, 释放: {releasedMemory:F1}MB, 泄漏: {memoryLeak:F1}MB"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行重复操作测试
        /// </summary>
        public void RunRepeatedOperationTest()
        {
            var testName = "重复操作内存测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                // 记录初始内存
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                var initialMemory = GC.GetTotalMemory(false);

                var memoryReadings = new List<long>();

                // 执行重复操作
                for (int i = 0; i < 1000; i++)
                {
                    // 模拟重复的业务操作
                    var tempData = new List<string>();
                    for (int j = 0; j < 100; j++)
                    {
                        tempData.Add($"TempData_{i}_{j}");
                    }

                    // 发布事件
                    _eventAggregator.GetEvent<SystemEvent>()
                        .Publish(new SystemEvent("RepeatedOperation", $"Operation_{i}"));

                    // 清理临时数据
                    tempData.Clear();
                    tempData = null;

                    // 每100次操作记录一次内存
                    if (i % 100 == 0)
                    {
                        memoryReadings.Add(GC.GetTotalMemory(false));
                    }
                }

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(false);

                stopwatch.Stop();

                var memoryTrend = AnalyzeMemoryTrend(memoryReadings);
                var memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024.0); // MB

                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    InitialMemoryMB = initialMemory / (1024 * 1024.0),
                    FinalMemoryMB = finalMemory / (1024 * 1024.0),
                    MemoryLeakMB = memoryIncrease,
                    MemoryTrend = memoryTrend,
                    OperationsPerformed = 1000,
                    Passed = memoryIncrease < 20 && memoryTrend != "Increasing", // 内存增长小于20MB且无上升趋势
                    Message = $"1000次操作后内存增长: {memoryIncrease:F2}MB, 趋势: {memoryTrend}"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行垃圾回收效率测试
        /// </summary>
        public void RunGarbageCollectionEfficiencyTest()
        {
            var testName = "垃圾回收效率测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                var gcCounts = new int[3];
                for (int i = 0; i < 3; i++)
                {
                    gcCounts[i] = GC.CollectionCount(i);
                }

                var initialMemory = GC.GetTotalMemory(false);

                // 创建大量短生命周期对象
                for (int i = 0; i < 10000; i++)
                {
                    var tempObjects = new List<object>();
                    for (int j = 0; j < 100; j++)
                    {
                        tempObjects.Add(new { Id = j, Data = $"TempObject_{i}_{j}" });
                    }
                    // tempObjects 在循环结束后变为垃圾
                }

                // 手动触发垃圾回收
                var gcStopwatch = Stopwatch.StartNew();
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                gcStopwatch.Stop();

                var finalMemory = GC.GetTotalMemory(false);

                var newGcCounts = new int[3];
                for (int i = 0; i < 3; i++)
                {
                    newGcCounts[i] = GC.CollectionCount(i);
                }

                stopwatch.Stop();

                var memoryReleased = (initialMemory - finalMemory) / (1024 * 1024.0); // MB
                var gen0Collections = newGcCounts[0] - gcCounts[0];
                var gen1Collections = newGcCounts[1] - gcCounts[1];
                var gen2Collections = newGcCounts[2] - gcCounts[2];

                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    GcTime = gcStopwatch.Elapsed,
                    InitialMemoryMB = initialMemory / (1024 * 1024.0),
                    FinalMemoryMB = finalMemory / (1024 * 1024.0),
                    ReleasedMemoryMB = memoryReleased,
                    Gen0Collections = gen0Collections,
                    Gen1Collections = gen1Collections,
                    Gen2Collections = gen2Collections,
                    Passed = gcStopwatch.Elapsed.TotalMilliseconds < 1000, // GC时间小于1秒
                    Message = $"GC时间: {gcStopwatch.Elapsed.TotalMilliseconds:F0}ms, 释放: {memoryReleased:F1}MB, " +
                             $"GC次数: Gen0={gen0Collections}, Gen1={gen1Collections}, Gen2={gen2Collections}"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行长期内存监控测试
        /// </summary>
        public void RunLongTermMemoryMonitoringTest()
        {
            var testName = "长期内存监控测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                var initialMemory = GC.GetTotalMemory(false);
                var monitoringSnapshots = new List<long>();

                // 监控30秒的内存使用
                for (int i = 0; i < 30; i++)
                {
                    // 执行一些操作
                    var tempData = new byte[1024 * 1024]; // 1MB
                    Array.Clear(tempData, 0, tempData.Length);
                    tempData = null;

                    // 记录内存快照
                    monitoringSnapshots.Add(GC.GetTotalMemory(false));

                    Thread.Sleep(1000); // 等待1秒
                }

                var finalMemory = GC.GetTotalMemory(false);

                stopwatch.Stop();

                var memoryTrend = AnalyzeMemoryTrend(monitoringSnapshots);
                var averageMemory = monitoringSnapshots.Average() / (1024 * 1024.0); // MB
                var maxMemory = monitoringSnapshots.Max() / (1024 * 1024.0); // MB
                var minMemory = monitoringSnapshots.Min() / (1024 * 1024.0); // MB
                var memoryVariance = CalculateVariance(monitoringSnapshots) / (1024 * 1024.0); // MB

                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    InitialMemoryMB = initialMemory / (1024 * 1024.0),
                    FinalMemoryMB = finalMemory / (1024 * 1024.0),
                    AverageMemoryMB = averageMemory,
                    MaxMemoryMB = maxMemory,
                    MinMemoryMB = minMemory,
                    MemoryVariance = memoryVariance,
                    MemoryTrend = memoryTrend,
                    MonitoringDuration = TimeSpan.FromSeconds(30),
                    Passed = memoryTrend != "Increasing" && memoryVariance < 50, // 无上升趋势且方差小于50MB
                    Message = $"30秒监控: 平均={averageMemory:F1}MB, 最大={maxMemory:F1}MB, 趋势={memoryTrend}"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new MemoryLeakTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 记录内存快照
        /// </summary>
        /// <param name="state">状态对象</param>
        private void RecordMemorySnapshot(object state)
        {
            if (_disposed) return;

            try
            {
                var memory = GC.GetTotalMemory(false);
                lock (_memorySnapshots)
                {
                    _memorySnapshots.Add(memory);

                    // 限制快照数量，保留最近的1000个
                    if (_memorySnapshots.Count > 1000)
                    {
                        _memorySnapshots.RemoveAt(0);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Warning($"记录内存快照失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 分析内存趋势
        /// </summary>
        /// <param name="memoryReadings">内存读数</param>
        /// <returns>趋势描述</returns>
        private string AnalyzeMemoryTrend(List<long> memoryReadings)
        {
            if (memoryReadings.Count < 2) return "Insufficient Data";

            var firstHalf = memoryReadings.Take(memoryReadings.Count / 2).Average();
            var secondHalf = memoryReadings.Skip(memoryReadings.Count / 2).Average();

            var difference = secondHalf - firstHalf;
            var percentageChange = (difference / firstHalf) * 100;

            if (percentageChange > 10) return "Increasing";
            if (percentageChange < -10) return "Decreasing";
            return "Stable";
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        /// <param name="values">数值列表</param>
        /// <returns>方差</returns>
        private double CalculateVariance(List<long> values)
        {
            if (values.Count == 0) return 0;

            var average = values.Average();
            var sumOfSquares = values.Sum(x => Math.Pow(x - average, 2));
            return sumOfSquares / values.Count;
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Stop();
                _memoryMonitorTimer?.Dispose();
                _testResults.Clear();
                _memorySnapshots.Clear();
                _disposed = true;
                _logger?.Debug("MemoryLeakTestSuite资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("MemoryLeakTestSuite资源释放失败", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// 内存泄漏测试结果
    /// </summary>
    public class MemoryLeakTestResult
    {
        public string TestName { get; set; }
        public bool Passed { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public string Message { get; set; }
        public string Details { get; set; }

        // 内存指标
        public double InitialMemoryMB { get; set; }
        public double FinalMemoryMB { get; set; }
        public double PeakMemoryMB { get; set; }
        public double MemoryLeakMB { get; set; }
        public double AllocatedMemoryMB { get; set; }
        public double ReleasedMemoryMB { get; set; }
        public double AverageMemoryMB { get; set; }
        public double MaxMemoryMB { get; set; }
        public double MinMemoryMB { get; set; }
        public double MemoryVariance { get; set; }
        public string MemoryTrend { get; set; }

        // 垃圾回收指标
        public TimeSpan GcTime { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }

        // 其他指标
        public int OperationsPerformed { get; set; }
        public TimeSpan MonitoringDuration { get; set; }
    }

    /// <summary>
    /// 内存泄漏测试套件结果
    /// </summary>
    public class MemoryLeakTestSuiteResult
    {
        public string SuiteName { get; set; }
        public int TotalTests { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public List<MemoryLeakTestResult> TestResults { get; set; }
        public List<long> MemorySnapshots { get; set; }
        public double FinalMemoryUsage { get; set; }
    }
}
