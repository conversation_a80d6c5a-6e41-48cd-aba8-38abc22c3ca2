using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Contracts;
using Contracts.Events;
using CommunicationTestModule.Services;

namespace CommunicationTestModule.Models
{
    /// <summary>
    /// 通信测试模型
    /// </summary>
    /// <remarks>
    /// 管理通信测试的数据和状态
    /// </remarks>
    public class CommunicationTestModel : IDisposable
    {
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private readonly EventMonitor _eventMonitor;
        private readonly TestCaseManager _testCaseManager;
        private readonly PerformanceMonitor _performanceMonitor;
        private bool _disposed = false;

        /// <summary>
        /// 事件记录变化事件
        /// </summary>
        public event EventHandler<EventRecordChangedEventArgs> EventRecordChanged;

        /// <summary>
        /// 测试进度变化事件
        /// </summary>
        public event EventHandler<TestProgressEventArgs> TestProgressChanged;

        /// <summary>
        /// 测试完成事件
        /// </summary>
        public event EventHandler<TestCompletedEventArgs> TestCompleted;

        /// <summary>
        /// 性能数据更新事件
        /// </summary>
        public event EventHandler<PerformanceDataEventArgs> PerformanceDataUpdated;

        /// <summary>
        /// 模型数据变化事件
        /// </summary>
        public event EventHandler<ModelDataChangedEventArgs> ModelDataChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public CommunicationTestModel(IEventAggregator eventAggregator, ILogger logger)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 创建服务组件
            _eventMonitor = new EventMonitor(_eventAggregator, _logger);
            _testCaseManager = new TestCaseManager(_eventAggregator, _logger);
            _performanceMonitor = new PerformanceMonitor(_logger);

            // 订阅事件
            _eventMonitor.EventRecordChanged += OnEventRecordChanged;
            _testCaseManager.TestProgressChanged += OnTestProgressChanged;
            _testCaseManager.TestCompleted += OnTestCompleted;
            _performanceMonitor.PerformanceDataUpdated += OnPerformanceDataUpdated;

            _logger.Info("CommunicationTestModel 初始化完成");
        }

        /// <summary>
        /// 开始事件监控
        /// </summary>
        public void StartEventMonitoring()
        {
            _eventMonitor.StartMonitoring();
            NotifyModelDataChanged("EventMonitoring", true);
        }

        /// <summary>
        /// 停止事件监控
        /// </summary>
        public void StopEventMonitoring()
        {
            _eventMonitor.StopMonitoring();
            NotifyModelDataChanged("EventMonitoring", false);
        }

        /// <summary>
        /// 开始性能监控
        /// </summary>
        /// <param name="intervalMs">监控间隔（毫秒）</param>
        public void StartPerformanceMonitoring(int intervalMs = 1000)
        {
            _performanceMonitor.StartMonitoring(intervalMs);
            NotifyModelDataChanged("PerformanceMonitoring", true);
        }

        /// <summary>
        /// 停止性能监控
        /// </summary>
        public void StopPerformanceMonitoring()
        {
            _performanceMonitor.StopMonitoring();
            NotifyModelDataChanged("PerformanceMonitoring", false);
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async void RunAllTests()
        {
            try
            {
                await _testCaseManager.RunAllTestsAsync();
            }
            catch (Exception ex)
            {
                _logger.Error("运行所有测试时发生错误", ex);
            }
        }

        /// <summary>
        /// 运行指定分类的测试
        /// </summary>
        /// <param name="category">测试分类</param>
        public async void RunTestsByCategory(string category)
        {
            try
            {
                await _testCaseManager.RunTestsByCategoryAsync(category);
            }
            catch (Exception ex)
            {
                _logger.Error($"运行分类测试时发生错误: {category}", ex);
            }
        }

        /// <summary>
        /// 停止测试
        /// </summary>
        public void StopTests()
        {
            _testCaseManager.StopTests();
        }

        /// <summary>
        /// 获取测试用例列表
        /// </summary>
        /// <returns>测试用例列表</returns>
        public List<TestCase> GetTestCases()
        {
            return _testCaseManager.GetTestCases();
        }

        /// <summary>
        /// 获取测试结果列表
        /// </summary>
        /// <returns>测试结果列表</returns>
        public List<TestResult> GetTestResults()
        {
            return _testCaseManager.GetTestResults();
        }

        /// <summary>
        /// 获取事件记录列表
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>事件记录列表</returns>
        public List<EventRecord> GetEventRecords(int count = 100)
        {
            return _eventMonitor.GetEventRecords(count);
        }

        /// <summary>
        /// 获取事件统计信息
        /// </summary>
        /// <returns>事件统计信息</returns>
        public EventStatistics GetEventStatistics()
        {
            return _eventMonitor.GetEventStatistics();
        }

        /// <summary>
        /// 获取性能快照列表
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>性能快照列表</returns>
        public List<PerformanceSnapshot> GetPerformanceSnapshots(int count = 100)
        {
            return _performanceMonitor.GetPerformanceSnapshots(count);
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计信息</returns>
        public PerformanceStatistics GetPerformanceStatistics()
        {
            return _performanceMonitor.GetPerformanceStatistics();
        }

        /// <summary>
        /// 清除事件记录
        /// </summary>
        public void ClearEventRecords()
        {
            _eventMonitor.ClearEventRecords();
            NotifyModelDataChanged("EventRecords", null);
        }

        /// <summary>
        /// 清除测试结果
        /// </summary>
        public void ClearTestResults()
        {
            _testCaseManager.ClearTestResults();
            NotifyModelDataChanged("TestResults", null);
        }

        /// <summary>
        /// 清除性能数据
        /// </summary>
        public void ClearPerformanceData()
        {
            _performanceMonitor.ClearPerformanceData();
            NotifyModelDataChanged("PerformanceData", null);
        }

        /// <summary>
        /// 获取测试分类列表
        /// </summary>
        /// <returns>测试分类列表</returns>
        public List<string> GetTestCategories()
        {
            return GetTestCases()
                .Select(tc => tc.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToList();
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        /// <returns>测试报告</returns>
        public TestReport GenerateTestReport()
        {
            var testResults = GetTestResults();
            var eventStats = GetEventStatistics();
            var perfStats = GetPerformanceStatistics();

            var report = new TestReport
            {
                GeneratedTime = DateTime.Now,
                TestSummary = new TestSummary
                {
                    TotalTests = testResults.Count,
                    PassedTests = testResults.Count(r => r.Status == TestStatus.Passed),
                    FailedTests = testResults.Count(r => r.Status == TestStatus.Failed),
                    ErrorTests = testResults.Count(r => r.Status == TestStatus.Error),
                    CancelledTests = testResults.Count(r => r.Status == TestStatus.Cancelled),
                    TotalDuration = TimeSpan.FromTicks(testResults.Sum(r => r.Duration.Ticks)),
                    TestResults = testResults
                },
                EventStatistics = eventStats,
                PerformanceStatistics = perfStats
            };

            return report;
        }

        // 事件处理方法
        private void OnEventRecordChanged(object sender, EventRecordChangedEventArgs e)
        {
            EventRecordChanged?.Invoke(this, e);
        }

        private void OnTestProgressChanged(object sender, TestProgressEventArgs e)
        {
            TestProgressChanged?.Invoke(this, e);
        }

        private void OnTestCompleted(object sender, TestCompletedEventArgs e)
        {
            TestCompleted?.Invoke(this, e);
        }

        private void OnPerformanceDataUpdated(object sender, PerformanceDataEventArgs e)
        {
            PerformanceDataUpdated?.Invoke(this, e);
        }

        /// <summary>
        /// 通知模型数据变化
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="value">属性值</param>
        private void NotifyModelDataChanged(string propertyName, object value)
        {
            ModelDataChanged?.Invoke(this, new ModelDataChangedEventArgs(propertyName, value));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 取消事件订阅
                if (_eventMonitor != null)
                {
                    _eventMonitor.EventRecordChanged -= OnEventRecordChanged;
                    _eventMonitor.Dispose();
                }

                if (_testCaseManager != null)
                {
                    _testCaseManager.TestProgressChanged -= OnTestProgressChanged;
                    _testCaseManager.TestCompleted -= OnTestCompleted;
                    _testCaseManager.Dispose();
                }

                if (_performanceMonitor != null)
                {
                    _performanceMonitor.PerformanceDataUpdated -= OnPerformanceDataUpdated;
                    _performanceMonitor.Dispose();
                }

                _logger?.Debug("CommunicationTestModel 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 CommunicationTestModel 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 测试报告
    /// </summary>
    public class TestReport
    {
        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; }

        /// <summary>
        /// 测试摘要
        /// </summary>
        public TestSummary TestSummary { get; set; }

        /// <summary>
        /// 事件统计信息
        /// </summary>
        public EventStatistics EventStatistics { get; set; }

        /// <summary>
        /// 性能统计信息
        /// </summary>
        public PerformanceStatistics PerformanceStatistics { get; set; }
    }

    /// <summary>
    /// 模型数据变化事件参数
    /// </summary>
    public class ModelDataChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; }

        /// <summary>
        /// 属性值
        /// </summary>
        public object Value { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="value">属性值</param>
        public ModelDataChangedEventArgs(string propertyName, object value)
        {
            PropertyName = propertyName;
            Value = value;
        }
    }
}
