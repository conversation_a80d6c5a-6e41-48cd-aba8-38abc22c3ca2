using System;
using System.Linq;
using DeviceModule.Tests.TestFramework;
using static DeviceModule.Tests.TestFramework.SimpleTestFramework;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using Services;
using DeviceModule;
using DeviceModule.Models;
using DeviceModule.Presenters;
using DeviceModule.Services;

namespace DeviceModule.Tests
{
    /// <summary>
    /// DeviceModule简化测试类
    /// </summary>
    /// <remarks>
    /// 基于实际接口的简化测试，验证核心功能
    /// </remarks>
    [TestClass("DeviceModule简化测试")]
    public class DeviceModuleSimpleTests
    {
        private DeviceModuleMain _deviceModule;
        private DeviceModel _deviceModel;
        private MockDeviceService _deviceService;
        private IEventAggregator _eventAggregator;
        private ILogger _logger;

        /// <summary>
        /// 测试初始化
        /// </summary>
        [TestInitialize]
        public void TestInitialize()
        {
            // 创建模拟的依赖项
            _eventAggregator = new EventAggregator();
            _logger = new TestFramework.SimpleLogger(); // 使用简单的测试日志记录器
            _deviceService = new MockDeviceService();

            // 创建设备模块实例
            _deviceModule = new DeviceModuleMain();

            // 创建设备模型实例 - 正确的参数顺序
            _deviceModel = new DeviceModel(_deviceService, _eventAggregator, _logger);
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            try
            {
                _deviceModule?.Dispose();
                _deviceModel?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试DeviceModuleMain创建
        /// </summary>
        [TestMethod("模块创建测试", "验证DeviceModuleMain能够正确创建")]
        public void DeviceModuleMain_ShouldBeCreated()
        {
            // Assert
            Assert.IsNotNull(_deviceModule, "设备模块应该能够创建");
            Assert.AreEqual("设备监控", _deviceModule.Name, "模块名称应该正确");
            Assert.IsNotNull(_deviceModule.Description, "模块描述不应该为空");
        }

        /// <summary>
        /// 测试DeviceModel创建
        /// </summary>
        [TestMethod("设备模型创建测试", "验证DeviceModel能够正确创建")]
        public void DeviceModel_ShouldBeCreated()
        {
            // Assert
            Assert.IsNotNull(_deviceModel, "设备模型应该能够创建");
            var devices = _deviceModel.GetDevices();
            Assert.IsNotNull(devices, "设备列表应该被初始化");
        }

        /// <summary>
        /// 测试MockDeviceService创建
        /// </summary>
        [TestMethod("设备服务创建测试", "验证MockDeviceService能够正确创建")]
        public void MockDeviceService_ShouldBeCreated()
        {
            // Assert
            Assert.IsNotNull(_deviceService, "设备服务应该能够创建");
            var devices = _deviceService.GetDevices();
            Assert.IsNotNull(devices, "设备列表应该被初始化");
            Assert.AreEqual(5, devices.Count, "应该有5个预定义设备");
        }

        /// <summary>
        /// 测试设备列表加载
        /// </summary>
        [TestMethod("设备列表加载测试", "验证能够正确加载设备列表")]
        public void LoadDeviceList_ShouldWork()
        {
            // Act
            _deviceModel.LoadDeviceList();

            // Assert
            var devices = _deviceModel.GetDevices();
            Assert.IsTrue(devices.Count > 0, "应该加载到设备");
            Assert.AreEqual(5, devices.Count, "应该加载5个设备");
        }

        /// <summary>
        /// 测试设备连接
        /// </summary>
        [TestMethod("设备连接测试", "验证能够正确连接设备")]
        public void ConnectDevice_ShouldWork()
        {
            // Arrange
            _deviceModel.LoadDeviceList();
            var devices = _deviceModel.GetDevices();
            var device = devices.First();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModel.ConnectDevice(device.DeviceId);
                Assert.IsTrue(true, "连接设备应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"连接设备不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试设备断开
        /// </summary>
        [TestMethod("设备断开测试", "验证能够正确断开设备")]
        public void DisconnectDevice_ShouldWork()
        {
            // Arrange
            _deviceModel.LoadDeviceList();
            var devices = _deviceModel.GetDevices();
            var device = devices.First();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModel.DisconnectDevice(device.DeviceId);
                Assert.IsTrue(true, "断开设备应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"断开设备不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试监控启动
        /// </summary>
        [TestMethod("监控启动测试", "验证能够正确启动监控")]
        public void StartMonitoring_ShouldWork()
        {
            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModel.StartMonitoring();
                Assert.IsTrue(true, "启动监控应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"启动监控不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试监控停止
        /// </summary>
        [TestMethod("监控停止测试", "验证能够正确停止监控")]
        public void StopMonitoring_ShouldWork()
        {
            // Arrange
            _deviceModel.StartMonitoring();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModel.StopMonitoring();
                Assert.IsTrue(true, "停止监控应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"停止监控不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块依赖注入
        /// </summary>
        [TestMethod("模块依赖注入测试", "验证模块依赖注入功能")]
        public void ModuleDependencyInjection_ShouldWork()
        {
            // Arrange & Act
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;

            // Assert
            Assert.IsNotNull(_deviceModule.EventAggregator, "EventAggregator应该被正确注入");
            Assert.IsNotNull(_deviceModule.Logger, "Logger应该被正确注入");
            Assert.AreSame(_eventAggregator, _deviceModule.EventAggregator, "EventAggregator应该是同一个实例");
            Assert.AreSame(_logger, _deviceModule.Logger, "Logger应该是同一个实例");
        }

        /// <summary>
        /// 测试模块初始化
        /// </summary>
        [TestMethod("模块初始化测试", "验证模块能够正确初始化")]
        public void ModuleInitialize_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModule.Initialize();
                Assert.IsTrue(true, "模块初始化应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"模块初始化不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块启动
        /// </summary>
        [TestMethod("模块启动测试", "验证模块能够正确启动")]
        public void ModuleStart_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;
            _deviceModule.Initialize();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModule.Start();
                Assert.IsTrue(true, "模块启动应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"模块启动不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试获取用户控件
        /// </summary>
        [TestMethod("获取用户控件测试", "验证能够获取有效的用户控件")]
        public void GetUserControl_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;
            _deviceModule.Initialize();

            // Act
            var userControl = _deviceModule.GetUserControl();

            // Assert
            Assert.IsNotNull(userControl, "应该返回有效的用户控件");
        }

        /// <summary>
        /// 测试设备服务基本功能
        /// </summary>
        [TestMethod("设备服务基本功能测试", "验证设备服务的基本功能")]
        public void DeviceServiceBasicFunctions_ShouldWork()
        {
            // Act
            var devices = _deviceService.GetDevices();
            var firstDevice = devices.First();

            // Assert
            Assert.IsNotNull(devices, "设备列表不应该为null");
            Assert.AreEqual(5, devices.Count, "应该有5个设备");
            Assert.IsNotNull(firstDevice, "第一个设备不应该为null");
            Assert.IsNotNull(firstDevice.Id, "设备ID不应该为null");
            Assert.IsNotNull(firstDevice.Name, "设备名称不应该为null");

            // 测试设备连接
            var connectResult = _deviceService.ConnectDevice(firstDevice.Id);
            Assert.IsTrue(connectResult, "连接设备应该成功");

            // 测试设备断开
            var disconnectResult = _deviceService.DisconnectDevice(firstDevice.Id);
            Assert.IsTrue(disconnectResult, "断开设备应该成功");
        }

        /// <summary>
        /// 测试完整的模块生命周期
        /// </summary>
        [TestMethod("模块生命周期测试", "验证完整的模块生命周期")]
        public void ModuleLifecycle_ShouldWork()
        {
            // Act & Assert - 完整的生命周期应该正常工作
            try
            {
                // 依赖注入
                _deviceModule.EventAggregator = _eventAggregator;
                _deviceModule.Logger = _logger;
                
                // 初始化
                _deviceModule.Initialize();
                
                // 启动
                _deviceModule.Start();
                
                // 获取用户控件
                var control = _deviceModule.GetUserControl();
                Assert.IsNotNull(control, "生命周期中应该能够获取用户控件");
                
                // 停止
                _deviceModule.Stop();
                
                // 释放
                _deviceModule.Dispose();
                
                Assert.IsTrue(true, "完整的模块生命周期应该正常工作");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"模块生命周期不应该抛出异常: {ex.Message}");
            }
        }
    }
}
