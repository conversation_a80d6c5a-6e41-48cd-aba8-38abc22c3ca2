# 工业HMI框架卸载脚本
# Industrial HMI Framework Uninstall Script
# 版本: 1.0.0
# 日期: 2025-08-13

param(
    [Parameter(Mandatory=$false)]
    [string]$InstallPath = "C:\IndustrialHMI",
    
    [Parameter(Mandatory=$false)]
    [switch]$RemoveUserData = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    if ($Verbose) {
        Add-Content -Path "$PSScriptRoot\uninstall.log" -Value $logMessage
    }
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 停止应用程序进程
function Stop-Application {
    Write-Log "检查并停止应用程序进程..."
    
    $processes = Get-Process -Name "IndustrialHMI" -ErrorAction SilentlyContinue
    if ($processes) {
        Write-Log "发现 $($processes.Count) 个运行中的进程"
        
        foreach ($process in $processes) {
            try {
                Write-Log "停止进程 ID: $($process.Id)"
                $process.CloseMainWindow()
                
                # 等待进程正常退出
                if (!$process.WaitForExit(5000)) {
                    Write-Log "强制终止进程 ID: $($process.Id)"
                    $process.Kill()
                }
                
                Write-Log "进程已停止: $($process.Id)"
            } catch {
                Write-Log "停止进程失败: $($_.Exception.Message)" "WARNING"
            }
        }
    } else {
        Write-Log "没有发现运行中的应用程序进程"
    }
}

# 删除桌面快捷方式
function Remove-DesktopShortcut {
    $shortcutPath = Join-Path ([Environment]::GetFolderPath("Desktop")) "工业HMI框架.lnk"
    
    if (Test-Path $shortcutPath) {
        Remove-Item $shortcutPath -Force
        Write-Log "删除桌面快捷方式: $shortcutPath"
    } else {
        Write-Log "桌面快捷方式不存在"
    }
}

# 删除开始菜单项
function Remove-StartMenuItems {
    $startMenuPath = Join-Path ([Environment]::GetFolderPath("CommonPrograms")) "工业HMI框架"
    
    if (Test-Path $startMenuPath) {
        Remove-Item $startMenuPath -Recurse -Force
        Write-Log "删除开始菜单项: $startMenuPath"
    } else {
        Write-Log "开始菜单项不存在"
    }
}

# 备份用户数据
function Backup-UserData {
    param([string]$InstallPath)
    
    $backupPath = Join-Path ([Environment]::GetFolderPath("MyDocuments")) "IndustrialHMI_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    
    $dataFolders = @(
        Join-Path $InstallPath "config",
        Join-Path $InstallPath "logs"
    )
    
    $hasData = $false
    foreach ($folder in $dataFolders) {
        if (Test-Path $folder) {
            $hasData = $true
            break
        }
    }
    
    if ($hasData) {
        Write-Log "备份用户数据到: $backupPath"
        New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        
        foreach ($folder in $dataFolders) {
            if (Test-Path $folder) {
                $folderName = Split-Path $folder -Leaf
                $targetFolder = Join-Path $backupPath $folderName
                Copy-Item $folder $targetFolder -Recurse -Force
                Write-Log "备份文件夹: $folderName"
            }
        }
        
        Write-Log "用户数据备份完成"
        return $backupPath
    } else {
        Write-Log "没有发现用户数据需要备份"
        return $null
    }
}

# 删除安装文件
function Remove-InstallationFiles {
    param([string]$InstallPath)
    
    if (!(Test-Path $InstallPath)) {
        Write-Log "安装路径不存在: $InstallPath"
        return
    }
    
    Write-Log "删除安装文件: $InstallPath"
    
    try {
        # 如果不删除用户数据，先备份
        $backupPath = $null
        if (!$RemoveUserData) {
            $backupPath = Backup-UserData -InstallPath $InstallPath
        }
        
        # 删除安装目录
        Remove-Item $InstallPath -Recurse -Force
        Write-Log "安装文件删除完成"
        
        if ($backupPath) {
            Write-Log "用户数据已备份到: $backupPath"
        }
        
    } catch {
        Write-Log "删除安装文件失败: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 清理注册表项
function Remove-RegistryEntries {
    Write-Log "清理注册表项..."
    
    $registryPaths = @(
        "HKLM:\SOFTWARE\IndustrialHMI",
        "HKCU:\SOFTWARE\IndustrialHMI"
    )
    
    foreach ($path in $registryPaths) {
        if (Test-Path $path) {
            Remove-Item $path -Recurse -Force
            Write-Log "删除注册表项: $path"
        }
    }
}

# 主卸载流程
function Start-Uninstallation {
    Write-Log "开始卸载工业HMI框架..."
    Write-Log "安装路径: $InstallPath"
    
    # 检查管理员权限
    if (!(Test-Administrator)) {
        Write-Log "需要管理员权限来执行卸载" "ERROR"
        exit 1
    }
    
    # 确认卸载
    if (!$Force) {
        Write-Host "即将卸载工业HMI框架" -ForegroundColor Yellow
        Write-Host "安装路径: $InstallPath" -ForegroundColor Yellow
        
        if ($RemoveUserData) {
            Write-Host "警告: 将删除所有用户数据（配置文件、日志等）" -ForegroundColor Red
        } else {
            Write-Host "用户数据将被备份到文档文件夹" -ForegroundColor Green
        }
        
        $response = Read-Host "确认继续卸载？(Y/N)"
        if ($response -ne "Y" -and $response -ne "y") {
            Write-Log "用户取消卸载操作"
            exit 0
        }
    }
    
    try {
        # 停止应用程序
        Stop-Application
        
        # 删除快捷方式
        Remove-DesktopShortcut
        Remove-StartMenuItems
        
        # 删除安装文件
        Remove-InstallationFiles -InstallPath $InstallPath
        
        # 清理注册表
        Remove-RegistryEntries
        
        Write-Log "卸载完成！" "SUCCESS"
        
    } catch {
        Write-Log "卸载失败: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
工业HMI框架卸载脚本

用法:
    .\Uninstall.ps1 [-InstallPath <路径>] [-RemoveUserData] [-Force] [-Verbose]

参数:
    -InstallPath     安装路径 (默认: C:\IndustrialHMI)
    -RemoveUserData  删除用户数据 (默认: false，会备份到文档文件夹)
    -Force           强制卸载，不询问确认 (默认: false)
    -Verbose         详细日志输出 (默认: false)

示例:
    .\Uninstall.ps1
    .\Uninstall.ps1 -InstallPath "D:\MyHMI" -Verbose
    .\Uninstall.ps1 -RemoveUserData -Force

"@
}

# 主入口
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    exit 0
}

Write-Host "工业HMI框架卸载脚本 v1.0.0" -ForegroundColor Red
Write-Host "==============================" -ForegroundColor Red

Start-Uninstallation

Write-Host "卸载脚本执行完成。" -ForegroundColor Red
