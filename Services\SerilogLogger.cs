using System;
using System.IO;
using Contracts;
using Serilog;
using Serilog.Events;

namespace Services
{
    /// <summary>
    /// Serilog日志记录器实现
    /// </summary>
    /// <remarks>
    /// 基于Serilog框架的日志记录器实现，支持文件轮转、多种输出目标和结构化日志
    /// </remarks>
    public class SerilogLogger : Contracts.ILogger, IDisposable
    {
        private readonly Serilog.ILogger _logger;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        public SerilogLogger(string logFilePath = null)
        {
            // 如果没有指定日志路径，使用默认路径（相对于应用程序目录）
            if (string.IsNullOrEmpty(logFilePath))
            {
                var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                logFilePath = Path.Combine(appDirectory, "logs", "application.log");
            }

            // 确保日志目录存在
            var logDirectory = Path.GetDirectoryName(logFilePath);
            if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // 配置Serilog
            _logger = CreateLogger(logFilePath);

            // 设置全局日志记录器
            Log.Logger = _logger;
        }

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        /// <returns>配置好的日志记录器</returns>
        private static Serilog.ILogger CreateLogger(string logFilePath)
        {
            var configuration = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("System", LogEventLevel.Information)
                .MinimumLevel.Override("DryIoc", LogEventLevel.Warning)
                .Enrich.WithProperty("Application", "IndustrialHMI")
                .Enrich.WithProperty("Version", GetApplicationVersion())
                .Enrich.WithProperty("MachineName", Environment.MachineName)
                .Enrich.WithProperty("ProcessId", System.Diagnostics.Process.GetCurrentProcess().Id)
                .WriteTo.Console(
                    outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .WriteTo.File(
                    path: logFilePath,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] [{Application}] [{MachineName}] [{ProcessId}] {Message:lj}{NewLine}{Exception}",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
                    rollOnFileSizeLimit: true,
                    shared: true,
                    flushToDiskInterval: TimeSpan.FromSeconds(1),
                    encoding: System.Text.Encoding.UTF8);

            return configuration.CreateLogger();
        }

        /// <summary>
        /// 获取应用程序版本
        /// </summary>
        /// <returns>应用程序版本字符串</returns>
        private static string GetApplicationVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetEntryAssembly() ?? System.Reflection.Assembly.GetExecutingAssembly();
                return assembly.GetName().Version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Debug(string message)
        {
            if (_disposed) return;
            _logger.Debug(message);
        }

        /// <summary>
        /// 记录调试信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Debug(string message, Exception exception)
        {
            if (_disposed) return;
            _logger.Debug(exception, message);
        }

        /// <summary>
        /// 记录一般信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Info(string message)
        {
            if (_disposed) return;
            _logger.Information(message);
        }

        /// <summary>
        /// 记录一般信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Info(string message, Exception exception)
        {
            if (_disposed) return;
            _logger.Information(exception, message);
        }

        /// <summary>
        /// 记录警告信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Warning(string message)
        {
            if (_disposed) return;
            _logger.Warning(message);
        }

        /// <summary>
        /// 记录警告信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Warning(string message, Exception exception)
        {
            if (_disposed) return;
            _logger.Warning(exception, message);
        }

        /// <summary>
        /// 记录错误信息
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Error(string message)
        {
            if (_disposed) return;
            _logger.Error(message);
        }

        /// <summary>
        /// 记录错误信息（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Error(string message, Exception exception)
        {
            if (_disposed) return;
            _logger.Error(exception, message);
        }

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">日志消息</param>
        public void Fatal(string message)
        {
            if (_disposed) return;
            _logger.Fatal(message);
        }

        /// <summary>
        /// 记录致命错误（带异常）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public void Fatal(string message, Exception exception)
        {
            if (_disposed) return;
            _logger.Fatal(exception, message);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    // 刷新并关闭Serilog
                    if (_logger is IDisposable disposableLogger)
                    {
                        disposableLogger.Dispose();
                    }
                    Log.CloseAndFlush();
                }
                catch (Exception)
                {
                    // 忽略释放过程中的异常
                }
                finally
                {
                    _disposed = true;
                }
            }
        }
    }
}
