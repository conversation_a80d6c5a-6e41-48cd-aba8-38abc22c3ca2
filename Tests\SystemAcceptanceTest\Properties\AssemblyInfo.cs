using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("SystemAcceptanceTest")]
[assembly: AssemblyDescription("工业HMI框架系统验收测试")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Industrial HMI Framework")]
[assembly: AssemblyCopyright("Copyright ©  2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]
[assembly: Guid("f1e2d3c4-b5a6-7890-1234-567890abcdef")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
