using System;
using System.Collections.Generic;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using Services;
using DryIoc;

namespace Shell.Bootstrapper
{
    /// <summary>
    /// DryIoc容器配置和管理
    /// </summary>
    public static class ContainerBootstrapper
    {

        /// <summary>
        /// 创建和配置DryIoc容器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <returns>配置好的DryIoc容器实例</returns>
        public static IContainer CreateContainer(ILogger logger = null)
        {
            logger?.Info("开始创建DryIoc容器");

            try
            {
                // 创建DryIoc容器，配置属性注入
                var container = new Container(Rules.Default
                    .With(propertiesAndFields: PropertiesAndFields.Properties())
                    .WithFactorySelector(Rules.SelectLastRegisteredFactory())
                    .WithTrackingDisposableTransients()
                );

                logger?.Debug("DryIoc容器创建成功，开始注册服务");

                // 注册日志记录器为单例
                if (logger != null)
                {
                    container.RegisterInstance<ILogger>(logger);
                    logger.Debug("注册自定义日志记录器为单例");
                }
                else
                {
                    var loggerInstance = new SerilogLogger();
                    container.RegisterInstance<ILogger>(loggerInstance);
                    logger?.Debug("注册SerilogLogger为单例");
                }

                // 注册事件聚合器为单例
                var eventAggregator = new EventAggregator();
                container.RegisterInstance<IEventAggregator>(eventAggregator);
                logger?.Debug("注册EventAggregator为单例");

                // 注册配置服务为单例
                var configService = new ConfigurationService();

                // 添加配置源
                // 1. 环境变量配置源（最高优先级）
                configService.AddSource(new EnvironmentConfigurationSource("INDUSTRIAL_HMI", 100, true, "Environment"));

                // 2. 自定义配置文件（中等优先级）
                var customConfigPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "application.config");
                configService.AddSource(new FileConfigurationSource(customConfigPath, 50, true, "CustomConfig"));

                // 3. 默认配置文件（最低优先级）
                var defaultConfigPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "IndustrialHMI.exe.config");
                configService.AddSource(new FileConfigurationSource(defaultConfigPath, 10, false, "DefaultConfig"));

                // 启用热更新
                configService.EnableHotReload();

                container.RegisterInstance<IConfigurationService>(configService);
                logger?.Debug("注册ConfigurationService为单例，已添加多个配置源并启用热更新");

                // 注册模块加载器为单例
                var moduleLoader = new ModuleLoader(
                    container.Resolve<IEventAggregator>(),
                    container.Resolve<ILogger>(),
                    container);
                container.RegisterInstance<ModuleLoader>(moduleLoader);
                logger?.Debug("注册ModuleLoader为单例（支持DryIoc依赖注入）");

                // 注册主窗体为单例
                var mainForm = new MainForm();
                container.RegisterInstance<MainForm>(mainForm);
                logger?.Debug("注册MainForm为单例");

                // 验证容器配置
                ValidateContainer(container, logger);

                logger?.Info("DryIoc容器创建和配置完成");
                return container;
            }
            catch (Exception ex)
            {
                logger?.Error("创建DryIoc容器失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 验证容器配置
        /// </summary>
        /// <param name="container">容器实例</param>
        /// <param name="logger">日志记录器</param>
        private static void ValidateContainer(IContainer container, ILogger logger)
        {
            logger?.Debug("开始验证DryIoc容器配置");

            try
            {
                // 验证核心服务是否可以正确解析
                var eventAggregator = container.Resolve<IEventAggregator>();
                if (eventAggregator == null)
                {
                    throw new InvalidOperationException("无法解析IEventAggregator");
                }

                var configService = container.Resolve<IConfigurationService>();
                if (configService == null)
                {
                    throw new InvalidOperationException("无法解析IConfigurationService");
                }

                var moduleLoader = container.Resolve<ModuleLoader>();
                if (moduleLoader == null)
                {
                    throw new InvalidOperationException("无法解析ModuleLoader");
                }

                var mainForm = container.Resolve<MainForm>();
                if (mainForm == null)
                {
                    throw new InvalidOperationException("无法解析MainForm");
                }

                logger?.Debug("DryIoc容器配置验证通过");
            }
            catch (Exception ex)
            {
                logger?.Error("DryIoc容器配置验证失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 注册额外的服务
        /// </summary>
        /// <param name="container">容器实例</param>
        /// <param name="serviceType">服务类型</param>
        /// <param name="implementationType">实现类型</param>
        /// <param name="reuse">重用策略</param>
        /// <param name="logger">日志记录器</param>
        public static void RegisterService(IContainer container, Type serviceType, Type implementationType,
            IReuse reuse = null, ILogger logger = null)
        {
            if (container == null) throw new ArgumentNullException(nameof(container));
            if (serviceType == null) throw new ArgumentNullException(nameof(serviceType));
            if (implementationType == null) throw new ArgumentNullException(nameof(implementationType));

            try
            {
                container.Register(serviceType, implementationType, reuse ?? Reuse.Transient);
                logger?.Debug($"注册服务: {serviceType.Name} -> {implementationType.Name}");
            }
            catch (Exception ex)
            {
                logger?.Error($"注册服务失败: {serviceType.Name} -> {implementationType.Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 注册服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="container">容器实例</param>
        /// <param name="instance">服务实例</param>
        /// <param name="ifAlreadyRegistered">如果已注册的处理方式</param>
        /// <param name="logger">日志记录器</param>
        public static void RegisterInstance<T>(IContainer container, T instance, IfAlreadyRegistered? ifAlreadyRegistered = null, ILogger logger = null)
        {
            if (container == null) throw new ArgumentNullException(nameof(container));
            if (instance == null) throw new ArgumentNullException(nameof(instance));

            try
            {
                container.RegisterInstance<T>(instance, ifAlreadyRegistered);
                logger?.Debug($"注册服务实例: {typeof(T).Name}");
            }
            catch (Exception ex)
            {
                logger?.Error($"注册服务实例失败: {typeof(T).Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 配置DryIoc容器规则
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <returns>配置好的容器规则</returns>
        public static Rules ConfigureContainerRules(ILogger logger = null)
        {
            logger?.Debug("配置DryIoc容器规则");

            return Rules.Default
                // 启用属性注入
                .With(propertiesAndFields: PropertiesAndFields.Properties())
                // 选择最后注册的工厂
                .WithFactorySelector(Rules.SelectLastRegisteredFactory())
                // 跟踪可释放的瞬态对象
                .WithTrackingDisposableTransients()
                // 启用循环依赖检测
                .WithoutThrowOnRegisteringDisposableTransient()
                // 默认重用策略
                .WithDefaultReuse(Reuse.Transient);
        }
    }


}
