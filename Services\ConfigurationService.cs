using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Xml;
using Contracts.Services;

namespace Services
{
    /// <summary>
    /// 配置服务实现
    /// </summary>
    /// <remarks>
    /// 基于app.config文件的配置服务实现，
    /// 支持运行时配置修改和保存
    /// </remarks>
    public class ConfigurationService : IConfigurationService
    {
        private readonly Dictionary<string, object> _configCache = new Dictionary<string, object>();
        private readonly List<IConfigurationSource> _sources = new List<IConfigurationSource>();
        private readonly object _lock = new object();
        private readonly string _configFilePath;
        private bool _hotReloadEnabled = false;

        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// 配置文件路径
        /// </summary>
        public string ConfigFilePath => _configFilePath;

        /// <summary>
        /// 是否启用热更新
        /// </summary>
        public bool IsHotReloadEnabled => _hotReloadEnabled;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationService()
        {
            _configFilePath = AppDomain.CurrentDomain.SetupInformation.ConfigurationFile;
            LoadConfiguration();
        }

        /// <summary>
        /// 获取字符串配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public string GetString(string key, string defaultValue = "")
        {
            return GetValue(key, defaultValue);
        }

        /// <summary>
        /// 获取整数配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public int GetInt(string key, int defaultValue = 0)
        {
            var value = GetString(key);
            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        /// <summary>
        /// 获取布尔配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public bool GetBool(string key, bool defaultValue = false)
        {
            var value = GetString(key);
            return bool.TryParse(value, out bool result) ? result : defaultValue;
        }

        /// <summary>
        /// 获取双精度浮点配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public double GetDouble(string key, double defaultValue = 0.0)
        {
            var value = GetString(key);
            return double.TryParse(value, out double result) ? result : defaultValue;
        }

        /// <summary>
        /// 获取泛型配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetValue<T>(string key, T defaultValue = default(T))
        {
            lock (_lock)
            {
                if (_configCache.TryGetValue(key, out object value))
                {
                    try
                    {
                        return (T)Convert.ChangeType(value, typeof(T));
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }

                // 尝试从app.config读取
                var configValue = ConfigurationManager.AppSettings[key];
                if (configValue != null)
                {
                    try
                    {
                        var convertedValue = (T)Convert.ChangeType(configValue, typeof(T));
                        _configCache[key] = convertedValue;
                        return convertedValue;
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }

                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetValue(string key, object value)
        {
            lock (_lock)
            {
                var oldValue = _configCache.ContainsKey(key) ? _configCache[key] : null;
                _configCache[key] = value;

                // 触发配置变更事件
                ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(key, oldValue, value));
            }
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            lock (_lock)
            {
                return _configCache.ContainsKey(key) || ConfigurationManager.AppSettings[key] != null;
            }
        }

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveKey(string key)
        {
            lock (_lock)
            {
                return _configCache.Remove(key);
            }
        }

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键列表</returns>
        public IEnumerable<string> GetAllKeys()
        {
            lock (_lock)
            {
                var keys = new HashSet<string>(_configCache.Keys);
                foreach (string key in ConfigurationManager.AppSettings.AllKeys)
                {
                    keys.Add(key);
                }
                return keys.ToList();
            }
        }

        /// <summary>
        /// 获取指定前缀的所有配置
        /// </summary>
        /// <param name="prefix">键前缀</param>
        /// <returns>配置字典</returns>
        public Dictionary<string, object> GetSection(string prefix)
        {
            lock (_lock)
            {
                var result = new Dictionary<string, object>();

                foreach (var kvp in _configCache.Where(x => x.Key.StartsWith(prefix)))
                {
                    result[kvp.Key] = kvp.Value;
                }

                foreach (string key in ConfigurationManager.AppSettings.AllKeys.Where(k => k.StartsWith(prefix)))
                {
                    if (!result.ContainsKey(key))
                    {
                        result[key] = ConfigurationManager.AppSettings[key];
                    }
                }

                return result;
            }
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <returns>是否保存成功</returns>
        public bool Save()
        {
            try
            {
                lock (_lock)
                {
                    var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                    foreach (var kvp in _configCache)
                    {
                        if (config.AppSettings.Settings[kvp.Key] != null)
                        {
                            config.AppSettings.Settings[kvp.Key].Value = kvp.Value?.ToString();
                        }
                        else
                        {
                            config.AppSettings.Settings.Add(kvp.Key, kvp.Value?.ToString());
                        }
                    }

                    config.Save(ConfigurationSaveMode.Modified);
                    ConfigurationManager.RefreshSection("appSettings");
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从文件重新加载配置
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool Reload()
        {
            try
            {
                ConfigurationManager.RefreshSection("appSettings");
                LoadConfiguration();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            lock (_lock)
            {
                _configCache.Clear();
                foreach (string key in ConfigurationManager.AppSettings.AllKeys)
                {
                    _configCache[key] = ConfigurationManager.AppSettings[key];
                }

                // 从配置源加载配置
                foreach (var source in _sources.OrderByDescending(s => s.Priority))
                {
                    try
                    {
                        var configurations = source.Load();
                        if (configurations != null)
                        {
                            foreach (var kvp in configurations)
                            {
                                _configCache[kvp.Key] = kvp.Value;
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // 忽略加载错误，继续处理其他配置源
                    }
                }
            }
        }

        /// <summary>
        /// 添加配置源
        /// </summary>
        /// <param name="source">配置源</param>
        public void AddSource(IConfigurationSource source)
        {
            if (source == null) return;

            lock (_lock)
            {
                _sources.Add(source);

                // 如果启用了热更新，开始监控新的配置源
                if (_hotReloadEnabled && source.SupportsHotReload)
                {
                    source.Changed += OnSourceChanged;
                    source.StartWatching();
                }

                // 重新加载配置
                LoadConfiguration();
            }
        }

        /// <summary>
        /// 移除配置源
        /// </summary>
        /// <param name="source">配置源</param>
        public void RemoveSource(IConfigurationSource source)
        {
            if (source == null) return;

            lock (_lock)
            {
                if (_sources.Remove(source))
                {
                    // 停止监控
                    source.Changed -= OnSourceChanged;
                    source.StopWatching();
                    source.Dispose();

                    // 重新加载配置
                    LoadConfiguration();
                }
            }
        }

        /// <summary>
        /// 获取所有配置源
        /// </summary>
        /// <returns>配置源列表</returns>
        public IEnumerable<IConfigurationSource> GetSources()
        {
            lock (_lock)
            {
                return _sources.ToList();
            }
        }

        /// <summary>
        /// 启用热更新监控
        /// </summary>
        public void EnableHotReload()
        {
            lock (_lock)
            {
                if (_hotReloadEnabled) return;

                _hotReloadEnabled = true;
                foreach (var source in _sources.Where(s => s.SupportsHotReload))
                {
                    source.Changed += OnSourceChanged;
                    source.StartWatching();
                }
            }
        }

        /// <summary>
        /// 禁用热更新监控
        /// </summary>
        public void DisableHotReload()
        {
            lock (_lock)
            {
                if (!_hotReloadEnabled) return;

                _hotReloadEnabled = false;
                foreach (var source in _sources.Where(s => s.SupportsHotReload))
                {
                    source.Changed -= OnSourceChanged;
                    source.StopWatching();
                }
            }
        }

        /// <summary>
        /// 配置源变更事件处理
        /// </summary>
        private void OnSourceChanged(object sender, ConfigurationChangedEventArgs e)
        {
            lock (_lock)
            {
                // 重新加载配置
                LoadConfiguration();

                // 触发配置变更事件
                ConfigurationChanged?.Invoke(this, e);
            }
        }
    }
}
