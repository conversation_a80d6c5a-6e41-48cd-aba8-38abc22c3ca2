using System;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using AlarmModule.Models;
using AlarmModule.Presenters;
using AlarmModule.Services;
using AlarmModule.Views;

namespace AlarmModule
{
    /// <summary>
    /// 报警管理模块主类
    /// </summary>
    /// <remarks>
    /// 实现报警接收、显示、确认和历史记录功能的模块
    /// </remarks>
    public class AlarmModuleMain : IModule
    {
        private AlarmView _view;
        private AlarmPresenter _presenter;
        private AlarmModel _model;
        private IAlarmService _alarmService;
        private bool _disposed = false;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string Name => "报警管理";

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description => "实时接收和管理系统报警，提供报警确认、清除和历史记录功能";

        /// <summary>
        /// 模块版本
        /// </summary>
        public string Version => "1.0.0";

        /// <summary>
        /// 事件聚合器（由ModuleLoader注入）
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器（由ModuleLoader注入）
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public ILogger Logger { get; set; }

        /// <summary>
        /// 配置服务（由ModuleLoader注入）
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public IConfigurationService ConfigurationService { get; set; }

        /// <summary>
        /// 无参构造函数
        /// </summary>
        /// <remarks>模块加载器要求必须有无参构造函数</remarks>
        public AlarmModuleMain()
        {
            // 模块加载器要求的无参构造函数
        }

        /// <summary>
        /// 模块初始化
        /// </summary>
        /// <remarks>
        /// 验证依赖注入，创建MVP组件，订阅系统事件
        /// </remarks>
        public void Initialize()
        {
            try
            {
                // 验证依赖注入
                if (EventAggregator == null)
                    throw new InvalidOperationException("EventAggregator 未注入");
                if (Logger == null)
                    throw new InvalidOperationException("Logger 未注入");

                Logger.Info("开始初始化报警管理模块");

                // 创建报警服务（使用模拟实现）
                _alarmService = new MockAlarmService();
                Logger.Debug("报警服务创建完成");

                // 创建MVP组件
                CreateMVPComponents();

                // 订阅系统事件
                SubscribeToSystemEvents();

                Logger.Info("报警管理模块初始化完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("初始化报警管理模块时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 创建MVP组件
        /// </summary>
        private void CreateMVPComponents()
        {
            try
            {
                // 创建视图
                _view = new AlarmView();
                Logger.Debug("报警视图创建完成");

                // 创建模型
                _model = new AlarmModel(_alarmService, EventAggregator, Logger);
                Logger.Debug("报警模型创建完成");

                // 创建表示器
                _presenter = new AlarmPresenter(_view, _model, Logger);
                Logger.Debug("报警表示器创建完成");

                Logger.Info("MVP组件创建完成");
            }
            catch (Exception ex)
            {
                Logger.Error("创建MVP组件时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 订阅系统事件
        /// </summary>
        private void SubscribeToSystemEvents()
        {
            try
            {
                // 订阅系统启动事件
                EventAggregator.GetEvent<SystemStartupEvent>()
                    .Subscribe(OnSystemStartup, ThreadOption.UIThread, false);

                // 订阅系统关闭事件
                EventAggregator.GetEvent<SystemShutdownEvent>()
                    .Subscribe(OnSystemShutdown, ThreadOption.UIThread, false);

                // 订阅模块加载事件
                EventAggregator.GetEvent<ModuleLoadedEvent>()
                    .Subscribe(OnModuleLoaded, ThreadOption.UIThread, false);

                // 订阅模块卸载事件
                EventAggregator.GetEvent<ModuleUnloadedEvent>()
                    .Subscribe(OnModuleUnloaded, ThreadOption.UIThread, false);

                // 订阅设备数据更新事件（用于报警规则评估）
                EventAggregator.GetEvent<DeviceDataUpdateEvent>()
                    .Subscribe(OnDeviceDataUpdate, ThreadOption.UIThread, false);

                // 订阅设备连接事件（用于连接状态报警）
                EventAggregator.GetEvent<DeviceConnectionEvent>()
                    .Subscribe(OnDeviceConnectionChanged, ThreadOption.UIThread, false);

                Logger.Debug("系统事件订阅完成");
            }
            catch (Exception ex)
            {
                Logger.Error("订阅系统事件时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 系统启动事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemStartup(SystemStartupEvent eventData)
        {
            try
            {
                Logger.Info("收到系统启动事件，报警管理模块开始工作");
                
                // 初始化表示器
                _presenter?.Initialize();
                
                Logger.Info("报警管理模块响应系统启动完成");
            }
            catch (Exception ex)
            {
                Logger.Error("处理系统启动事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 系统关闭事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemShutdown(SystemShutdownEvent eventData)
        {
            try
            {
                Logger.Info("收到系统关闭事件，报警管理模块开始清理");
                
                // 停止监控
                _presenter?.StopMonitoring();
                
                Logger.Info("报警管理模块响应系统关闭完成");
            }
            catch (Exception ex)
            {
                Logger.Error("处理系统关闭事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 模块加载事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleLoaded(ModuleLoadedEvent eventData)
        {
            Logger.Debug($"模块已加载: {eventData.ModuleName}");
        }

        /// <summary>
        /// 模块卸载事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleUnloaded(ModuleUnloadedEvent eventData)
        {
            Logger.Debug($"模块已卸载: {eventData.ModuleName}");
        }

        /// <summary>
        /// 设备数据更新事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceDataUpdate(DeviceDataUpdateEvent eventData)
        {
            try
            {
                // 将设备数据传递给报警服务进行规则评估
                var dataInfo = eventData.DataInfo;
                _alarmService?.EvaluateRules(dataInfo.DeviceId, dataInfo.DataPointName, dataInfo.Value);
            }
            catch (Exception ex)
            {
                Logger.Error("处理设备数据更新事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 设备连接状态变化事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceConnectionChanged(DeviceConnectionEvent eventData)
        {
            try
            {
                // 将设备连接状态传递给报警服务进行规则评估
                var status = eventData.Status;
                _alarmService?.EvaluateRules(status.DeviceId, "ConnectionStatus", status.IsConnected);
            }
            catch (Exception ex)
            {
                Logger.Error("处理设备连接状态变化事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取模块的用户控件
        /// </summary>
        /// <returns>用户控件</returns>
        public UserControl GetUserControl()
        {
            return _view;
        }

        /// <summary>
        /// 启动模块
        /// </summary>
        /// <remarks>系统就绪后调用，开始报警监控</remarks>
        public void Start()
        {
            try
            {
                Logger.Info("启动报警管理模块");
                
                // 启动报警监控
                _presenter?.StartMonitoring();
                
                Logger.Info("报警管理模块启动完成");
            }
            catch (Exception ex)
            {
                Logger.Error("启动报警管理模块时发生错误", ex);
            }
        }

        /// <summary>
        /// 停止模块
        /// </summary>
        public void Stop()
        {
            try
            {
                Logger.Info("停止报警管理模块");
                
                // 停止报警监控
                _presenter?.StopMonitoring();
                
                Logger.Info("报警管理模块停止完成");
            }
            catch (Exception ex)
            {
                Logger.Error("停止报警管理模块时发生错误", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Logger?.Info("开始释放报警管理模块资源");

                // 取消系统事件订阅
                EventAggregator?.GetEvent<SystemStartupEvent>()
                    .Unsubscribe(OnSystemStartup);

                EventAggregator?.GetEvent<SystemShutdownEvent>()
                    .Unsubscribe(OnSystemShutdown);

                EventAggregator?.GetEvent<ModuleLoadedEvent>()
                    .Unsubscribe(OnModuleLoaded);

                EventAggregator?.GetEvent<ModuleUnloadedEvent>()
                    .Unsubscribe(OnModuleUnloaded);

                EventAggregator?.GetEvent<DeviceDataUpdateEvent>()
                    .Unsubscribe(OnDeviceDataUpdate);

                EventAggregator?.GetEvent<DeviceConnectionEvent>()
                    .Unsubscribe(OnDeviceConnectionChanged);

                // 释放MVP组件
                _presenter?.Dispose();
                _model?.Dispose();
                _view?.Dispose();

                Logger?.Info("报警管理模块资源释放完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("释放报警管理模块资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
