using System;
using System.Collections.Generic;

namespace Contracts.Events
{
    /// <summary>
    /// 设备连接事件
    /// </summary>
    /// <remarks>
    /// 当设备连接状态发生变化时发布此事件
    /// </remarks>
    public class DeviceConnectionEvent
    {
        /// <summary>
        /// 设备连接状态信息
        /// </summary>
        public DeviceConnectionStatus Status { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="status">设备连接状态</param>
        public DeviceConnectionEvent(DeviceConnectionStatus status)
        {
            Status = status;
        }
    }

    /// <summary>
    /// 设备连接状态
    /// </summary>
    public class DeviceConnectionStatus
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 状态变化时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 错误信息（如果连接失败）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public string DeviceType { get; set; }

        /// <summary>
        /// 连接地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="isConnected">是否已连接</param>
        public DeviceConnectionStatus(string deviceId, string deviceName, bool isConnected)
        {
            DeviceId = deviceId;
            DeviceName = deviceName;
            IsConnected = isConnected;
        }
    }

    /// <summary>
    /// 设备数据更新事件
    /// </summary>
    /// <remarks>
    /// 当设备数据发生变化时发布此事件
    /// </remarks>
    public class DeviceDataUpdateEvent
    {
        /// <summary>
        /// 设备数据信息
        /// </summary>
        public DeviceDataInfo DataInfo { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataInfo">设备数据信息</param>
        public DeviceDataUpdateEvent(DeviceDataInfo dataInfo)
        {
            DataInfo = dataInfo;
        }
    }

    /// <summary>
    /// 设备数据信息
    /// </summary>
    public class DeviceDataInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 数据点名称
        /// </summary>
        public string DataPointName { get; set; }

        /// <summary>
        /// 数据值
        /// </summary>
        public object Value { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public Type DataType { get; set; }

        /// <summary>
        /// 数据质量
        /// </summary>
        public DataQuality Quality { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="dataPointName">数据点名称</param>
        /// <param name="value">数据值</param>
        public DeviceDataInfo(string deviceId, string dataPointName, object value)
        {
            DeviceId = deviceId;
            DataPointName = dataPointName;
            Value = value;
            DataType = value?.GetType();
            Quality = DataQuality.Good;
        }
    }

    /// <summary>
    /// 数据质量枚举
    /// </summary>
    public enum DataQuality
    {
        /// <summary>
        /// 良好
        /// </summary>
        Good,

        /// <summary>
        /// 不确定
        /// </summary>
        Uncertain,

        /// <summary>
        /// 错误
        /// </summary>
        Bad
    }

    /// <summary>
    /// 设备命令事件
    /// </summary>
    /// <remarks>
    /// 用于向设备发送控制命令
    /// </remarks>
    public class DeviceCommandEvent
    {
        /// <summary>
        /// 设备命令信息
        /// </summary>
        public DeviceCommandInfo CommandInfo { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commandInfo">设备命令信息</param>
        public DeviceCommandEvent(DeviceCommandInfo commandInfo)
        {
            CommandInfo = commandInfo;
        }
    }

    /// <summary>
    /// 设备命令信息
    /// </summary>
    public class DeviceCommandInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 命令名称
        /// </summary>
        public string CommandName { get; set; }

        /// <summary>
        /// 命令参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; }

        /// <summary>
        /// 命令ID（用于跟踪命令执行结果）
        /// </summary>
        public string CommandId { get; set; }

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="commandName">命令名称</param>
        public DeviceCommandInfo(string deviceId, string commandName)
        {
            DeviceId = deviceId;
            CommandName = commandName;
            CommandId = Guid.NewGuid().ToString();
            Parameters = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 实时数据事件
    /// </summary>
    /// <remarks>
    /// 用于发布设备的实时数据更新
    /// </remarks>
    public class RealTimeDataEvent
    {
        /// <summary>
        /// 实时数据字典
        /// </summary>
        /// <remarks>键为设备ID或数据点名称，值为对应的数据值</remarks>
        public Dictionary<string, object> Data { get; set; }

        /// <summary>
        /// 数据时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="data">实时数据字典</param>
        public RealTimeDataEvent(Dictionary<string, object> data)
        {
            Data = data ?? new Dictionary<string, object>();
            Timestamp = DateTime.Now;
        }
    }
}
