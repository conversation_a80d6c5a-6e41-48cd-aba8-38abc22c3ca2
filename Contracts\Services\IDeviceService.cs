using System;
using System.Collections.Generic;
using Contracts.Events;

namespace Contracts.Services
{
    /// <summary>
    /// 设备服务接口
    /// </summary>
    /// <remarks>
    /// 提供设备管理、连接控制和数据访问功能
    /// </remarks>
    public interface IDeviceService
    {
        /// <summary>
        /// 获取所有设备列表
        /// </summary>
        /// <returns>设备信息列表</returns>
        /// <remarks>返回系统中配置的所有设备信息</remarks>
        IList<DeviceInfo> GetDevices();

        /// <summary>
        /// 根据设备ID获取设备信息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备信息，如果不存在返回null</returns>
        DeviceInfo GetDevice(string deviceId);

        /// <summary>
        /// 连接设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>连接是否成功</returns>
        /// <remarks>异步连接设备，连接结果通过事件通知</remarks>
        bool ConnectDevice(string deviceId);

        /// <summary>
        /// 断开设备连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>断开是否成功</returns>
        bool DisconnectDevice(string deviceId);

        /// <summary>
        /// 连接所有设备
        /// </summary>
        /// <returns>操作是否成功启动</returns>
        bool ConnectAllDevices();

        /// <summary>
        /// 断开所有设备连接
        /// </summary>
        /// <returns>操作是否成功启动</returns>
        bool DisconnectAllDevices();

        /// <summary>
        /// 获取设备连接状态
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>连接状态</returns>
        bool IsDeviceConnected(string deviceId);

        /// <summary>
        /// 读取设备数据点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="dataPointName">数据点名称</param>
        /// <returns>数据点值</returns>
        object ReadDeviceData(string deviceId, string dataPointName);

        /// <summary>
        /// 写入设备数据点
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="dataPointName">数据点名称</param>
        /// <param name="value">要写入的值</param>
        /// <returns>写入是否成功</returns>
        bool WriteDeviceData(string deviceId, string dataPointName, object value);

        /// <summary>
        /// 发送设备命令
        /// </summary>
        /// <param name="commandInfo">命令信息</param>
        /// <returns>命令是否成功发送</returns>
        bool SendDeviceCommand(DeviceCommandInfo commandInfo);

        /// <summary>
        /// 获取设备实时数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>实时数据字典</returns>
        Dictionary<string, object> GetDeviceRealTimeData(string deviceId);

        /// <summary>
        /// 启动设备监控
        /// </summary>
        /// <remarks>开始监控所有设备的状态和数据变化</remarks>
        void StartMonitoring();

        /// <summary>
        /// 停止设备监控
        /// </summary>
        /// <remarks>停止监控设备状态和数据变化</remarks>
        void StopMonitoring();

        /// <summary>
        /// 添加设备
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>添加是否成功</returns>
        bool AddDevice(DeviceInfo deviceInfo);

        /// <summary>
        /// 移除设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>移除是否成功</returns>
        bool RemoveDevice(string deviceId);

        /// <summary>
        /// 更新设备信息
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>更新是否成功</returns>
        bool UpdateDevice(DeviceInfo deviceInfo);
    }

    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 连接地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 连接端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 当前值（用于显示）
        /// </summary>
        public string CurrentValue { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 设备描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 设备配置参数
        /// </summary>
        public Dictionary<string, object> Configuration { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceInfo()
        {
            Configuration = new Dictionary<string, object>();
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">设备ID</param>
        /// <param name="name">设备名称</param>
        /// <param name="type">设备类型</param>
        /// <param name="address">连接地址</param>
        public DeviceInfo(string id, string name, string type, string address) : this()
        {
            Id = id;
            Name = name;
            Type = type;
            Address = address;
        }
    }
}
