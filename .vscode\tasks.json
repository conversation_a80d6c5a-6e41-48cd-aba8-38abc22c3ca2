{"version": "2.0.0", "tasks": [{"label": "build-x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["IndustrialHMI.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/verbosity:minimal"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "build-tests-x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["Tests/DeviceModule.Tests/DeviceModule.Tests.csproj", "/p:Configuration=Debug", "/p:Platform=x64", "/verbosity:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "build-acceptance-test-x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["Tests/SystemAcceptanceTest/SystemAcceptanceTest.csproj", "/p:Configuration=Debug", "/p:Platform=x64", "/verbosity:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "clean-x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["IndustrialHMI.sln", "/t:Clean", "/p:Configuration=Debug", "/p:Platform=x64", "/verbosity:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "rebuild-x64", "type": "shell", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe", "args": ["IndustrialHMI.sln", "/t:Rebuild", "/p:Configuration=Debug", "/p:Platform=x64", "/verbosity:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "run-device-tests", "type": "shell", "command": "${workspaceFolder}/bin/Debug/Tests/DeviceModule.Tests.exe", "args": [], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "build-tests-x64"}, {"label": "run-acceptance-test", "type": "shell", "command": "${workspaceFolder}/bin/Debug/Tests/SystemAcceptanceTest.exe", "args": [], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "dependsOn": "build-acceptance-test-x64"}]}