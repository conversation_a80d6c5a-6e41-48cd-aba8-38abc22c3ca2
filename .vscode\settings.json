{"dotnet.defaultSolution": "IndustrialHMI.sln", "dotnet.preferCSharpExtension": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/packages": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/packages": true}, "files.associations": {"*.csproj": "xml", "*.sln": "plaintext", "*.config": "xml"}, "editor.formatOnSave": true, "msbuild.configuration": "Debug", "msbuild.platform": "x64"}