﻿[2025-08-13 17:32:54.109 INF] [IndustrialHMI] [LIN-PC] [28180] === 应用程序启动 ===
[2025-08-13 17:32:54.142 INF] [IndustrialHMI] [LIN-PC] [28180] 应用程序版本: 1.0.0.0
[2025-08-13 17:32:54.142 INF] [IndustrialHMI] [LIN-PC] [28180] 启动参数: 
[2025-08-13 17:32:54.143 INF] [IndustrialHMI] [LIN-PC] [28180] 开始初始化应用程序
[2025-08-13 17:32:54.143 INF] [IndustrialHMI] [LIN-PC] [28180] 步骤1: 创建服务容器
[2025-08-13 17:32:54.146 INF] [IndustrialHMI] [LIN-PC] [28180] 开始创建DryIoc容器
[2025-08-13 17:32:54.160 DBG] [IndustrialHMI] [LIN-PC] [28180] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:32:54.164 DBG] [IndustrialHMI] [LIN-PC] [28180] 注册自定义日志记录器为单例
[2025-08-13 17:32:54.166 DBG] [IndustrialHMI] [LIN-PC] [28180] 注册EventAggregator为单例
[2025-08-13 17:32:54.171 DBG] [IndustrialHMI] [LIN-PC] [28180] 注册ConfigurationService为单例
[2025-08-13 17:32:54.183 DBG] [IndustrialHMI] [LIN-PC] [28180] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:32:54.211 DBG] [IndustrialHMI] [LIN-PC] [28180] 注册MainForm为单例
[2025-08-13 17:32:54.212 DBG] [IndustrialHMI] [LIN-PC] [28180] 开始验证DryIoc容器配置
[2025-08-13 17:32:54.212 DBG] [IndustrialHMI] [LIN-PC] [28180] DryIoc容器配置验证通过
[2025-08-13 17:32:54.212 INF] [IndustrialHMI] [LIN-PC] [28180] DryIoc容器创建和配置完成
[2025-08-13 17:32:54.212 INF] [IndustrialHMI] [LIN-PC] [28180] 步骤2: 创建主窗体
[2025-08-13 17:32:54.212 INF] [IndustrialHMI] [LIN-PC] [28180] 步骤3: 创建模块加载器
[2025-08-13 17:32:54.212 INF] [IndustrialHMI] [LIN-PC] [28180] 步骤4: 加载模块
[2025-08-13 17:32:54.213 INF] [IndustrialHMI] [LIN-PC] [28180] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:32:54.214 DBG] [IndustrialHMI] [LIN-PC] [28180] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:32:54.221 DBG] [IndustrialHMI] [LIN-PC] [28180] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 发现模块类型: 
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:32:54.222 DBG] [IndustrialHMI] [LIN-PC] [28180] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:32:54.223 DBG] [IndustrialHMI] [LIN-PC] [28180] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:32:54.224 DBG] [IndustrialHMI] [LIN-PC] [28180] 发现模块类型: TestModuleMain
[2025-08-13 17:32:54.224 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:32:54.224 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:32:54.224 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:32:54.224 DBG] [IndustrialHMI] [LIN-PC] [28180] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:32:54.225 DBG] [IndustrialHMI] [LIN-PC] [28180] 开始加载模块: TestModuleMain
[2025-08-13 17:32:54.229 ERR] [IndustrialHMI] [LIN-PC] [28180] 加载模块失败: TestModuleMain
System.ArgumentException: 类型“Services.EventAggregator”的对象无法转换为类型“Contracts.Events.IEventAggregator”。
   在 System.RuntimeType.TryChangeType(Object value, Binder binder, CultureInfo culture, Boolean needsSpecialCast)
   在 System.Reflection.MethodBase.CheckArguments(Object[] parameters, Binder binder, BindingFlags invokeAttr, CultureInfo culture, Signature sig)
   在 System.Reflection.RuntimeMethodInfo.InvokeArgumentsCheck(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   在 System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   在 System.Reflection.RuntimePropertyInfo.SetValue(Object obj, Object value, Object[] index)
   在 Services.ModuleLoader.InjectDependencies(Object moduleInstance) 位置 F:\Project\C#_project\winform\winfoms\Services\ModuleLoader.cs:行号 228
   在 Services.ModuleLoader.LoadModule(Type moduleType) 位置 F:\Project\C#_project\winform\winfoms\Services\ModuleLoader.cs:行号 169
[2025-08-13 17:32:54.234 INF] [IndustrialHMI] [LIN-PC] [28180] 模块加载完成，共加载 0 个模块
[2025-08-13 17:32:54.234 INF] [IndustrialHMI] [LIN-PC] [28180] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 0 个模块
[2025-08-13 17:32:54.234 INF] [IndustrialHMI] [LIN-PC] [28180] 步骤5: 初始化主窗体
[2025-08-13 17:32:54.235 DBG] [IndustrialHMI] [LIN-PC] [28180] 主窗体初始化完成
[2025-08-13 17:32:54.235 INF] [IndustrialHMI] [LIN-PC] [28180] 应用程序初始化完成
[2025-08-13 17:32:54.235 INF] [IndustrialHMI] [LIN-PC] [28180] 应用程序初始化成功，启动主窗体
[2025-08-13 17:32:54.261 DBG] [IndustrialHMI] [LIN-PC] [28180] 主窗体事件订阅完成
﻿[2025-08-13 17:33:27.759 INF] [IndustrialHMI] [LIN-PC] [31052] === 应用程序启动 ===
[2025-08-13 17:33:27.790 INF] [IndustrialHMI] [LIN-PC] [31052] 应用程序版本: 1.0.0.0
[2025-08-13 17:33:27.790 INF] [IndustrialHMI] [LIN-PC] [31052] 启动参数: 
[2025-08-13 17:33:27.791 INF] [IndustrialHMI] [LIN-PC] [31052] 开始初始化应用程序
[2025-08-13 17:33:27.791 INF] [IndustrialHMI] [LIN-PC] [31052] 步骤1: 创建服务容器
[2025-08-13 17:33:27.794 INF] [IndustrialHMI] [LIN-PC] [31052] 开始创建DryIoc容器
[2025-08-13 17:33:27.806 DBG] [IndustrialHMI] [LIN-PC] [31052] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:33:27.809 DBG] [IndustrialHMI] [LIN-PC] [31052] 注册自定义日志记录器为单例
[2025-08-13 17:33:27.810 DBG] [IndustrialHMI] [LIN-PC] [31052] 注册EventAggregator为单例
[2025-08-13 17:33:27.814 DBG] [IndustrialHMI] [LIN-PC] [31052] 注册ConfigurationService为单例
[2025-08-13 17:33:27.826 DBG] [IndustrialHMI] [LIN-PC] [31052] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:33:27.854 DBG] [IndustrialHMI] [LIN-PC] [31052] 注册MainForm为单例
[2025-08-13 17:33:27.854 DBG] [IndustrialHMI] [LIN-PC] [31052] 开始验证DryIoc容器配置
[2025-08-13 17:33:27.854 DBG] [IndustrialHMI] [LIN-PC] [31052] DryIoc容器配置验证通过
[2025-08-13 17:33:27.854 INF] [IndustrialHMI] [LIN-PC] [31052] DryIoc容器创建和配置完成
[2025-08-13 17:33:27.854 INF] [IndustrialHMI] [LIN-PC] [31052] 步骤2: 创建主窗体
[2025-08-13 17:33:27.854 INF] [IndustrialHMI] [LIN-PC] [31052] 步骤3: 创建模块加载器
[2025-08-13 17:33:27.854 INF] [IndustrialHMI] [LIN-PC] [31052] 步骤4: 加载模块
[2025-08-13 17:33:27.855 INF] [IndustrialHMI] [LIN-PC] [31052] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:33:27.856 DBG] [IndustrialHMI] [LIN-PC] [31052] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:33:27.862 DBG] [IndustrialHMI] [LIN-PC] [31052] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 发现模块类型: 
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:27.863 DBG] [IndustrialHMI] [LIN-PC] [31052] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:33:27.864 DBG] [IndustrialHMI] [LIN-PC] [31052] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:33:27.865 DBG] [IndustrialHMI] [LIN-PC] [31052] 发现模块类型: TestModuleMain
[2025-08-13 17:33:27.865 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:33:27.865 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:33:27.865 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:33:27.865 DBG] [IndustrialHMI] [LIN-PC] [31052] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:33:27.865 DBG] [IndustrialHMI] [LIN-PC] [31052] 开始加载模块: TestModuleMain
[2025-08-13 17:33:27.869 ERR] [IndustrialHMI] [LIN-PC] [31052] 加载模块失败: TestModuleMain
System.ArgumentException: 类型“Services.EventAggregator”的对象无法转换为类型“Contracts.Events.IEventAggregator”。
   在 System.RuntimeType.TryChangeType(Object value, Binder binder, CultureInfo culture, Boolean needsSpecialCast)
   在 System.Reflection.MethodBase.CheckArguments(Object[] parameters, Binder binder, BindingFlags invokeAttr, CultureInfo culture, Signature sig)
   在 System.Reflection.RuntimeMethodInfo.InvokeArgumentsCheck(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   在 System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   在 System.Reflection.RuntimePropertyInfo.SetValue(Object obj, Object value, Object[] index)
   在 Services.ModuleLoader.InjectDependencies(Object moduleInstance) 位置 F:\Project\C#_project\winform\winfoms\Services\ModuleLoader.cs:行号 228
   在 Services.ModuleLoader.LoadModule(Type moduleType) 位置 F:\Project\C#_project\winform\winfoms\Services\ModuleLoader.cs:行号 169
[2025-08-13 17:33:27.874 INF] [IndustrialHMI] [LIN-PC] [31052] 模块加载完成，共加载 0 个模块
[2025-08-13 17:33:27.874 INF] [IndustrialHMI] [LIN-PC] [31052] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 0 个模块
[2025-08-13 17:33:27.875 INF] [IndustrialHMI] [LIN-PC] [31052] 步骤5: 初始化主窗体
[2025-08-13 17:33:27.875 DBG] [IndustrialHMI] [LIN-PC] [31052] 主窗体初始化完成
[2025-08-13 17:33:27.875 INF] [IndustrialHMI] [LIN-PC] [31052] 应用程序初始化完成
[2025-08-13 17:33:27.875 INF] [IndustrialHMI] [LIN-PC] [31052] 应用程序初始化成功，启动主窗体
[2025-08-13 17:33:27.896 DBG] [IndustrialHMI] [LIN-PC] [31052] 主窗体事件订阅完成
[2025-08-13 17:33:30.471 INF] [IndustrialHMI] [LIN-PC] [28180] 用户请求关闭应用程序
[2025-08-13 17:33:30.473 INF] [IndustrialHMI] [LIN-PC] [28180] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:33:30.473 INF] [IndustrialHMI] [LIN-PC] [28180] 开始卸载所有模块
[2025-08-13 17:33:30.473 INF] [IndustrialHMI] [LIN-PC] [28180] 所有模块卸载完成
[2025-08-13 17:33:30.473 INF] [IndustrialHMI] [LIN-PC] [28180] 应用程序关闭流程完成
[2025-08-13 17:33:30.474 INF] [IndustrialHMI] [LIN-PC] [28180] 主窗体已关闭，资源清理完成
[2025-08-13 17:33:30.494 INF] [IndustrialHMI] [LIN-PC] [28180] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:33:30.495 INF] [IndustrialHMI] [LIN-PC] [28180] 开始释放应用程序资源
[2025-08-13 17:33:30.495 INF] [IndustrialHMI] [LIN-PC] [28180] 开始卸载所有模块
[2025-08-13 17:33:30.495 INF] [IndustrialHMI] [LIN-PC] [28180] 所有模块卸载完成
[2025-08-13 17:33:30.495 INF] [IndustrialHMI] [LIN-PC] [28180] 应用程序资源释放完成
[2025-08-13 17:33:30.495 INF] [IndustrialHMI] [LIN-PC] [28180] === 应用程序正常退出 ===
[2025-08-13 17:33:30.709 INF] [IndustrialHMI] [LIN-PC] [31052] 用户请求关闭应用程序
[2025-08-13 17:33:30.710 INF] [IndustrialHMI] [LIN-PC] [31052] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:33:30.711 INF] [IndustrialHMI] [LIN-PC] [31052] 开始卸载所有模块
[2025-08-13 17:33:30.711 INF] [IndustrialHMI] [LIN-PC] [31052] 所有模块卸载完成
[2025-08-13 17:33:30.711 INF] [IndustrialHMI] [LIN-PC] [31052] 应用程序关闭流程完成
[2025-08-13 17:33:30.712 INF] [IndustrialHMI] [LIN-PC] [31052] 主窗体已关闭，资源清理完成
[2025-08-13 17:33:30.748 INF] [IndustrialHMI] [LIN-PC] [31052] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:33:30.749 INF] [IndustrialHMI] [LIN-PC] [31052] 开始释放应用程序资源
[2025-08-13 17:33:30.749 INF] [IndustrialHMI] [LIN-PC] [31052] 开始卸载所有模块
[2025-08-13 17:33:30.749 INF] [IndustrialHMI] [LIN-PC] [31052] 所有模块卸载完成
[2025-08-13 17:33:30.749 INF] [IndustrialHMI] [LIN-PC] [31052] 应用程序资源释放完成
[2025-08-13 17:33:30.749 INF] [IndustrialHMI] [LIN-PC] [31052] === 应用程序正常退出 ===
﻿[2025-08-13 17:33:30.866 INF] [IndustrialHMI] [LIN-PC] [24848] === 应用程序启动 ===
[2025-08-13 17:33:30.898 INF] [IndustrialHMI] [LIN-PC] [24848] 应用程序版本: 1.0.0.0
[2025-08-13 17:33:30.899 INF] [IndustrialHMI] [LIN-PC] [24848] 启动参数: 
[2025-08-13 17:33:30.899 INF] [IndustrialHMI] [LIN-PC] [24848] 开始初始化应用程序
[2025-08-13 17:33:30.899 INF] [IndustrialHMI] [LIN-PC] [24848] 步骤1: 创建服务容器
[2025-08-13 17:33:30.903 INF] [IndustrialHMI] [LIN-PC] [24848] 开始创建DryIoc容器
[2025-08-13 17:33:30.918 DBG] [IndustrialHMI] [LIN-PC] [24848] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:33:30.922 DBG] [IndustrialHMI] [LIN-PC] [24848] 注册自定义日志记录器为单例
[2025-08-13 17:33:30.922 DBG] [IndustrialHMI] [LIN-PC] [24848] 注册EventAggregator为单例
[2025-08-13 17:33:30.927 DBG] [IndustrialHMI] [LIN-PC] [24848] 注册ConfigurationService为单例
[2025-08-13 17:33:30.939 DBG] [IndustrialHMI] [LIN-PC] [24848] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:33:30.971 DBG] [IndustrialHMI] [LIN-PC] [24848] 注册MainForm为单例
[2025-08-13 17:33:30.972 DBG] [IndustrialHMI] [LIN-PC] [24848] 开始验证DryIoc容器配置
[2025-08-13 17:33:30.972 DBG] [IndustrialHMI] [LIN-PC] [24848] DryIoc容器配置验证通过
[2025-08-13 17:33:30.972 INF] [IndustrialHMI] [LIN-PC] [24848] DryIoc容器创建和配置完成
[2025-08-13 17:33:30.972 INF] [IndustrialHMI] [LIN-PC] [24848] 步骤2: 创建主窗体
[2025-08-13 17:33:30.972 INF] [IndustrialHMI] [LIN-PC] [24848] 步骤3: 创建模块加载器
[2025-08-13 17:33:30.972 INF] [IndustrialHMI] [LIN-PC] [24848] 步骤4: 加载模块
[2025-08-13 17:33:30.972 INF] [IndustrialHMI] [LIN-PC] [24848] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:33:30.973 DBG] [IndustrialHMI] [LIN-PC] [24848] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:33:30.980 DBG] [IndustrialHMI] [LIN-PC] [24848] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 发现模块类型: 
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:33:30.981 DBG] [IndustrialHMI] [LIN-PC] [24848] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:33:30.982 DBG] [IndustrialHMI] [LIN-PC] [24848] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:33:30.982 DBG] [IndustrialHMI] [LIN-PC] [24848] 发现模块类型: TestModuleMain
[2025-08-13 17:33:30.982 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:33:30.982 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:33:30.982 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:33:30.983 DBG] [IndustrialHMI] [LIN-PC] [24848] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:33:30.983 DBG] [IndustrialHMI] [LIN-PC] [24848] 开始加载模块: TestModuleMain
[2025-08-13 17:33:30.986 ERR] [IndustrialHMI] [LIN-PC] [24848] 加载模块失败: TestModuleMain
System.ArgumentException: 类型“Services.EventAggregator”的对象无法转换为类型“Contracts.Events.IEventAggregator”。
   在 System.RuntimeType.TryChangeType(Object value, Binder binder, CultureInfo culture, Boolean needsSpecialCast)
   在 System.Reflection.MethodBase.CheckArguments(Object[] parameters, Binder binder, BindingFlags invokeAttr, CultureInfo culture, Signature sig)
   在 System.Reflection.RuntimeMethodInfo.InvokeArgumentsCheck(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   在 System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   在 System.Reflection.RuntimePropertyInfo.SetValue(Object obj, Object value, Object[] index)
   在 Services.ModuleLoader.InjectDependencies(Object moduleInstance) 位置 F:\Project\C#_project\winform\winfoms\Services\ModuleLoader.cs:行号 228
   在 Services.ModuleLoader.LoadModule(Type moduleType) 位置 F:\Project\C#_project\winform\winfoms\Services\ModuleLoader.cs:行号 169
[2025-08-13 17:33:30.992 INF] [IndustrialHMI] [LIN-PC] [24848] 模块加载完成，共加载 0 个模块
[2025-08-13 17:33:30.992 INF] [IndustrialHMI] [LIN-PC] [24848] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 0 个模块
[2025-08-13 17:33:30.992 INF] [IndustrialHMI] [LIN-PC] [24848] 步骤5: 初始化主窗体
[2025-08-13 17:33:30.993 DBG] [IndustrialHMI] [LIN-PC] [24848] 主窗体初始化完成
[2025-08-13 17:33:30.993 INF] [IndustrialHMI] [LIN-PC] [24848] 应用程序初始化完成
[2025-08-13 17:33:30.993 INF] [IndustrialHMI] [LIN-PC] [24848] 应用程序初始化成功，启动主窗体
[2025-08-13 17:33:31.040 DBG] [IndustrialHMI] [LIN-PC] [24848] 主窗体事件订阅完成
[2025-08-13 17:33:32.800 INF] [IndustrialHMI] [LIN-PC] [24848] 用户请求关闭应用程序
[2025-08-13 17:33:32.801 INF] [IndustrialHMI] [LIN-PC] [24848] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:33:32.801 INF] [IndustrialHMI] [LIN-PC] [24848] 开始卸载所有模块
[2025-08-13 17:33:32.801 INF] [IndustrialHMI] [LIN-PC] [24848] 所有模块卸载完成
[2025-08-13 17:33:32.801 INF] [IndustrialHMI] [LIN-PC] [24848] 应用程序关闭流程完成
[2025-08-13 17:33:32.802 INF] [IndustrialHMI] [LIN-PC] [24848] 主窗体已关闭，资源清理完成
[2025-08-13 17:33:32.841 INF] [IndustrialHMI] [LIN-PC] [24848] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:33:32.842 INF] [IndustrialHMI] [LIN-PC] [24848] 开始释放应用程序资源
[2025-08-13 17:33:32.842 INF] [IndustrialHMI] [LIN-PC] [24848] 开始卸载所有模块
[2025-08-13 17:33:32.842 INF] [IndustrialHMI] [LIN-PC] [24848] 所有模块卸载完成
[2025-08-13 17:33:32.842 INF] [IndustrialHMI] [LIN-PC] [24848] 应用程序资源释放完成
[2025-08-13 17:33:32.842 INF] [IndustrialHMI] [LIN-PC] [24848] === 应用程序正常退出 ===
﻿[2025-08-13 17:35:06.358 INF] [IndustrialHMI] [LIN-PC] [22216] === 应用程序启动 ===
[2025-08-13 17:35:06.393 INF] [IndustrialHMI] [LIN-PC] [22216] 应用程序版本: 1.0.0.0
[2025-08-13 17:35:06.393 INF] [IndustrialHMI] [LIN-PC] [22216] 启动参数: 
[2025-08-13 17:35:06.393 INF] [IndustrialHMI] [LIN-PC] [22216] 开始初始化应用程序
[2025-08-13 17:35:06.393 INF] [IndustrialHMI] [LIN-PC] [22216] 步骤1: 创建服务容器
[2025-08-13 17:35:06.397 INF] [IndustrialHMI] [LIN-PC] [22216] 开始创建DryIoc容器
[2025-08-13 17:35:06.411 DBG] [IndustrialHMI] [LIN-PC] [22216] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:35:06.415 DBG] [IndustrialHMI] [LIN-PC] [22216] 注册自定义日志记录器为单例
[2025-08-13 17:35:06.415 DBG] [IndustrialHMI] [LIN-PC] [22216] 注册EventAggregator为单例
[2025-08-13 17:35:06.421 DBG] [IndustrialHMI] [LIN-PC] [22216] 注册ConfigurationService为单例
[2025-08-13 17:35:06.434 DBG] [IndustrialHMI] [LIN-PC] [22216] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:35:06.461 DBG] [IndustrialHMI] [LIN-PC] [22216] 注册MainForm为单例
[2025-08-13 17:35:06.461 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始验证DryIoc容器配置
[2025-08-13 17:35:06.461 DBG] [IndustrialHMI] [LIN-PC] [22216] DryIoc容器配置验证通过
[2025-08-13 17:35:06.461 INF] [IndustrialHMI] [LIN-PC] [22216] DryIoc容器创建和配置完成
[2025-08-13 17:35:06.461 INF] [IndustrialHMI] [LIN-PC] [22216] 步骤2: 创建主窗体
[2025-08-13 17:35:06.461 INF] [IndustrialHMI] [LIN-PC] [22216] 步骤3: 创建模块加载器
[2025-08-13 17:35:06.461 INF] [IndustrialHMI] [LIN-PC] [22216] 步骤4: 加载模块
[2025-08-13 17:35:06.462 INF] [IndustrialHMI] [LIN-PC] [22216] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:35:06.463 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 17:35:06.470 DBG] [IndustrialHMI] [LIN-PC] [22216] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 发现模块类型: AlarmModuleMain
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.471 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.472 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载模块: AlarmModuleMain
[2025-08-13 17:35:06.472 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 17:35:06.472 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 17:35:06.473 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 17:35:06.473 INF] [IndustrialHMI] [LIN-PC] [22216] 开始初始化报警管理模块
[2025-08-13 17:35:06.475 DBG] [IndustrialHMI] [LIN-PC] [22216] 报警服务创建完成
[2025-08-13 17:35:06.482 DBG] [IndustrialHMI] [LIN-PC] [22216] 报警视图创建完成
[2025-08-13 17:35:06.484 DBG] [IndustrialHMI] [LIN-PC] [22216] AlarmModel 初始化完成
[2025-08-13 17:35:06.484 DBG] [IndustrialHMI] [LIN-PC] [22216] 报警模型创建完成
[2025-08-13 17:35:06.485 DBG] [IndustrialHMI] [LIN-PC] [22216] AlarmPresenter 初始化完成
[2025-08-13 17:35:06.485 DBG] [IndustrialHMI] [LIN-PC] [22216] 报警表示器创建完成
[2025-08-13 17:35:06.485 INF] [IndustrialHMI] [LIN-PC] [22216] MVP组件创建完成
[2025-08-13 17:35:06.485 DBG] [IndustrialHMI] [LIN-PC] [22216] 系统事件订阅完成
[2025-08-13 17:35:06.485 INF] [IndustrialHMI] [LIN-PC] [22216] 报警管理模块初始化完成
[2025-08-13 17:35:06.485 INF] [IndustrialHMI] [LIN-PC] [22216] 启动报警管理模块
[2025-08-13 17:35:06.486 INF] [IndustrialHMI] [LIN-PC] [22216] 启动报警监控
[2025-08-13 17:35:06.486 INF] [IndustrialHMI] [LIN-PC] [22216] 开始报警监控
[2025-08-13 17:35:06.486 INF] [IndustrialHMI] [LIN-PC] [22216] 报警管理模块启动完成
[2025-08-13 17:35:06.486 INF] [IndustrialHMI] [LIN-PC] [22216] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 17:35:06.488 DBG] [IndustrialHMI] [LIN-PC] [22216] 模块已加载: 报警管理
[2025-08-13 17:35:06.488 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 17:35:06.489 DBG] [IndustrialHMI] [LIN-PC] [22216] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 17:35:06.490 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 17:35:06.491 INF] [IndustrialHMI] [LIN-PC] [22216] 开始初始化 CommunicationTestModule
[2025-08-13 17:35:06.492 INF] [IndustrialHMI] [LIN-PC] [22216] 初始化了 8 个测试用例
[2025-08-13 17:35:06.492 INF] [IndustrialHMI] [LIN-PC] [22216] 性能监控器初始化完成
[2025-08-13 17:35:06.493 INF] [IndustrialHMI] [LIN-PC] [22216] CommunicationTestModel 初始化完成
[2025-08-13 17:35:06.497 DBG] [IndustrialHMI] [LIN-PC] [22216] CommunicationTestView 初始化完成
[2025-08-13 17:35:06.501 DBG] [IndustrialHMI] [LIN-PC] [22216] 视图数据初始化完成
[2025-08-13 17:35:06.501 INF] [IndustrialHMI] [LIN-PC] [22216] CommunicationTestPresenter 初始化完成
[2025-08-13 17:35:06.501 DBG] [IndustrialHMI] [LIN-PC] [22216] MVP组件创建完成
[2025-08-13 17:35:06.502 DBG] [IndustrialHMI] [LIN-PC] [22216] 系统事件订阅完成
[2025-08-13 17:35:06.502 INF] [IndustrialHMI] [LIN-PC] [22216] CommunicationTestModule 初始化完成
[2025-08-13 17:35:06.502 INF] [IndustrialHMI] [LIN-PC] [22216] 启动 CommunicationTestModule
[2025-08-13 17:35:06.502 INF] [IndustrialHMI] [LIN-PC] [22216] CommunicationTestModule 启动完成
[2025-08-13 17:35:06.502 INF] [IndustrialHMI] [LIN-PC] [22216] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 17:35:06.502 DBG] [IndustrialHMI] [LIN-PC] [22216] 模块已加载: 通信测试
[2025-08-13 17:35:06.502 DBG] [IndustrialHMI] [LIN-PC] [22216] 收到模块加载事件: 通信测试
[2025-08-13 17:35:06.502 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:35:06.503 DBG] [IndustrialHMI] [LIN-PC] [22216] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 发现模块类型: 
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:06.504 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 发现模块类型: DeviceModuleMain
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.505 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载模块: DeviceModuleMain
[2025-08-13 17:35:06.506 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 17:35:06.506 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 17:35:06.506 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 17:35:06.506 INF] [IndustrialHMI] [LIN-PC] [22216] 开始初始化设备监控模块
[2025-08-13 17:35:06.507 DBG] [IndustrialHMI] [LIN-PC] [22216] 设备服务创建完成
[2025-08-13 17:35:06.509 DBG] [IndustrialHMI] [LIN-PC] [22216] 设备视图创建完成
[2025-08-13 17:35:06.510 DBG] [IndustrialHMI] [LIN-PC] [22216] DeviceModel 初始化完成
[2025-08-13 17:35:06.510 DBG] [IndustrialHMI] [LIN-PC] [22216] 设备模型创建完成
[2025-08-13 17:35:06.511 DBG] [IndustrialHMI] [LIN-PC] [22216] DevicePresenter 初始化完成
[2025-08-13 17:35:06.511 DBG] [IndustrialHMI] [LIN-PC] [22216] 设备表示器创建完成
[2025-08-13 17:35:06.511 INF] [IndustrialHMI] [LIN-PC] [22216] MVP组件创建完成
[2025-08-13 17:35:06.512 DBG] [IndustrialHMI] [LIN-PC] [22216] 系统事件订阅完成
[2025-08-13 17:35:06.512 INF] [IndustrialHMI] [LIN-PC] [22216] 设备监控模块初始化完成
[2025-08-13 17:35:06.512 INF] [IndustrialHMI] [LIN-PC] [22216] 启动设备监控模块
[2025-08-13 17:35:06.512 INF] [IndustrialHMI] [LIN-PC] [22216] 用户请求开始设备监控
[2025-08-13 17:35:06.512 INF] [IndustrialHMI] [LIN-PC] [22216] 开始设备监控
[2025-08-13 17:35:06.512 INF] [IndustrialHMI] [LIN-PC] [22216] 设备监控已启动
[2025-08-13 17:35:06.512 INF] [IndustrialHMI] [LIN-PC] [22216] 设备监控模块启动完成
[2025-08-13 17:35:06.513 INF] [IndustrialHMI] [LIN-PC] [22216] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 17:35:06.513 DBG] [IndustrialHMI] [LIN-PC] [22216] 模块已加载: 设备监控
[2025-08-13 17:35:06.513 DBG] [IndustrialHMI] [LIN-PC] [22216] 收到模块加载事件: 设备监控
[2025-08-13 17:35:06.513 DBG] [IndustrialHMI] [LIN-PC] [22216] 模块已加载: 设备监控
[2025-08-13 17:35:06.513 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 17:35:06.514 DBG] [IndustrialHMI] [LIN-PC] [22216] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:06.514 DBG] [IndustrialHMI] [LIN-PC] [22216] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 17:35:06.514 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:06.514 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:35:06.514 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:35:06.514 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 17:35:06.515 INF] [IndustrialHMI] [LIN-PC] [22216] 开始初始化测试框架模块
[2025-08-13 17:35:06.515 DBG] [IndustrialHMI] [LIN-PC] [22216] ConfigurationService未注入（可选）
[2025-08-13 17:35:11.238 DBG] [IndustrialHMI] [LIN-PC] [22216] 初始化TestFrameworkPresenter
[2025-08-13 17:35:11.248 DBG] [IndustrialHMI] [LIN-PC] [22216] TestFrameworkPresenter初始化完成
[2025-08-13 17:35:11.249 DBG] [IndustrialHMI] [LIN-PC] [22216] 测试框架模块事件订阅完成
[2025-08-13 17:35:11.249 INF] [IndustrialHMI] [LIN-PC] [22216] 测试框架模块初始化完成
[2025-08-13 17:35:11.249 INF] [IndustrialHMI] [LIN-PC] [22216] 启动测试框架模块
[2025-08-13 17:35:11.249 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 模型已启动
[2025-08-13 17:35:11.249 DBG] [IndustrialHMI] [LIN-PC] [22216] 加载TestFramework数据
[2025-08-13 17:35:11.250 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型数据已更新，视图已刷新
[2025-08-13 17:35:11.250 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 数据加载完成
[2025-08-13 17:35:11.251 DBG] [IndustrialHMI] [LIN-PC] [22216] TestFramework数据加载完成
[2025-08-13 17:35:11.251 INF] [IndustrialHMI] [LIN-PC] [22216] 初始化集成测试套件
[2025-08-13 17:35:11.251 INF] [IndustrialHMI] [LIN-PC] [22216] 集成测试套件初始化完成
[2025-08-13 17:35:11.251 INF] [IndustrialHMI] [LIN-PC] [22216] 初始化性能测试套件
[2025-08-13 17:35:11.372 INF] [IndustrialHMI] [LIN-PC] [22216] 性能测试套件初始化完成
[2025-08-13 17:35:11.372 INF] [IndustrialHMI] [LIN-PC] [22216] 初始化内存泄漏测试套件
[2025-08-13 17:35:11.372 INF] [IndustrialHMI] [LIN-PC] [22216] 内存泄漏测试套件初始化完成
[2025-08-13 17:35:11.372 INF] [IndustrialHMI] [LIN-PC] [22216] 测试框架模块启动完成
[2025-08-13 17:35:11.372 INF] [IndustrialHMI] [LIN-PC] [22216] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 17:35:11.372 INF] [IndustrialHMI] [LIN-PC] [22216] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 17:35:11.373 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型数据已更新，视图已刷新
[2025-08-13 17:35:11.373 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:35:11.373 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:35:11.374 DBG] [IndustrialHMI] [LIN-PC] [22216] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 发现模块类型: TestModuleMain
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 开始加载模块: TestModuleMain
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 TestModuleMain 注入Logger
[2025-08-13 17:35:11.375 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 17:35:11.376 INF] [IndustrialHMI] [LIN-PC] [22216] 开始初始化模块: 测试模块
[2025-08-13 17:35:11.376 WRN] [IndustrialHMI] [LIN-PC] [22216] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 17:35:11.376 DBG] [IndustrialHMI] [LIN-PC] [22216] 依赖注入验证通过
[2025-08-13 17:35:11.376 DBG] [IndustrialHMI] [LIN-PC] [22216] 创建TestModuleModel成功
[2025-08-13 17:35:11.379 DBG] [IndustrialHMI] [LIN-PC] [22216] 创建TestModuleView成功
[2025-08-13 17:35:11.379 DBG] [IndustrialHMI] [LIN-PC] [22216] TestModulePresenter创建完成
[2025-08-13 17:35:11.379 DBG] [IndustrialHMI] [LIN-PC] [22216] 创建TestModulePresenter成功
[2025-08-13 17:35:11.379 DBG] [IndustrialHMI] [LIN-PC] [22216] 事件订阅完成
[2025-08-13 17:35:11.379 INF] [IndustrialHMI] [LIN-PC] [22216] 模块初始化完成: 测试模块
[2025-08-13 17:35:11.380 INF] [IndustrialHMI] [LIN-PC] [22216] 开始启动模块: 测试模块
[2025-08-13 17:35:11.380 INF] [IndustrialHMI] [LIN-PC] [22216] 启动TestModulePresenter
[2025-08-13 17:35:11.385 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 模型启动完成
[2025-08-13 17:35:11.386 DBG] [IndustrialHMI] [LIN-PC] [22216] 系统事件订阅完成
[2025-08-13 17:35:11.386 INF] [IndustrialHMI] [LIN-PC] [22216] TestModulePresenter启动完成
[2025-08-13 17:35:11.387 INF] [IndustrialHMI] [LIN-PC] [22216] 模块启动完成: 测试模块
[2025-08-13 17:35:11.387 DBG] [IndustrialHMI] [LIN-PC] [22216] 处理系统事件: ModuleStarted
[2025-08-13 17:35:11.388 INF] [IndustrialHMI] [LIN-PC] [22216] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 17:35:11.388 INF] [IndustrialHMI] [LIN-PC] [22216] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 17:35:11.388 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型数据已更新，视图已刷新
[2025-08-13 17:35:11.390 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型数据变化事件处理完成
[2025-08-13 17:35:11.393 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:35:11.394 DBG] [IndustrialHMI] [LIN-PC] [22216] 处理模块加载事件: 测试模块
[2025-08-13 17:35:11.394 INF] [IndustrialHMI] [LIN-PC] [22216] 模块加载完成，共加载 5 个模块
[2025-08-13 17:35:11.394 INF] [IndustrialHMI] [LIN-PC] [22216] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 17:35:11.395 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 报警管理 添加了UI标签页
[2025-08-13 17:35:11.395 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 通信测试 添加了UI标签页
[2025-08-13 17:35:11.395 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 设备监控 添加了UI标签页
[2025-08-13 17:35:11.396 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 17:35:11.396 DBG] [IndustrialHMI] [LIN-PC] [22216] 为模块 测试模块 添加了UI标签页
[2025-08-13 17:35:11.396 INF] [IndustrialHMI] [LIN-PC] [22216] 步骤5: 初始化主窗体
[2025-08-13 17:35:11.397 DBG] [IndustrialHMI] [LIN-PC] [22216] 主窗体初始化完成
[2025-08-13 17:35:11.397 INF] [IndustrialHMI] [LIN-PC] [22216] 应用程序初始化完成
[2025-08-13 17:35:11.397 INF] [IndustrialHMI] [LIN-PC] [22216] 应用程序初始化成功，启动主窗体
[2025-08-13 17:35:11.397 INF] [IndustrialHMI] [LIN-PC] [22216] 测试框架模块收到系统启动事件
[2025-08-13 17:35:11.398 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型数据已更新，视图已刷新
[2025-08-13 17:35:11.398 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 17:35:11.398 INF] [IndustrialHMI] [LIN-PC] [22216] 模块 测试模块 收到系统启动事件
[2025-08-13 17:35:11.399 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 收到系统启动事件
[2025-08-13 17:35:11.529 DBG] [IndustrialHMI] [LIN-PC] [22216] 主窗体事件订阅完成
[2025-08-13 17:35:11.535 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 17:35:11.536 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 定时状态更新 - 17:35:11
[2025-08-13 17:35:12.415 DBG] [IndustrialHMI] [LIN-PC] [22216] 模型状态变化: 系统启动后初始化完成
﻿[2025-08-13 17:35:14.448 INF] [IndustrialHMI] [LIN-PC] [29444] === 应用程序启动 ===
[2025-08-13 17:35:14.481 INF] [IndustrialHMI] [LIN-PC] [29444] 应用程序版本: 1.0.0.0
[2025-08-13 17:35:14.481 INF] [IndustrialHMI] [LIN-PC] [29444] 启动参数: 
[2025-08-13 17:35:14.481 INF] [IndustrialHMI] [LIN-PC] [29444] 开始初始化应用程序
[2025-08-13 17:35:14.481 INF] [IndustrialHMI] [LIN-PC] [29444] 步骤1: 创建服务容器
[2025-08-13 17:35:14.485 INF] [IndustrialHMI] [LIN-PC] [29444] 开始创建DryIoc容器
[2025-08-13 17:35:14.498 DBG] [IndustrialHMI] [LIN-PC] [29444] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:35:14.502 DBG] [IndustrialHMI] [LIN-PC] [29444] 注册自定义日志记录器为单例
[2025-08-13 17:35:14.502 DBG] [IndustrialHMI] [LIN-PC] [29444] 注册EventAggregator为单例
[2025-08-13 17:35:14.507 DBG] [IndustrialHMI] [LIN-PC] [29444] 注册ConfigurationService为单例
[2025-08-13 17:35:14.519 DBG] [IndustrialHMI] [LIN-PC] [29444] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:35:14.548 DBG] [IndustrialHMI] [LIN-PC] [29444] 注册MainForm为单例
[2025-08-13 17:35:14.549 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始验证DryIoc容器配置
[2025-08-13 17:35:14.549 DBG] [IndustrialHMI] [LIN-PC] [29444] DryIoc容器配置验证通过
[2025-08-13 17:35:14.549 INF] [IndustrialHMI] [LIN-PC] [29444] DryIoc容器创建和配置完成
[2025-08-13 17:35:14.549 INF] [IndustrialHMI] [LIN-PC] [29444] 步骤2: 创建主窗体
[2025-08-13 17:35:14.549 INF] [IndustrialHMI] [LIN-PC] [29444] 步骤3: 创建模块加载器
[2025-08-13 17:35:14.549 INF] [IndustrialHMI] [LIN-PC] [29444] 步骤4: 加载模块
[2025-08-13 17:35:14.550 INF] [IndustrialHMI] [LIN-PC] [29444] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:35:14.550 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 17:35:14.557 DBG] [IndustrialHMI] [LIN-PC] [29444] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 发现模块类型: AlarmModuleMain
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.558 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.559 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载模块: AlarmModuleMain
[2025-08-13 17:35:14.560 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 17:35:14.560 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 17:35:14.561 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 17:35:14.561 INF] [IndustrialHMI] [LIN-PC] [29444] 开始初始化报警管理模块
[2025-08-13 17:35:14.563 DBG] [IndustrialHMI] [LIN-PC] [29444] 报警服务创建完成
[2025-08-13 17:35:14.571 DBG] [IndustrialHMI] [LIN-PC] [29444] 报警视图创建完成
[2025-08-13 17:35:14.573 DBG] [IndustrialHMI] [LIN-PC] [29444] AlarmModel 初始化完成
[2025-08-13 17:35:14.573 DBG] [IndustrialHMI] [LIN-PC] [29444] 报警模型创建完成
[2025-08-13 17:35:14.573 DBG] [IndustrialHMI] [LIN-PC] [29444] AlarmPresenter 初始化完成
[2025-08-13 17:35:14.573 DBG] [IndustrialHMI] [LIN-PC] [29444] 报警表示器创建完成
[2025-08-13 17:35:14.573 INF] [IndustrialHMI] [LIN-PC] [29444] MVP组件创建完成
[2025-08-13 17:35:14.574 DBG] [IndustrialHMI] [LIN-PC] [29444] 系统事件订阅完成
[2025-08-13 17:35:14.574 INF] [IndustrialHMI] [LIN-PC] [29444] 报警管理模块初始化完成
[2025-08-13 17:35:14.574 INF] [IndustrialHMI] [LIN-PC] [29444] 启动报警管理模块
[2025-08-13 17:35:14.574 INF] [IndustrialHMI] [LIN-PC] [29444] 启动报警监控
[2025-08-13 17:35:14.574 INF] [IndustrialHMI] [LIN-PC] [29444] 开始报警监控
[2025-08-13 17:35:14.575 INF] [IndustrialHMI] [LIN-PC] [29444] 报警管理模块启动完成
[2025-08-13 17:35:14.575 INF] [IndustrialHMI] [LIN-PC] [29444] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 17:35:14.577 DBG] [IndustrialHMI] [LIN-PC] [29444] 模块已加载: 报警管理
[2025-08-13 17:35:14.577 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 17:35:14.578 DBG] [IndustrialHMI] [LIN-PC] [29444] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.579 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 17:35:14.580 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 17:35:14.581 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 17:35:14.581 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 17:35:14.581 INF] [IndustrialHMI] [LIN-PC] [29444] 开始初始化 CommunicationTestModule
[2025-08-13 17:35:14.582 INF] [IndustrialHMI] [LIN-PC] [29444] 初始化了 8 个测试用例
[2025-08-13 17:35:14.583 INF] [IndustrialHMI] [LIN-PC] [29444] 性能监控器初始化完成
[2025-08-13 17:35:14.583 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModel 初始化完成
[2025-08-13 17:35:14.589 DBG] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestView 初始化完成
[2025-08-13 17:35:14.592 DBG] [IndustrialHMI] [LIN-PC] [29444] 视图数据初始化完成
[2025-08-13 17:35:14.592 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestPresenter 初始化完成
[2025-08-13 17:35:14.592 DBG] [IndustrialHMI] [LIN-PC] [29444] MVP组件创建完成
[2025-08-13 17:35:14.592 DBG] [IndustrialHMI] [LIN-PC] [29444] 系统事件订阅完成
[2025-08-13 17:35:14.592 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModule 初始化完成
[2025-08-13 17:35:14.592 INF] [IndustrialHMI] [LIN-PC] [29444] 启动 CommunicationTestModule
[2025-08-13 17:35:14.592 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModule 启动完成
[2025-08-13 17:35:14.592 INF] [IndustrialHMI] [LIN-PC] [29444] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 17:35:14.592 DBG] [IndustrialHMI] [LIN-PC] [29444] 模块已加载: 通信测试
[2025-08-13 17:35:14.592 DBG] [IndustrialHMI] [LIN-PC] [29444] 收到模块加载事件: 通信测试
[2025-08-13 17:35:14.592 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:35:14.595 DBG] [IndustrialHMI] [LIN-PC] [29444] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:14.595 DBG] [IndustrialHMI] [LIN-PC] [29444] 发现模块类型: 
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:35:14.596 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 发现模块类型: DeviceModuleMain
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.597 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载模块: DeviceModuleMain
[2025-08-13 17:35:14.598 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 17:35:14.598 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 17:35:14.598 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 17:35:14.598 INF] [IndustrialHMI] [LIN-PC] [29444] 开始初始化设备监控模块
[2025-08-13 17:35:14.599 DBG] [IndustrialHMI] [LIN-PC] [29444] 设备服务创建完成
[2025-08-13 17:35:14.601 DBG] [IndustrialHMI] [LIN-PC] [29444] 设备视图创建完成
[2025-08-13 17:35:14.602 DBG] [IndustrialHMI] [LIN-PC] [29444] DeviceModel 初始化完成
[2025-08-13 17:35:14.602 DBG] [IndustrialHMI] [LIN-PC] [29444] 设备模型创建完成
[2025-08-13 17:35:14.602 DBG] [IndustrialHMI] [LIN-PC] [29444] DevicePresenter 初始化完成
[2025-08-13 17:35:14.602 DBG] [IndustrialHMI] [LIN-PC] [29444] 设备表示器创建完成
[2025-08-13 17:35:14.602 INF] [IndustrialHMI] [LIN-PC] [29444] MVP组件创建完成
[2025-08-13 17:35:14.603 DBG] [IndustrialHMI] [LIN-PC] [29444] 系统事件订阅完成
[2025-08-13 17:35:14.603 INF] [IndustrialHMI] [LIN-PC] [29444] 设备监控模块初始化完成
[2025-08-13 17:35:14.603 INF] [IndustrialHMI] [LIN-PC] [29444] 启动设备监控模块
[2025-08-13 17:35:14.603 INF] [IndustrialHMI] [LIN-PC] [29444] 用户请求开始设备监控
[2025-08-13 17:35:14.603 INF] [IndustrialHMI] [LIN-PC] [29444] 开始设备监控
[2025-08-13 17:35:14.604 INF] [IndustrialHMI] [LIN-PC] [29444] 设备监控已启动
[2025-08-13 17:35:14.604 INF] [IndustrialHMI] [LIN-PC] [29444] 设备监控模块启动完成
[2025-08-13 17:35:14.604 INF] [IndustrialHMI] [LIN-PC] [29444] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 17:35:14.604 DBG] [IndustrialHMI] [LIN-PC] [29444] 模块已加载: 设备监控
[2025-08-13 17:35:14.604 DBG] [IndustrialHMI] [LIN-PC] [29444] 收到模块加载事件: 设备监控
[2025-08-13 17:35:14.604 DBG] [IndustrialHMI] [LIN-PC] [29444] 模块已加载: 设备监控
[2025-08-13 17:35:14.604 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 17:35:14.605 DBG] [IndustrialHMI] [LIN-PC] [29444] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 17:35:14.606 INF] [IndustrialHMI] [LIN-PC] [29444] 开始初始化测试框架模块
[2025-08-13 17:35:14.606 DBG] [IndustrialHMI] [LIN-PC] [29444] ConfigurationService未注入（可选）
[2025-08-13 17:35:15.080 DBG] [IndustrialHMI] [LIN-PC] [29444] 初始化TestFrameworkPresenter
[2025-08-13 17:35:15.089 DBG] [IndustrialHMI] [LIN-PC] [29444] TestFrameworkPresenter初始化完成
[2025-08-13 17:35:15.090 DBG] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块事件订阅完成
[2025-08-13 17:35:15.090 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块初始化完成
[2025-08-13 17:35:15.090 INF] [IndustrialHMI] [LIN-PC] [29444] 启动测试框架模块
[2025-08-13 17:35:15.091 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 模型已启动
[2025-08-13 17:35:15.091 DBG] [IndustrialHMI] [LIN-PC] [29444] 加载TestFramework数据
[2025-08-13 17:35:15.092 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:15.092 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 数据加载完成
[2025-08-13 17:35:15.092 DBG] [IndustrialHMI] [LIN-PC] [29444] TestFramework数据加载完成
[2025-08-13 17:35:15.093 INF] [IndustrialHMI] [LIN-PC] [29444] 初始化集成测试套件
[2025-08-13 17:35:15.093 INF] [IndustrialHMI] [LIN-PC] [29444] 集成测试套件初始化完成
[2025-08-13 17:35:15.093 INF] [IndustrialHMI] [LIN-PC] [29444] 初始化性能测试套件
[2025-08-13 17:35:15.218 INF] [IndustrialHMI] [LIN-PC] [29444] 性能测试套件初始化完成
[2025-08-13 17:35:15.218 INF] [IndustrialHMI] [LIN-PC] [29444] 初始化内存泄漏测试套件
[2025-08-13 17:35:15.218 INF] [IndustrialHMI] [LIN-PC] [29444] 内存泄漏测试套件初始化完成
[2025-08-13 17:35:15.218 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块启动完成
[2025-08-13 17:35:15.218 INF] [IndustrialHMI] [LIN-PC] [29444] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 17:35:15.219 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 17:35:15.219 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:15.219 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:35:15.219 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 发现模块类型: TestModuleMain
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 开始加载模块: TestModuleMain
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 TestModuleMain 注入Logger
[2025-08-13 17:35:15.221 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 17:35:15.222 INF] [IndustrialHMI] [LIN-PC] [29444] 开始初始化模块: 测试模块
[2025-08-13 17:35:15.222 WRN] [IndustrialHMI] [LIN-PC] [29444] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 17:35:15.222 DBG] [IndustrialHMI] [LIN-PC] [29444] 依赖注入验证通过
[2025-08-13 17:35:15.222 DBG] [IndustrialHMI] [LIN-PC] [29444] 创建TestModuleModel成功
[2025-08-13 17:35:15.225 DBG] [IndustrialHMI] [LIN-PC] [29444] 创建TestModuleView成功
[2025-08-13 17:35:15.225 DBG] [IndustrialHMI] [LIN-PC] [29444] TestModulePresenter创建完成
[2025-08-13 17:35:15.225 DBG] [IndustrialHMI] [LIN-PC] [29444] 创建TestModulePresenter成功
[2025-08-13 17:35:15.225 DBG] [IndustrialHMI] [LIN-PC] [29444] 事件订阅完成
[2025-08-13 17:35:15.225 INF] [IndustrialHMI] [LIN-PC] [29444] 模块初始化完成: 测试模块
[2025-08-13 17:35:15.226 INF] [IndustrialHMI] [LIN-PC] [29444] 开始启动模块: 测试模块
[2025-08-13 17:35:15.226 INF] [IndustrialHMI] [LIN-PC] [29444] 启动TestModulePresenter
[2025-08-13 17:35:15.233 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 模型启动完成
[2025-08-13 17:35:15.235 DBG] [IndustrialHMI] [LIN-PC] [29444] 系统事件订阅完成
[2025-08-13 17:35:15.235 INF] [IndustrialHMI] [LIN-PC] [29444] TestModulePresenter启动完成
[2025-08-13 17:35:15.235 INF] [IndustrialHMI] [LIN-PC] [29444] 模块启动完成: 测试模块
[2025-08-13 17:35:15.236 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理系统事件: ModuleStarted
[2025-08-13 17:35:15.236 INF] [IndustrialHMI] [LIN-PC] [29444] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 17:35:15.236 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 17:35:15.237 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:15.237 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:35:15.238 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理模块加载事件: 测试模块
[2025-08-13 17:35:15.238 INF] [IndustrialHMI] [LIN-PC] [29444] 模块加载完成，共加载 5 个模块
[2025-08-13 17:35:15.238 INF] [IndustrialHMI] [LIN-PC] [29444] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 17:35:15.239 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 报警管理 添加了UI标签页
[2025-08-13 17:35:15.239 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 通信测试 添加了UI标签页
[2025-08-13 17:35:15.240 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 设备监控 添加了UI标签页
[2025-08-13 17:35:15.241 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 17:35:15.241 DBG] [IndustrialHMI] [LIN-PC] [29444] 为模块 测试模块 添加了UI标签页
[2025-08-13 17:35:15.241 INF] [IndustrialHMI] [LIN-PC] [29444] 步骤5: 初始化主窗体
[2025-08-13 17:35:15.241 DBG] [IndustrialHMI] [LIN-PC] [29444] 主窗体初始化完成
[2025-08-13 17:35:15.241 INF] [IndustrialHMI] [LIN-PC] [29444] 应用程序初始化完成
[2025-08-13 17:35:15.241 INF] [IndustrialHMI] [LIN-PC] [29444] 应用程序初始化成功，启动主窗体
[2025-08-13 17:35:15.242 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到系统启动事件
[2025-08-13 17:35:15.243 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:15.243 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 17:35:15.244 INF] [IndustrialHMI] [LIN-PC] [29444] 模块 测试模块 收到系统启动事件
[2025-08-13 17:35:15.245 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到系统启动事件
[2025-08-13 17:35:15.326 DBG] [IndustrialHMI] [LIN-PC] [29444] 主窗体事件订阅完成
[2025-08-13 17:35:15.331 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据变化事件处理完成
[2025-08-13 17:35:15.332 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 17:35:15.334 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 定时状态更新 - 17:35:15
[2025-08-13 17:35:16.255 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 系统启动后初始化完成
[2025-08-13 17:35:16.384 INF] [IndustrialHMI] [LIN-PC] [29444] 用户请求关闭应用程序
[2025-08-13 17:35:16.385 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 17:35:16.385 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:16.385 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 17:35:16.386 INF] [IndustrialHMI] [LIN-PC] [29444] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 17:35:16.387 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到系统关闭事件
[2025-08-13 17:35:16.387 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据变化事件处理完成
[2025-08-13 17:35:16.388 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 系统关闭前清理完成
[2025-08-13 17:35:16.388 INF] [IndustrialHMI] [LIN-PC] [29444] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:35:16.388 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载所有模块
[2025-08-13 17:35:16.388 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载模块: 报警管理
[2025-08-13 17:35:16.389 INF] [IndustrialHMI] [LIN-PC] [29444] 停止报警管理模块
[2025-08-13 17:35:16.389 INF] [IndustrialHMI] [LIN-PC] [29444] 停止报警监控
[2025-08-13 17:35:16.389 INF] [IndustrialHMI] [LIN-PC] [29444] 停止报警监控
[2025-08-13 17:35:16.389 INF] [IndustrialHMI] [LIN-PC] [29444] 报警管理模块停止完成
[2025-08-13 17:35:16.389 INF] [IndustrialHMI] [LIN-PC] [29444] 开始释放报警管理模块资源
[2025-08-13 17:35:16.390 INF] [IndustrialHMI] [LIN-PC] [29444] 停止报警监控
[2025-08-13 17:35:16.390 INF] [IndustrialHMI] [LIN-PC] [29444] 停止报警监控
[2025-08-13 17:35:16.390 DBG] [IndustrialHMI] [LIN-PC] [29444] AlarmPresenter 资源释放完成
[2025-08-13 17:35:16.391 INF] [IndustrialHMI] [LIN-PC] [29444] 停止报警监控
[2025-08-13 17:35:16.391 DBG] [IndustrialHMI] [LIN-PC] [29444] AlarmModel 资源释放完成
[2025-08-13 17:35:16.397 INF] [IndustrialHMI] [LIN-PC] [29444] 报警管理模块资源释放完成
[2025-08-13 17:35:16.398 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 17:35:16.398 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:16.398 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 17:35:16.399 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理模块卸载事件: 报警管理
[2025-08-13 17:35:16.399 INF] [IndustrialHMI] [LIN-PC] [29444] 模块卸载成功: 报警管理
[2025-08-13 17:35:16.399 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载模块: 通信测试
[2025-08-13 17:35:16.399 INF] [IndustrialHMI] [LIN-PC] [29444] 停止 CommunicationTestModule
[2025-08-13 17:35:16.400 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据变化: EventMonitoring
[2025-08-13 17:35:16.400 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据变化: PerformanceMonitoring
[2025-08-13 17:35:16.400 INF] [IndustrialHMI] [LIN-PC] [29444] 测试已停止
[2025-08-13 17:35:16.400 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModule 停止完成
[2025-08-13 17:35:16.400 INF] [IndustrialHMI] [LIN-PC] [29444] 开始释放 CommunicationTestModule 资源
[2025-08-13 17:35:16.400 INF] [IndustrialHMI] [LIN-PC] [29444] 停止 CommunicationTestModule
[2025-08-13 17:35:16.400 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据变化: EventMonitoring
[2025-08-13 17:35:16.400 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据变化: PerformanceMonitoring
[2025-08-13 17:35:16.400 INF] [IndustrialHMI] [LIN-PC] [29444] 测试已停止
[2025-08-13 17:35:16.400 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModule 停止完成
[2025-08-13 17:35:16.400 DBG] [IndustrialHMI] [LIN-PC] [29444] 系统事件订阅已取消
[2025-08-13 17:35:16.401 DBG] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestPresenter 资源释放完成
[2025-08-13 17:35:16.401 DBG] [IndustrialHMI] [LIN-PC] [29444] EventMonitor 资源释放完成
[2025-08-13 17:35:16.401 INF] [IndustrialHMI] [LIN-PC] [29444] 测试已停止
[2025-08-13 17:35:16.401 DBG] [IndustrialHMI] [LIN-PC] [29444] TestCaseManager 资源释放完成
[2025-08-13 17:35:16.402 DBG] [IndustrialHMI] [LIN-PC] [29444] PerformanceMonitor 资源释放完成
[2025-08-13 17:35:16.402 DBG] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModel 资源释放完成
[2025-08-13 17:35:16.402 INF] [IndustrialHMI] [LIN-PC] [29444] CommunicationTestModule 资源释放完成
[2025-08-13 17:35:16.402 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 17:35:16.402 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:16.402 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 17:35:16.403 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理模块卸载事件: 通信测试
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 模块卸载成功: 通信测试
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载模块: 设备监控
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 停止设备监控模块
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 用户请求停止设备监控
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 停止设备监控
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 设备监控已停止
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 设备监控模块停止完成
[2025-08-13 17:35:16.403 INF] [IndustrialHMI] [LIN-PC] [29444] 开始释放设备监控模块资源
[2025-08-13 17:35:16.404 DBG] [IndustrialHMI] [LIN-PC] [29444] DevicePresenter 资源释放完成
[2025-08-13 17:35:16.404 INF] [IndustrialHMI] [LIN-PC] [29444] 停止设备监控
[2025-08-13 17:35:16.404 DBG] [IndustrialHMI] [LIN-PC] [29444] DeviceModel 资源释放完成
[2025-08-13 17:35:16.404 INF] [IndustrialHMI] [LIN-PC] [29444] 设备监控模块资源释放完成
[2025-08-13 17:35:16.404 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 17:35:16.405 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型数据已更新，视图已刷新
[2025-08-13 17:35:16.405 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 17:35:16.405 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理模块卸载事件: 设备监控
[2025-08-13 17:35:16.405 INF] [IndustrialHMI] [LIN-PC] [29444] 模块卸载成功: 设备监控
[2025-08-13 17:35:16.405 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载模块: 测试框架模块
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 停止测试框架模块
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 停止内存泄漏测试套件
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 内存泄漏测试套件已停止
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 停止性能测试套件
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 性能测试套件已停止
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 停止集成测试套件
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 集成测试套件已停止
[2025-08-13 17:35:16.406 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 模型已停止
[2025-08-13 17:35:16.406 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块停止完成
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 开始释放测试框架模块资源
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 停止内存泄漏测试套件
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 内存泄漏测试套件已停止
[2025-08-13 17:35:16.407 DBG] [IndustrialHMI] [LIN-PC] [29444] MemoryLeakTestSuite资源释放完成
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 停止性能测试套件
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 性能测试套件已停止
[2025-08-13 17:35:16.407 DBG] [IndustrialHMI] [LIN-PC] [29444] PerformanceTestSuite资源释放完成
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 停止集成测试套件
[2025-08-13 17:35:16.407 INF] [IndustrialHMI] [LIN-PC] [29444] 集成测试套件已停止
[2025-08-13 17:35:16.407 DBG] [IndustrialHMI] [LIN-PC] [29444] IntegrationTestSuite资源释放完成
[2025-08-13 17:35:16.407 DBG] [IndustrialHMI] [LIN-PC] [29444] TestFrameworkPresenter资源释放完成
[2025-08-13 17:35:16.408 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块资源释放完成
[2025-08-13 17:35:16.408 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 17:35:16.408 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理模块卸载事件: 测试框架模块
[2025-08-13 17:35:16.408 INF] [IndustrialHMI] [LIN-PC] [29444] 模块卸载成功: 测试框架模块
[2025-08-13 17:35:16.408 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载模块: 测试模块
[2025-08-13 17:35:16.409 INF] [IndustrialHMI] [LIN-PC] [29444] 开始停止模块: 测试模块
[2025-08-13 17:35:16.409 INF] [IndustrialHMI] [LIN-PC] [29444] 停止TestModulePresenter
[2025-08-13 17:35:16.409 DBG] [IndustrialHMI] [LIN-PC] [29444] 系统事件取消订阅完成
[2025-08-13 17:35:16.409 DBG] [IndustrialHMI] [LIN-PC] [29444] 模型状态变化: 模型停止完成
[2025-08-13 17:35:16.410 INF] [IndustrialHMI] [LIN-PC] [29444] TestModulePresenter停止完成
[2025-08-13 17:35:16.410 DBG] [IndustrialHMI] [LIN-PC] [29444] 事件取消订阅完成
[2025-08-13 17:35:16.410 INF] [IndustrialHMI] [LIN-PC] [29444] 模块停止完成: 测试模块
[2025-08-13 17:35:16.410 DBG] [IndustrialHMI] [LIN-PC] [29444] 处理系统事件: ModuleStopped
[2025-08-13 17:35:16.411 INF] [IndustrialHMI] [LIN-PC] [29444] 开始释放模块资源: 测试模块
[2025-08-13 17:35:16.411 INF] [IndustrialHMI] [LIN-PC] [29444] 释放TestModulePresenter资源
[2025-08-13 17:35:16.411 INF] [IndustrialHMI] [LIN-PC] [29444] TestModulePresenter资源释放完成
[2025-08-13 17:35:16.416 INF] [IndustrialHMI] [LIN-PC] [29444] 模块资源释放完成: 测试模块
[2025-08-13 17:35:16.416 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 17:35:16.418 ERR] [IndustrialHMI] [LIN-PC] [29444] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 17:35:16.424 INF] [IndustrialHMI] [LIN-PC] [29444] 模块卸载成功: 测试模块
[2025-08-13 17:35:16.424 INF] [IndustrialHMI] [LIN-PC] [29444] 所有模块卸载完成
[2025-08-13 17:35:16.424 INF] [IndustrialHMI] [LIN-PC] [29444] 应用程序关闭流程完成
[2025-08-13 17:35:16.424 INF] [IndustrialHMI] [LIN-PC] [29444] 主窗体已关闭，资源清理完成
[2025-08-13 17:35:16.446 INF] [IndustrialHMI] [LIN-PC] [29444] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 17:35:16.446 INF] [IndustrialHMI] [LIN-PC] [29444] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 17:35:16.446 INF] [IndustrialHMI] [LIN-PC] [29444] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:35:16.446 INF] [IndustrialHMI] [LIN-PC] [29444] 开始释放应用程序资源
[2025-08-13 17:35:16.447 INF] [IndustrialHMI] [LIN-PC] [29444] 开始卸载所有模块
[2025-08-13 17:35:16.447 INF] [IndustrialHMI] [LIN-PC] [29444] 所有模块卸载完成
[2025-08-13 17:35:16.447 INF] [IndustrialHMI] [LIN-PC] [29444] 应用程序资源释放完成
[2025-08-13 17:35:16.447 INF] [IndustrialHMI] [LIN-PC] [29444] === 应用程序正常退出 ===
﻿[2025-08-13 17:38:32.435 INF] [IndustrialHMI] [LIN-PC] [27408] === 应用程序启动 ===
[2025-08-13 17:38:32.467 INF] [IndustrialHMI] [LIN-PC] [27408] 应用程序版本: 1.0.0.0
[2025-08-13 17:38:32.467 INF] [IndustrialHMI] [LIN-PC] [27408] 启动参数: 
[2025-08-13 17:38:32.467 INF] [IndustrialHMI] [LIN-PC] [27408] 开始初始化应用程序
[2025-08-13 17:38:32.467 INF] [IndustrialHMI] [LIN-PC] [27408] 步骤1: 创建服务容器
[2025-08-13 17:38:32.471 INF] [IndustrialHMI] [LIN-PC] [27408] 开始创建DryIoc容器
[2025-08-13 17:38:32.483 DBG] [IndustrialHMI] [LIN-PC] [27408] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:38:32.487 DBG] [IndustrialHMI] [LIN-PC] [27408] 注册自定义日志记录器为单例
[2025-08-13 17:38:32.487 DBG] [IndustrialHMI] [LIN-PC] [27408] 注册EventAggregator为单例
[2025-08-13 17:38:32.492 DBG] [IndustrialHMI] [LIN-PC] [27408] 注册ConfigurationService为单例
[2025-08-13 17:38:32.503 DBG] [IndustrialHMI] [LIN-PC] [27408] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:38:32.528 DBG] [IndustrialHMI] [LIN-PC] [27408] 注册MainForm为单例
[2025-08-13 17:38:32.528 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始验证DryIoc容器配置
[2025-08-13 17:38:32.528 DBG] [IndustrialHMI] [LIN-PC] [27408] DryIoc容器配置验证通过
[2025-08-13 17:38:32.528 INF] [IndustrialHMI] [LIN-PC] [27408] DryIoc容器创建和配置完成
[2025-08-13 17:38:32.528 INF] [IndustrialHMI] [LIN-PC] [27408] 步骤2: 创建主窗体
[2025-08-13 17:38:32.528 INF] [IndustrialHMI] [LIN-PC] [27408] 步骤3: 创建模块加载器
[2025-08-13 17:38:32.528 INF] [IndustrialHMI] [LIN-PC] [27408] 步骤4: 加载模块
[2025-08-13 17:38:32.529 INF] [IndustrialHMI] [LIN-PC] [27408] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:38:32.530 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 17:38:32.536 DBG] [IndustrialHMI] [LIN-PC] [27408] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 发现模块类型: AlarmModuleMain
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.537 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.538 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载模块: AlarmModuleMain
[2025-08-13 17:38:32.538 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 17:38:32.538 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 17:38:32.539 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 17:38:32.539 INF] [IndustrialHMI] [LIN-PC] [27408] 开始初始化报警管理模块
[2025-08-13 17:38:32.541 DBG] [IndustrialHMI] [LIN-PC] [27408] 报警服务创建完成
[2025-08-13 17:38:32.549 DBG] [IndustrialHMI] [LIN-PC] [27408] 报警视图创建完成
[2025-08-13 17:38:32.550 DBG] [IndustrialHMI] [LIN-PC] [27408] AlarmModel 初始化完成
[2025-08-13 17:38:32.550 DBG] [IndustrialHMI] [LIN-PC] [27408] 报警模型创建完成
[2025-08-13 17:38:32.551 DBG] [IndustrialHMI] [LIN-PC] [27408] AlarmPresenter 初始化完成
[2025-08-13 17:38:32.551 DBG] [IndustrialHMI] [LIN-PC] [27408] 报警表示器创建完成
[2025-08-13 17:38:32.551 INF] [IndustrialHMI] [LIN-PC] [27408] MVP组件创建完成
[2025-08-13 17:38:32.551 DBG] [IndustrialHMI] [LIN-PC] [27408] 系统事件订阅完成
[2025-08-13 17:38:32.552 INF] [IndustrialHMI] [LIN-PC] [27408] 报警管理模块初始化完成
[2025-08-13 17:38:32.552 INF] [IndustrialHMI] [LIN-PC] [27408] 启动报警管理模块
[2025-08-13 17:38:32.552 INF] [IndustrialHMI] [LIN-PC] [27408] 启动报警监控
[2025-08-13 17:38:32.552 INF] [IndustrialHMI] [LIN-PC] [27408] 开始报警监控
[2025-08-13 17:38:32.552 INF] [IndustrialHMI] [LIN-PC] [27408] 报警管理模块启动完成
[2025-08-13 17:38:32.552 INF] [IndustrialHMI] [LIN-PC] [27408] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 17:38:32.554 DBG] [IndustrialHMI] [LIN-PC] [27408] 模块已加载: 报警管理
[2025-08-13 17:38:32.554 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 17:38:32.555 DBG] [IndustrialHMI] [LIN-PC] [27408] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 17:38:32.556 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 17:38:32.556 INF] [IndustrialHMI] [LIN-PC] [27408] 开始初始化 CommunicationTestModule
[2025-08-13 17:38:32.558 INF] [IndustrialHMI] [LIN-PC] [27408] 初始化了 8 个测试用例
[2025-08-13 17:38:32.558 INF] [IndustrialHMI] [LIN-PC] [27408] 性能监控器初始化完成
[2025-08-13 17:38:32.558 INF] [IndustrialHMI] [LIN-PC] [27408] CommunicationTestModel 初始化完成
[2025-08-13 17:38:32.563 DBG] [IndustrialHMI] [LIN-PC] [27408] CommunicationTestView 初始化完成
[2025-08-13 17:38:32.566 DBG] [IndustrialHMI] [LIN-PC] [27408] 视图数据初始化完成
[2025-08-13 17:38:32.566 INF] [IndustrialHMI] [LIN-PC] [27408] CommunicationTestPresenter 初始化完成
[2025-08-13 17:38:32.566 DBG] [IndustrialHMI] [LIN-PC] [27408] MVP组件创建完成
[2025-08-13 17:38:32.567 DBG] [IndustrialHMI] [LIN-PC] [27408] 系统事件订阅完成
[2025-08-13 17:38:32.567 INF] [IndustrialHMI] [LIN-PC] [27408] CommunicationTestModule 初始化完成
[2025-08-13 17:38:32.567 INF] [IndustrialHMI] [LIN-PC] [27408] 启动 CommunicationTestModule
[2025-08-13 17:38:32.567 INF] [IndustrialHMI] [LIN-PC] [27408] CommunicationTestModule 启动完成
[2025-08-13 17:38:32.567 INF] [IndustrialHMI] [LIN-PC] [27408] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 17:38:32.567 DBG] [IndustrialHMI] [LIN-PC] [27408] 模块已加载: 通信测试
[2025-08-13 17:38:32.567 DBG] [IndustrialHMI] [LIN-PC] [27408] 收到模块加载事件: 通信测试
[2025-08-13 17:38:32.567 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:38:32.568 DBG] [IndustrialHMI] [LIN-PC] [27408] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 发现模块类型: 
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:32.569 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 发现模块类型: DeviceModuleMain
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:32.570 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载模块: DeviceModuleMain
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 17:38:32.571 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 17:38:32.571 INF] [IndustrialHMI] [LIN-PC] [27408] 开始初始化设备监控模块
[2025-08-13 17:38:32.572 DBG] [IndustrialHMI] [LIN-PC] [27408] 设备服务创建完成
[2025-08-13 17:38:32.573 DBG] [IndustrialHMI] [LIN-PC] [27408] 设备视图创建完成
[2025-08-13 17:38:32.574 DBG] [IndustrialHMI] [LIN-PC] [27408] DeviceModel 初始化完成
[2025-08-13 17:38:32.574 DBG] [IndustrialHMI] [LIN-PC] [27408] 设备模型创建完成
[2025-08-13 17:38:32.574 DBG] [IndustrialHMI] [LIN-PC] [27408] DevicePresenter 初始化完成
[2025-08-13 17:38:32.574 DBG] [IndustrialHMI] [LIN-PC] [27408] 设备表示器创建完成
[2025-08-13 17:38:32.574 INF] [IndustrialHMI] [LIN-PC] [27408] MVP组件创建完成
[2025-08-13 17:38:32.575 DBG] [IndustrialHMI] [LIN-PC] [27408] 系统事件订阅完成
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 设备监控模块初始化完成
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 启动设备监控模块
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 用户请求开始设备监控
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 开始设备监控
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 设备监控已启动
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 设备监控模块启动完成
[2025-08-13 17:38:32.575 INF] [IndustrialHMI] [LIN-PC] [27408] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 17:38:32.576 DBG] [IndustrialHMI] [LIN-PC] [27408] 模块已加载: 设备监控
[2025-08-13 17:38:32.576 DBG] [IndustrialHMI] [LIN-PC] [27408] 收到模块加载事件: 设备监控
[2025-08-13 17:38:32.576 DBG] [IndustrialHMI] [LIN-PC] [27408] 模块已加载: 设备监控
[2025-08-13 17:38:32.576 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 17:38:32.577 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 17:38:32.578 INF] [IndustrialHMI] [LIN-PC] [27408] 开始初始化测试框架模块
[2025-08-13 17:38:32.578 DBG] [IndustrialHMI] [LIN-PC] [27408] ConfigurationService未注入（可选）
[2025-08-13 17:38:37.212 DBG] [IndustrialHMI] [LIN-PC] [27408] 初始化TestFrameworkPresenter
[2025-08-13 17:38:37.221 DBG] [IndustrialHMI] [LIN-PC] [27408] TestFrameworkPresenter初始化完成
[2025-08-13 17:38:37.221 DBG] [IndustrialHMI] [LIN-PC] [27408] 测试框架模块事件订阅完成
[2025-08-13 17:38:37.222 INF] [IndustrialHMI] [LIN-PC] [27408] 测试框架模块初始化完成
[2025-08-13 17:38:37.222 INF] [IndustrialHMI] [LIN-PC] [27408] 启动测试框架模块
[2025-08-13 17:38:37.222 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 模型已启动
[2025-08-13 17:38:37.222 DBG] [IndustrialHMI] [LIN-PC] [27408] 加载TestFramework数据
[2025-08-13 17:38:37.223 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型数据已更新，视图已刷新
[2025-08-13 17:38:37.223 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 数据加载完成
[2025-08-13 17:38:37.223 DBG] [IndustrialHMI] [LIN-PC] [27408] TestFramework数据加载完成
[2025-08-13 17:38:37.224 INF] [IndustrialHMI] [LIN-PC] [27408] 初始化集成测试套件
[2025-08-13 17:38:37.224 INF] [IndustrialHMI] [LIN-PC] [27408] 集成测试套件初始化完成
[2025-08-13 17:38:37.224 INF] [IndustrialHMI] [LIN-PC] [27408] 初始化性能测试套件
[2025-08-13 17:38:37.339 INF] [IndustrialHMI] [LIN-PC] [27408] 性能测试套件初始化完成
[2025-08-13 17:38:37.339 INF] [IndustrialHMI] [LIN-PC] [27408] 初始化内存泄漏测试套件
[2025-08-13 17:38:37.339 INF] [IndustrialHMI] [LIN-PC] [27408] 内存泄漏测试套件初始化完成
[2025-08-13 17:38:37.339 INF] [IndustrialHMI] [LIN-PC] [27408] 测试框架模块启动完成
[2025-08-13 17:38:37.339 INF] [IndustrialHMI] [LIN-PC] [27408] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 17:38:37.339 INF] [IndustrialHMI] [LIN-PC] [27408] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 17:38:37.340 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型数据已更新，视图已刷新
[2025-08-13 17:38:37.340 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:38:37.340 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 发现模块类型: TestModuleMain
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 开始加载模块: TestModuleMain
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 TestModuleMain 注入Logger
[2025-08-13 17:38:37.342 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 17:38:37.343 INF] [IndustrialHMI] [LIN-PC] [27408] 开始初始化模块: 测试模块
[2025-08-13 17:38:37.343 WRN] [IndustrialHMI] [LIN-PC] [27408] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 17:38:37.343 DBG] [IndustrialHMI] [LIN-PC] [27408] 依赖注入验证通过
[2025-08-13 17:38:37.343 DBG] [IndustrialHMI] [LIN-PC] [27408] 创建TestModuleModel成功
[2025-08-13 17:38:37.346 DBG] [IndustrialHMI] [LIN-PC] [27408] 创建TestModuleView成功
[2025-08-13 17:38:37.346 DBG] [IndustrialHMI] [LIN-PC] [27408] TestModulePresenter创建完成
[2025-08-13 17:38:37.346 DBG] [IndustrialHMI] [LIN-PC] [27408] 创建TestModulePresenter成功
[2025-08-13 17:38:37.346 DBG] [IndustrialHMI] [LIN-PC] [27408] 事件订阅完成
[2025-08-13 17:38:37.346 INF] [IndustrialHMI] [LIN-PC] [27408] 模块初始化完成: 测试模块
[2025-08-13 17:38:37.347 INF] [IndustrialHMI] [LIN-PC] [27408] 开始启动模块: 测试模块
[2025-08-13 17:38:37.347 INF] [IndustrialHMI] [LIN-PC] [27408] 启动TestModulePresenter
[2025-08-13 17:38:37.356 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 模型启动完成
[2025-08-13 17:38:37.357 DBG] [IndustrialHMI] [LIN-PC] [27408] 系统事件订阅完成
[2025-08-13 17:38:37.357 INF] [IndustrialHMI] [LIN-PC] [27408] TestModulePresenter启动完成
[2025-08-13 17:38:37.357 INF] [IndustrialHMI] [LIN-PC] [27408] 模块启动完成: 测试模块
[2025-08-13 17:38:37.359 DBG] [IndustrialHMI] [LIN-PC] [27408] 处理系统事件: ModuleStarted
[2025-08-13 17:38:37.359 INF] [IndustrialHMI] [LIN-PC] [27408] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 17:38:37.359 INF] [IndustrialHMI] [LIN-PC] [27408] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 17:38:37.360 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型数据已更新，视图已刷新
[2025-08-13 17:38:37.360 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:38:37.361 DBG] [IndustrialHMI] [LIN-PC] [27408] 处理模块加载事件: 测试模块
[2025-08-13 17:38:37.361 INF] [IndustrialHMI] [LIN-PC] [27408] 模块加载完成，共加载 5 个模块
[2025-08-13 17:38:37.361 INF] [IndustrialHMI] [LIN-PC] [27408] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 17:38:37.362 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 报警管理 添加了UI标签页
[2025-08-13 17:38:37.362 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 通信测试 添加了UI标签页
[2025-08-13 17:38:37.362 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 设备监控 添加了UI标签页
[2025-08-13 17:38:37.363 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 17:38:37.364 DBG] [IndustrialHMI] [LIN-PC] [27408] 为模块 测试模块 添加了UI标签页
[2025-08-13 17:38:37.364 INF] [IndustrialHMI] [LIN-PC] [27408] 步骤5: 初始化主窗体
[2025-08-13 17:38:37.364 DBG] [IndustrialHMI] [LIN-PC] [27408] 主窗体初始化完成
[2025-08-13 17:38:37.364 INF] [IndustrialHMI] [LIN-PC] [27408] 应用程序初始化完成
[2025-08-13 17:38:37.364 INF] [IndustrialHMI] [LIN-PC] [27408] 应用程序初始化成功，启动主窗体
[2025-08-13 17:38:37.364 INF] [IndustrialHMI] [LIN-PC] [27408] 测试框架模块收到系统启动事件
[2025-08-13 17:38:37.365 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型数据已更新，视图已刷新
[2025-08-13 17:38:37.365 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 17:38:37.365 INF] [IndustrialHMI] [LIN-PC] [27408] 模块 测试模块 收到系统启动事件
[2025-08-13 17:38:37.366 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 收到系统启动事件
[2025-08-13 17:38:37.464 DBG] [IndustrialHMI] [LIN-PC] [27408] 主窗体事件订阅完成
[2025-08-13 17:38:37.474 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型数据变化事件处理完成
[2025-08-13 17:38:37.476 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 17:38:37.478 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 定时状态更新 - 17:38:37
[2025-08-13 17:38:38.388 DBG] [IndustrialHMI] [LIN-PC] [27408] 模型状态变化: 系统启动后初始化完成
﻿[2025-08-13 17:38:40.510 INF] [IndustrialHMI] [LIN-PC] [21464] === 应用程序启动 ===
[2025-08-13 17:38:40.542 INF] [IndustrialHMI] [LIN-PC] [21464] 应用程序版本: 1.0.0.0
[2025-08-13 17:38:40.542 INF] [IndustrialHMI] [LIN-PC] [21464] 启动参数: 
[2025-08-13 17:38:40.543 INF] [IndustrialHMI] [LIN-PC] [21464] 开始初始化应用程序
[2025-08-13 17:38:40.543 INF] [IndustrialHMI] [LIN-PC] [21464] 步骤1: 创建服务容器
[2025-08-13 17:38:40.547 INF] [IndustrialHMI] [LIN-PC] [21464] 开始创建DryIoc容器
[2025-08-13 17:38:40.561 DBG] [IndustrialHMI] [LIN-PC] [21464] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:38:40.564 DBG] [IndustrialHMI] [LIN-PC] [21464] 注册自定义日志记录器为单例
[2025-08-13 17:38:40.564 DBG] [IndustrialHMI] [LIN-PC] [21464] 注册EventAggregator为单例
[2025-08-13 17:38:40.570 DBG] [IndustrialHMI] [LIN-PC] [21464] 注册ConfigurationService为单例
[2025-08-13 17:38:40.581 DBG] [IndustrialHMI] [LIN-PC] [21464] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:38:40.608 DBG] [IndustrialHMI] [LIN-PC] [21464] 注册MainForm为单例
[2025-08-13 17:38:40.609 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始验证DryIoc容器配置
[2025-08-13 17:38:40.609 DBG] [IndustrialHMI] [LIN-PC] [21464] DryIoc容器配置验证通过
[2025-08-13 17:38:40.609 INF] [IndustrialHMI] [LIN-PC] [21464] DryIoc容器创建和配置完成
[2025-08-13 17:38:40.609 INF] [IndustrialHMI] [LIN-PC] [21464] 步骤2: 创建主窗体
[2025-08-13 17:38:40.609 INF] [IndustrialHMI] [LIN-PC] [21464] 步骤3: 创建模块加载器
[2025-08-13 17:38:40.609 INF] [IndustrialHMI] [LIN-PC] [21464] 步骤4: 加载模块
[2025-08-13 17:38:40.609 INF] [IndustrialHMI] [LIN-PC] [21464] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:38:40.611 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 17:38:40.619 DBG] [IndustrialHMI] [LIN-PC] [21464] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 发现模块类型: AlarmModuleMain
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.620 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.621 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载模块: AlarmModuleMain
[2025-08-13 17:38:40.621 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 17:38:40.621 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 17:38:40.622 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 17:38:40.622 INF] [IndustrialHMI] [LIN-PC] [21464] 开始初始化报警管理模块
[2025-08-13 17:38:40.624 DBG] [IndustrialHMI] [LIN-PC] [21464] 报警服务创建完成
[2025-08-13 17:38:40.633 DBG] [IndustrialHMI] [LIN-PC] [21464] 报警视图创建完成
[2025-08-13 17:38:40.635 DBG] [IndustrialHMI] [LIN-PC] [21464] AlarmModel 初始化完成
[2025-08-13 17:38:40.635 DBG] [IndustrialHMI] [LIN-PC] [21464] 报警模型创建完成
[2025-08-13 17:38:40.636 DBG] [IndustrialHMI] [LIN-PC] [21464] AlarmPresenter 初始化完成
[2025-08-13 17:38:40.636 DBG] [IndustrialHMI] [LIN-PC] [21464] 报警表示器创建完成
[2025-08-13 17:38:40.636 INF] [IndustrialHMI] [LIN-PC] [21464] MVP组件创建完成
[2025-08-13 17:38:40.636 DBG] [IndustrialHMI] [LIN-PC] [21464] 系统事件订阅完成
[2025-08-13 17:38:40.636 INF] [IndustrialHMI] [LIN-PC] [21464] 报警管理模块初始化完成
[2025-08-13 17:38:40.636 INF] [IndustrialHMI] [LIN-PC] [21464] 启动报警管理模块
[2025-08-13 17:38:40.637 INF] [IndustrialHMI] [LIN-PC] [21464] 启动报警监控
[2025-08-13 17:38:40.637 INF] [IndustrialHMI] [LIN-PC] [21464] 开始报警监控
[2025-08-13 17:38:40.637 INF] [IndustrialHMI] [LIN-PC] [21464] 报警管理模块启动完成
[2025-08-13 17:38:40.637 INF] [IndustrialHMI] [LIN-PC] [21464] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 17:38:40.639 DBG] [IndustrialHMI] [LIN-PC] [21464] 模块已加载: 报警管理
[2025-08-13 17:38:40.639 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 17:38:40.640 DBG] [IndustrialHMI] [LIN-PC] [21464] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 17:38:40.641 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 17:38:40.642 INF] [IndustrialHMI] [LIN-PC] [21464] 开始初始化 CommunicationTestModule
[2025-08-13 17:38:40.643 INF] [IndustrialHMI] [LIN-PC] [21464] 初始化了 8 个测试用例
[2025-08-13 17:38:40.643 INF] [IndustrialHMI] [LIN-PC] [21464] 性能监控器初始化完成
[2025-08-13 17:38:40.643 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModel 初始化完成
[2025-08-13 17:38:40.649 DBG] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestView 初始化完成
[2025-08-13 17:38:40.653 DBG] [IndustrialHMI] [LIN-PC] [21464] 视图数据初始化完成
[2025-08-13 17:38:40.653 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestPresenter 初始化完成
[2025-08-13 17:38:40.653 DBG] [IndustrialHMI] [LIN-PC] [21464] MVP组件创建完成
[2025-08-13 17:38:40.654 DBG] [IndustrialHMI] [LIN-PC] [21464] 系统事件订阅完成
[2025-08-13 17:38:40.654 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModule 初始化完成
[2025-08-13 17:38:40.654 INF] [IndustrialHMI] [LIN-PC] [21464] 启动 CommunicationTestModule
[2025-08-13 17:38:40.654 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModule 启动完成
[2025-08-13 17:38:40.654 INF] [IndustrialHMI] [LIN-PC] [21464] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 17:38:40.654 DBG] [IndustrialHMI] [LIN-PC] [21464] 模块已加载: 通信测试
[2025-08-13 17:38:40.654 DBG] [IndustrialHMI] [LIN-PC] [21464] 收到模块加载事件: 通信测试
[2025-08-13 17:38:40.654 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:38:40.655 DBG] [IndustrialHMI] [LIN-PC] [21464] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 发现模块类型: 
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:38:40.656 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 发现模块类型: DeviceModuleMain
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.657 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载模块: DeviceModuleMain
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 17:38:40.658 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 17:38:40.658 INF] [IndustrialHMI] [LIN-PC] [21464] 开始初始化设备监控模块
[2025-08-13 17:38:40.659 DBG] [IndustrialHMI] [LIN-PC] [21464] 设备服务创建完成
[2025-08-13 17:38:40.661 DBG] [IndustrialHMI] [LIN-PC] [21464] 设备视图创建完成
[2025-08-13 17:38:40.662 DBG] [IndustrialHMI] [LIN-PC] [21464] DeviceModel 初始化完成
[2025-08-13 17:38:40.662 DBG] [IndustrialHMI] [LIN-PC] [21464] 设备模型创建完成
[2025-08-13 17:38:40.662 DBG] [IndustrialHMI] [LIN-PC] [21464] DevicePresenter 初始化完成
[2025-08-13 17:38:40.662 DBG] [IndustrialHMI] [LIN-PC] [21464] 设备表示器创建完成
[2025-08-13 17:38:40.662 INF] [IndustrialHMI] [LIN-PC] [21464] MVP组件创建完成
[2025-08-13 17:38:40.663 DBG] [IndustrialHMI] [LIN-PC] [21464] 系统事件订阅完成
[2025-08-13 17:38:40.663 INF] [IndustrialHMI] [LIN-PC] [21464] 设备监控模块初始化完成
[2025-08-13 17:38:40.663 INF] [IndustrialHMI] [LIN-PC] [21464] 启动设备监控模块
[2025-08-13 17:38:40.663 INF] [IndustrialHMI] [LIN-PC] [21464] 用户请求开始设备监控
[2025-08-13 17:38:40.663 INF] [IndustrialHMI] [LIN-PC] [21464] 开始设备监控
[2025-08-13 17:38:40.664 INF] [IndustrialHMI] [LIN-PC] [21464] 设备监控已启动
[2025-08-13 17:38:40.664 INF] [IndustrialHMI] [LIN-PC] [21464] 设备监控模块启动完成
[2025-08-13 17:38:40.664 INF] [IndustrialHMI] [LIN-PC] [21464] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 17:38:40.664 DBG] [IndustrialHMI] [LIN-PC] [21464] 模块已加载: 设备监控
[2025-08-13 17:38:40.664 DBG] [IndustrialHMI] [LIN-PC] [21464] 收到模块加载事件: 设备监控
[2025-08-13 17:38:40.664 DBG] [IndustrialHMI] [LIN-PC] [21464] 模块已加载: 设备监控
[2025-08-13 17:38:40.664 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 17:38:40.665 DBG] [IndustrialHMI] [LIN-PC] [21464] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 17:38:40.666 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 17:38:40.667 INF] [IndustrialHMI] [LIN-PC] [21464] 开始初始化测试框架模块
[2025-08-13 17:38:40.667 DBG] [IndustrialHMI] [LIN-PC] [21464] ConfigurationService未注入（可选）
[2025-08-13 17:38:41.157 DBG] [IndustrialHMI] [LIN-PC] [21464] 初始化TestFrameworkPresenter
[2025-08-13 17:38:41.166 DBG] [IndustrialHMI] [LIN-PC] [21464] TestFrameworkPresenter初始化完成
[2025-08-13 17:38:41.166 DBG] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块事件订阅完成
[2025-08-13 17:38:41.167 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块初始化完成
[2025-08-13 17:38:41.167 INF] [IndustrialHMI] [LIN-PC] [21464] 启动测试框架模块
[2025-08-13 17:38:41.167 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 模型已启动
[2025-08-13 17:38:41.167 DBG] [IndustrialHMI] [LIN-PC] [21464] 加载TestFramework数据
[2025-08-13 17:38:41.168 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:41.168 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 数据加载完成
[2025-08-13 17:38:41.169 DBG] [IndustrialHMI] [LIN-PC] [21464] TestFramework数据加载完成
[2025-08-13 17:38:41.169 INF] [IndustrialHMI] [LIN-PC] [21464] 初始化集成测试套件
[2025-08-13 17:38:41.169 INF] [IndustrialHMI] [LIN-PC] [21464] 集成测试套件初始化完成
[2025-08-13 17:38:41.169 INF] [IndustrialHMI] [LIN-PC] [21464] 初始化性能测试套件
[2025-08-13 17:38:41.286 INF] [IndustrialHMI] [LIN-PC] [21464] 性能测试套件初始化完成
[2025-08-13 17:38:41.287 INF] [IndustrialHMI] [LIN-PC] [21464] 初始化内存泄漏测试套件
[2025-08-13 17:38:41.287 INF] [IndustrialHMI] [LIN-PC] [21464] 内存泄漏测试套件初始化完成
[2025-08-13 17:38:41.287 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块启动完成
[2025-08-13 17:38:41.287 INF] [IndustrialHMI] [LIN-PC] [21464] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 17:38:41.288 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 17:38:41.290 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:41.291 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:38:41.291 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:38:41.293 DBG] [IndustrialHMI] [LIN-PC] [21464] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 发现模块类型: TestModuleMain
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 开始加载模块: TestModuleMain
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 TestModuleMain 注入Logger
[2025-08-13 17:38:41.294 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 17:38:41.294 INF] [IndustrialHMI] [LIN-PC] [21464] 开始初始化模块: 测试模块
[2025-08-13 17:38:41.295 WRN] [IndustrialHMI] [LIN-PC] [21464] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 17:38:41.295 DBG] [IndustrialHMI] [LIN-PC] [21464] 依赖注入验证通过
[2025-08-13 17:38:41.295 DBG] [IndustrialHMI] [LIN-PC] [21464] 创建TestModuleModel成功
[2025-08-13 17:38:41.297 DBG] [IndustrialHMI] [LIN-PC] [21464] 创建TestModuleView成功
[2025-08-13 17:38:41.297 DBG] [IndustrialHMI] [LIN-PC] [21464] TestModulePresenter创建完成
[2025-08-13 17:38:41.297 DBG] [IndustrialHMI] [LIN-PC] [21464] 创建TestModulePresenter成功
[2025-08-13 17:38:41.297 DBG] [IndustrialHMI] [LIN-PC] [21464] 事件订阅完成
[2025-08-13 17:38:41.297 INF] [IndustrialHMI] [LIN-PC] [21464] 模块初始化完成: 测试模块
[2025-08-13 17:38:41.298 INF] [IndustrialHMI] [LIN-PC] [21464] 开始启动模块: 测试模块
[2025-08-13 17:38:41.298 INF] [IndustrialHMI] [LIN-PC] [21464] 启动TestModulePresenter
[2025-08-13 17:38:41.305 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 模型启动完成
[2025-08-13 17:38:41.307 DBG] [IndustrialHMI] [LIN-PC] [21464] 系统事件订阅完成
[2025-08-13 17:38:41.307 INF] [IndustrialHMI] [LIN-PC] [21464] TestModulePresenter启动完成
[2025-08-13 17:38:41.307 INF] [IndustrialHMI] [LIN-PC] [21464] 模块启动完成: 测试模块
[2025-08-13 17:38:41.307 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理系统事件: ModuleStarted
[2025-08-13 17:38:41.307 INF] [IndustrialHMI] [LIN-PC] [21464] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 17:38:41.307 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 17:38:41.307 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:41.308 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:38:41.308 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理模块加载事件: 测试模块
[2025-08-13 17:38:41.308 INF] [IndustrialHMI] [LIN-PC] [21464] 模块加载完成，共加载 5 个模块
[2025-08-13 17:38:41.308 INF] [IndustrialHMI] [LIN-PC] [21464] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 17:38:41.310 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 报警管理 添加了UI标签页
[2025-08-13 17:38:41.310 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 通信测试 添加了UI标签页
[2025-08-13 17:38:41.310 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 设备监控 添加了UI标签页
[2025-08-13 17:38:41.311 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 17:38:41.311 DBG] [IndustrialHMI] [LIN-PC] [21464] 为模块 测试模块 添加了UI标签页
[2025-08-13 17:38:41.311 INF] [IndustrialHMI] [LIN-PC] [21464] 步骤5: 初始化主窗体
[2025-08-13 17:38:41.311 DBG] [IndustrialHMI] [LIN-PC] [21464] 主窗体初始化完成
[2025-08-13 17:38:41.311 INF] [IndustrialHMI] [LIN-PC] [21464] 应用程序初始化完成
[2025-08-13 17:38:41.311 INF] [IndustrialHMI] [LIN-PC] [21464] 应用程序初始化成功，启动主窗体
[2025-08-13 17:38:41.312 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到系统启动事件
[2025-08-13 17:38:41.312 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:41.312 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 17:38:41.313 INF] [IndustrialHMI] [LIN-PC] [21464] 模块 测试模块 收到系统启动事件
[2025-08-13 17:38:41.313 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到系统启动事件
[2025-08-13 17:38:41.403 DBG] [IndustrialHMI] [LIN-PC] [21464] 主窗体事件订阅完成
[2025-08-13 17:38:41.408 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据变化事件处理完成
[2025-08-13 17:38:41.409 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 17:38:41.410 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 定时状态更新 - 17:38:41
[2025-08-13 17:38:42.338 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 系统启动后初始化完成
[2025-08-13 17:38:42.443 INF] [IndustrialHMI] [LIN-PC] [21464] 用户请求关闭应用程序
[2025-08-13 17:38:42.443 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 17:38:42.444 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:42.444 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 17:38:42.444 INF] [IndustrialHMI] [LIN-PC] [21464] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 17:38:42.445 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到系统关闭事件
[2025-08-13 17:38:42.446 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据变化事件处理完成
[2025-08-13 17:38:42.446 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 系统关闭前清理完成
[2025-08-13 17:38:42.447 INF] [IndustrialHMI] [LIN-PC] [21464] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:38:42.447 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载所有模块
[2025-08-13 17:38:42.447 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载模块: 报警管理
[2025-08-13 17:38:42.447 INF] [IndustrialHMI] [LIN-PC] [21464] 停止报警管理模块
[2025-08-13 17:38:42.447 INF] [IndustrialHMI] [LIN-PC] [21464] 停止报警监控
[2025-08-13 17:38:42.447 INF] [IndustrialHMI] [LIN-PC] [21464] 停止报警监控
[2025-08-13 17:38:42.448 INF] [IndustrialHMI] [LIN-PC] [21464] 报警管理模块停止完成
[2025-08-13 17:38:42.448 INF] [IndustrialHMI] [LIN-PC] [21464] 开始释放报警管理模块资源
[2025-08-13 17:38:42.449 INF] [IndustrialHMI] [LIN-PC] [21464] 停止报警监控
[2025-08-13 17:38:42.449 INF] [IndustrialHMI] [LIN-PC] [21464] 停止报警监控
[2025-08-13 17:38:42.449 DBG] [IndustrialHMI] [LIN-PC] [21464] AlarmPresenter 资源释放完成
[2025-08-13 17:38:42.449 INF] [IndustrialHMI] [LIN-PC] [21464] 停止报警监控
[2025-08-13 17:38:42.449 DBG] [IndustrialHMI] [LIN-PC] [21464] AlarmModel 资源释放完成
[2025-08-13 17:38:42.454 INF] [IndustrialHMI] [LIN-PC] [21464] 报警管理模块资源释放完成
[2025-08-13 17:38:42.455 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 17:38:42.455 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:42.455 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 17:38:42.456 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理模块卸载事件: 报警管理
[2025-08-13 17:38:42.456 INF] [IndustrialHMI] [LIN-PC] [21464] 模块卸载成功: 报警管理
[2025-08-13 17:38:42.456 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载模块: 通信测试
[2025-08-13 17:38:42.456 INF] [IndustrialHMI] [LIN-PC] [21464] 停止 CommunicationTestModule
[2025-08-13 17:38:42.457 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据变化: EventMonitoring
[2025-08-13 17:38:42.457 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据变化: PerformanceMonitoring
[2025-08-13 17:38:42.457 INF] [IndustrialHMI] [LIN-PC] [21464] 测试已停止
[2025-08-13 17:38:42.457 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModule 停止完成
[2025-08-13 17:38:42.457 INF] [IndustrialHMI] [LIN-PC] [21464] 开始释放 CommunicationTestModule 资源
[2025-08-13 17:38:42.457 INF] [IndustrialHMI] [LIN-PC] [21464] 停止 CommunicationTestModule
[2025-08-13 17:38:42.457 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据变化: EventMonitoring
[2025-08-13 17:38:42.457 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据变化: PerformanceMonitoring
[2025-08-13 17:38:42.457 INF] [IndustrialHMI] [LIN-PC] [21464] 测试已停止
[2025-08-13 17:38:42.457 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModule 停止完成
[2025-08-13 17:38:42.457 DBG] [IndustrialHMI] [LIN-PC] [21464] 系统事件订阅已取消
[2025-08-13 17:38:42.458 DBG] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestPresenter 资源释放完成
[2025-08-13 17:38:42.458 DBG] [IndustrialHMI] [LIN-PC] [21464] EventMonitor 资源释放完成
[2025-08-13 17:38:42.458 INF] [IndustrialHMI] [LIN-PC] [21464] 测试已停止
[2025-08-13 17:38:42.458 DBG] [IndustrialHMI] [LIN-PC] [21464] TestCaseManager 资源释放完成
[2025-08-13 17:38:42.459 DBG] [IndustrialHMI] [LIN-PC] [21464] PerformanceMonitor 资源释放完成
[2025-08-13 17:38:42.459 DBG] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModel 资源释放完成
[2025-08-13 17:38:42.459 INF] [IndustrialHMI] [LIN-PC] [21464] CommunicationTestModule 资源释放完成
[2025-08-13 17:38:42.459 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 17:38:42.459 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:42.459 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 17:38:42.460 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理模块卸载事件: 通信测试
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 模块卸载成功: 通信测试
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载模块: 设备监控
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 停止设备监控模块
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 用户请求停止设备监控
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 停止设备监控
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 设备监控已停止
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 设备监控模块停止完成
[2025-08-13 17:38:42.460 INF] [IndustrialHMI] [LIN-PC] [21464] 开始释放设备监控模块资源
[2025-08-13 17:38:42.461 DBG] [IndustrialHMI] [LIN-PC] [21464] DevicePresenter 资源释放完成
[2025-08-13 17:38:42.461 INF] [IndustrialHMI] [LIN-PC] [21464] 停止设备监控
[2025-08-13 17:38:42.461 DBG] [IndustrialHMI] [LIN-PC] [21464] DeviceModel 资源释放完成
[2025-08-13 17:38:42.461 INF] [IndustrialHMI] [LIN-PC] [21464] 设备监控模块资源释放完成
[2025-08-13 17:38:42.461 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 17:38:42.462 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型数据已更新，视图已刷新
[2025-08-13 17:38:42.462 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 17:38:42.462 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理模块卸载事件: 设备监控
[2025-08-13 17:38:42.462 INF] [IndustrialHMI] [LIN-PC] [21464] 模块卸载成功: 设备监控
[2025-08-13 17:38:42.462 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载模块: 测试框架模块
[2025-08-13 17:38:42.462 INF] [IndustrialHMI] [LIN-PC] [21464] 停止测试框架模块
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 停止内存泄漏测试套件
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 内存泄漏测试套件已停止
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 停止性能测试套件
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 性能测试套件已停止
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 停止集成测试套件
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 集成测试套件已停止
[2025-08-13 17:38:42.463 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 模型已停止
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块停止完成
[2025-08-13 17:38:42.463 INF] [IndustrialHMI] [LIN-PC] [21464] 开始释放测试框架模块资源
[2025-08-13 17:38:42.464 INF] [IndustrialHMI] [LIN-PC] [21464] 停止内存泄漏测试套件
[2025-08-13 17:38:42.464 INF] [IndustrialHMI] [LIN-PC] [21464] 内存泄漏测试套件已停止
[2025-08-13 17:38:42.464 DBG] [IndustrialHMI] [LIN-PC] [21464] MemoryLeakTestSuite资源释放完成
[2025-08-13 17:38:42.464 INF] [IndustrialHMI] [LIN-PC] [21464] 停止性能测试套件
[2025-08-13 17:38:42.464 INF] [IndustrialHMI] [LIN-PC] [21464] 性能测试套件已停止
[2025-08-13 17:38:42.464 DBG] [IndustrialHMI] [LIN-PC] [21464] PerformanceTestSuite资源释放完成
[2025-08-13 17:38:42.464 INF] [IndustrialHMI] [LIN-PC] [21464] 停止集成测试套件
[2025-08-13 17:38:42.464 INF] [IndustrialHMI] [LIN-PC] [21464] 集成测试套件已停止
[2025-08-13 17:38:42.464 DBG] [IndustrialHMI] [LIN-PC] [21464] IntegrationTestSuite资源释放完成
[2025-08-13 17:38:42.464 DBG] [IndustrialHMI] [LIN-PC] [21464] TestFrameworkPresenter资源释放完成
[2025-08-13 17:38:42.465 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块资源释放完成
[2025-08-13 17:38:42.465 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 17:38:42.465 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理模块卸载事件: 测试框架模块
[2025-08-13 17:38:42.465 INF] [IndustrialHMI] [LIN-PC] [21464] 模块卸载成功: 测试框架模块
[2025-08-13 17:38:42.465 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载模块: 测试模块
[2025-08-13 17:38:42.465 INF] [IndustrialHMI] [LIN-PC] [21464] 开始停止模块: 测试模块
[2025-08-13 17:38:42.465 INF] [IndustrialHMI] [LIN-PC] [21464] 停止TestModulePresenter
[2025-08-13 17:38:42.466 DBG] [IndustrialHMI] [LIN-PC] [21464] 系统事件取消订阅完成
[2025-08-13 17:38:42.466 DBG] [IndustrialHMI] [LIN-PC] [21464] 模型状态变化: 模型停止完成
[2025-08-13 17:38:42.466 INF] [IndustrialHMI] [LIN-PC] [21464] TestModulePresenter停止完成
[2025-08-13 17:38:42.467 DBG] [IndustrialHMI] [LIN-PC] [21464] 事件取消订阅完成
[2025-08-13 17:38:42.467 INF] [IndustrialHMI] [LIN-PC] [21464] 模块停止完成: 测试模块
[2025-08-13 17:38:42.467 DBG] [IndustrialHMI] [LIN-PC] [21464] 处理系统事件: ModuleStopped
[2025-08-13 17:38:42.467 INF] [IndustrialHMI] [LIN-PC] [21464] 开始释放模块资源: 测试模块
[2025-08-13 17:38:42.467 INF] [IndustrialHMI] [LIN-PC] [21464] 释放TestModulePresenter资源
[2025-08-13 17:38:42.468 INF] [IndustrialHMI] [LIN-PC] [21464] TestModulePresenter资源释放完成
[2025-08-13 17:38:42.472 INF] [IndustrialHMI] [LIN-PC] [21464] 模块资源释放完成: 测试模块
[2025-08-13 17:38:42.472 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 17:38:42.475 ERR] [IndustrialHMI] [LIN-PC] [21464] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 17:38:42.480 INF] [IndustrialHMI] [LIN-PC] [21464] 模块卸载成功: 测试模块
[2025-08-13 17:38:42.480 INF] [IndustrialHMI] [LIN-PC] [21464] 所有模块卸载完成
[2025-08-13 17:38:42.480 INF] [IndustrialHMI] [LIN-PC] [21464] 应用程序关闭流程完成
[2025-08-13 17:38:42.481 INF] [IndustrialHMI] [LIN-PC] [21464] 主窗体已关闭，资源清理完成
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 收到系统关闭事件，原因: UserRequest
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 开始释放应用程序资源
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 开始卸载所有模块
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 所有模块卸载完成
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] 应用程序资源释放完成
[2025-08-13 17:38:42.501 INF] [IndustrialHMI] [LIN-PC] [21464] === 应用程序正常退出 ===
﻿[2025-08-13 17:59:52.920 INF] [IndustrialHMI] [LIN-PC] [276] === 应用程序启动 ===
[2025-08-13 17:59:52.953 INF] [IndustrialHMI] [LIN-PC] [276] 应用程序版本: 1.0.0.0
[2025-08-13 17:59:52.953 INF] [IndustrialHMI] [LIN-PC] [276] 启动参数: 
[2025-08-13 17:59:52.953 INF] [IndustrialHMI] [LIN-PC] [276] 开始初始化应用程序
[2025-08-13 17:59:52.953 INF] [IndustrialHMI] [LIN-PC] [276] 步骤1: 创建服务容器
[2025-08-13 17:59:52.957 INF] [IndustrialHMI] [LIN-PC] [276] 开始创建DryIoc容器
[2025-08-13 17:59:52.970 DBG] [IndustrialHMI] [LIN-PC] [276] DryIoc容器创建成功，开始注册服务
[2025-08-13 17:59:52.973 DBG] [IndustrialHMI] [LIN-PC] [276] 注册自定义日志记录器为单例
[2025-08-13 17:59:52.974 DBG] [IndustrialHMI] [LIN-PC] [276] 注册EventAggregator为单例
[2025-08-13 17:59:52.983 DBG] [IndustrialHMI] [LIN-PC] [276] 注册ConfigurationService为单例，已添加多个配置源并启用热更新
[2025-08-13 17:59:52.993 DBG] [IndustrialHMI] [LIN-PC] [276] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 17:59:53.046 DBG] [IndustrialHMI] [LIN-PC] [276] 注册MainForm为单例
[2025-08-13 17:59:53.046 DBG] [IndustrialHMI] [LIN-PC] [276] 开始验证DryIoc容器配置
[2025-08-13 17:59:53.046 DBG] [IndustrialHMI] [LIN-PC] [276] DryIoc容器配置验证通过
[2025-08-13 17:59:53.046 INF] [IndustrialHMI] [LIN-PC] [276] DryIoc容器创建和配置完成
[2025-08-13 17:59:53.046 INF] [IndustrialHMI] [LIN-PC] [276] 步骤2: 创建主窗体
[2025-08-13 17:59:53.046 INF] [IndustrialHMI] [LIN-PC] [276] 步骤3: 创建模块加载器
[2025-08-13 17:59:53.046 INF] [IndustrialHMI] [LIN-PC] [276] 步骤4: 加载模块
[2025-08-13 17:59:53.047 INF] [IndustrialHMI] [LIN-PC] [276] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 17:59:53.048 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 17:59:53.055 DBG] [IndustrialHMI] [LIN-PC] [276] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:59:53.056 DBG] [IndustrialHMI] [LIN-PC] [276] 发现模块类型: AlarmModuleMain
[2025-08-13 17:59:53.056 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:59:53.056 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 17:59:53.056 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 17:59:53.056 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:59:53.056 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.057 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载模块: AlarmModuleMain
[2025-08-13 17:59:53.058 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 17:59:53.058 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 17:59:53.059 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 17:59:53.059 INF] [IndustrialHMI] [LIN-PC] [276] 开始初始化报警管理模块
[2025-08-13 17:59:53.061 DBG] [IndustrialHMI] [LIN-PC] [276] 报警服务创建完成
[2025-08-13 17:59:53.070 DBG] [IndustrialHMI] [LIN-PC] [276] 报警视图创建完成
[2025-08-13 17:59:53.071 DBG] [IndustrialHMI] [LIN-PC] [276] AlarmModel 初始化完成
[2025-08-13 17:59:53.071 DBG] [IndustrialHMI] [LIN-PC] [276] 报警模型创建完成
[2025-08-13 17:59:53.072 DBG] [IndustrialHMI] [LIN-PC] [276] AlarmPresenter 初始化完成
[2025-08-13 17:59:53.072 DBG] [IndustrialHMI] [LIN-PC] [276] 报警表示器创建完成
[2025-08-13 17:59:53.072 INF] [IndustrialHMI] [LIN-PC] [276] MVP组件创建完成
[2025-08-13 17:59:53.073 DBG] [IndustrialHMI] [LIN-PC] [276] 系统事件订阅完成
[2025-08-13 17:59:53.073 INF] [IndustrialHMI] [LIN-PC] [276] 报警管理模块初始化完成
[2025-08-13 17:59:53.073 INF] [IndustrialHMI] [LIN-PC] [276] 启动报警管理模块
[2025-08-13 17:59:53.073 INF] [IndustrialHMI] [LIN-PC] [276] 启动报警监控
[2025-08-13 17:59:53.073 INF] [IndustrialHMI] [LIN-PC] [276] 开始报警监控
[2025-08-13 17:59:53.073 INF] [IndustrialHMI] [LIN-PC] [276] 报警管理模块启动完成
[2025-08-13 17:59:53.074 INF] [IndustrialHMI] [LIN-PC] [276] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 17:59:53.075 DBG] [IndustrialHMI] [LIN-PC] [276] 模块已加载: 报警管理
[2025-08-13 17:59:53.076 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 17:59:53.077 DBG] [IndustrialHMI] [LIN-PC] [276] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 17:59:53.078 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 17:59:53.079 INF] [IndustrialHMI] [LIN-PC] [276] 开始初始化 CommunicationTestModule
[2025-08-13 17:59:53.080 INF] [IndustrialHMI] [LIN-PC] [276] 初始化了 8 个测试用例
[2025-08-13 17:59:53.080 INF] [IndustrialHMI] [LIN-PC] [276] 性能监控器初始化完成
[2025-08-13 17:59:53.081 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModel 初始化完成
[2025-08-13 17:59:53.088 DBG] [IndustrialHMI] [LIN-PC] [276] CommunicationTestView 初始化完成
[2025-08-13 17:59:53.092 DBG] [IndustrialHMI] [LIN-PC] [276] 视图数据初始化完成
[2025-08-13 17:59:53.092 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestPresenter 初始化完成
[2025-08-13 17:59:53.092 DBG] [IndustrialHMI] [LIN-PC] [276] MVP组件创建完成
[2025-08-13 17:59:53.092 DBG] [IndustrialHMI] [LIN-PC] [276] 系统事件订阅完成
[2025-08-13 17:59:53.092 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModule 初始化完成
[2025-08-13 17:59:53.092 INF] [IndustrialHMI] [LIN-PC] [276] 启动 CommunicationTestModule
[2025-08-13 17:59:53.092 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModule 启动完成
[2025-08-13 17:59:53.092 INF] [IndustrialHMI] [LIN-PC] [276] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 17:59:53.092 DBG] [IndustrialHMI] [LIN-PC] [276] 模块已加载: 通信测试
[2025-08-13 17:59:53.092 DBG] [IndustrialHMI] [LIN-PC] [276] 收到模块加载事件: 通信测试
[2025-08-13 17:59:53.092 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 发现模块类型: 
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.094 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.095 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.095 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 17:59:53.095 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 17:59:53.096 DBG] [IndustrialHMI] [LIN-PC] [276] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:59:53.096 DBG] [IndustrialHMI] [LIN-PC] [276] 发现模块类型: DeviceModuleMain
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载模块: DeviceModuleMain
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 17:59:53.097 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 17:59:53.097 INF] [IndustrialHMI] [LIN-PC] [276] 开始初始化设备监控模块
[2025-08-13 17:59:53.098 DBG] [IndustrialHMI] [LIN-PC] [276] 设备服务创建完成
[2025-08-13 17:59:53.100 DBG] [IndustrialHMI] [LIN-PC] [276] 设备视图创建完成
[2025-08-13 17:59:53.101 DBG] [IndustrialHMI] [LIN-PC] [276] DeviceModel 初始化完成
[2025-08-13 17:59:53.101 DBG] [IndustrialHMI] [LIN-PC] [276] 设备模型创建完成
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] DevicePresenter 初始化完成
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] 设备表示器创建完成
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] MVP组件创建完成
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] 系统事件订阅完成
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 设备监控模块初始化完成
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 启动设备监控模块
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 用户请求开始设备监控
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 开始设备监控
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 设备监控已启动
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 设备监控模块启动完成
[2025-08-13 17:59:53.102 INF] [IndustrialHMI] [LIN-PC] [276] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] 模块已加载: 设备监控
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] 收到模块加载事件: 设备监控
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] 模块已加载: 设备监控
[2025-08-13 17:59:53.102 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 17:59:53.104 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 17:59:53.105 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 17:59:53.105 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 17:59:53.105 INF] [IndustrialHMI] [LIN-PC] [276] 开始初始化测试框架模块
[2025-08-13 17:59:53.105 DBG] [IndustrialHMI] [LIN-PC] [276] ConfigurationService未注入（可选）
[2025-08-13 17:59:57.888 DBG] [IndustrialHMI] [LIN-PC] [276] 初始化TestFrameworkPresenter
[2025-08-13 17:59:57.904 DBG] [IndustrialHMI] [LIN-PC] [276] TestFrameworkPresenter初始化完成
[2025-08-13 17:59:57.905 DBG] [IndustrialHMI] [LIN-PC] [276] 测试框架模块事件订阅完成
[2025-08-13 17:59:57.905 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块初始化完成
[2025-08-13 17:59:57.905 INF] [IndustrialHMI] [LIN-PC] [276] 启动测试框架模块
[2025-08-13 17:59:57.905 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 模型已启动
[2025-08-13 17:59:57.906 DBG] [IndustrialHMI] [LIN-PC] [276] 加载TestFramework数据
[2025-08-13 17:59:57.906 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 17:59:57.907 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 数据加载完成
[2025-08-13 17:59:57.907 DBG] [IndustrialHMI] [LIN-PC] [276] TestFramework数据加载完成
[2025-08-13 17:59:57.907 INF] [IndustrialHMI] [LIN-PC] [276] 初始化集成测试套件
[2025-08-13 17:59:57.907 INF] [IndustrialHMI] [LIN-PC] [276] 集成测试套件初始化完成
[2025-08-13 17:59:57.907 INF] [IndustrialHMI] [LIN-PC] [276] 初始化性能测试套件
[2025-08-13 17:59:58.032 INF] [IndustrialHMI] [LIN-PC] [276] 性能测试套件初始化完成
[2025-08-13 17:59:58.032 INF] [IndustrialHMI] [LIN-PC] [276] 初始化内存泄漏测试套件
[2025-08-13 17:59:58.032 INF] [IndustrialHMI] [LIN-PC] [276] 内存泄漏测试套件初始化完成
[2025-08-13 17:59:58.032 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块启动完成
[2025-08-13 17:59:58.032 INF] [IndustrialHMI] [LIN-PC] [276] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 17:59:58.032 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 17:59:58.033 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 17:59:58.033 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:59:58.033 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 发现模块类型: TestModuleMain
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 开始加载模块: TestModuleMain
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 TestModuleMain 注入Logger
[2025-08-13 17:59:58.035 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 17:59:58.036 INF] [IndustrialHMI] [LIN-PC] [276] 开始初始化模块: 测试模块
[2025-08-13 17:59:58.036 WRN] [IndustrialHMI] [LIN-PC] [276] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 17:59:58.036 DBG] [IndustrialHMI] [LIN-PC] [276] 依赖注入验证通过
[2025-08-13 17:59:58.036 DBG] [IndustrialHMI] [LIN-PC] [276] 创建TestModuleModel成功
[2025-08-13 17:59:58.061 DBG] [IndustrialHMI] [LIN-PC] [276] 创建TestModuleView成功
[2025-08-13 17:59:58.062 DBG] [IndustrialHMI] [LIN-PC] [276] TestModulePresenter创建完成
[2025-08-13 17:59:58.062 DBG] [IndustrialHMI] [LIN-PC] [276] 创建TestModulePresenter成功
[2025-08-13 17:59:58.062 DBG] [IndustrialHMI] [LIN-PC] [276] 事件订阅完成
[2025-08-13 17:59:58.062 INF] [IndustrialHMI] [LIN-PC] [276] 模块初始化完成: 测试模块
[2025-08-13 17:59:58.062 INF] [IndustrialHMI] [LIN-PC] [276] 开始启动模块: 测试模块
[2025-08-13 17:59:58.063 INF] [IndustrialHMI] [LIN-PC] [276] 启动TestModulePresenter
[2025-08-13 17:59:58.078 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 模型启动完成
[2025-08-13 17:59:58.080 DBG] [IndustrialHMI] [LIN-PC] [276] 系统事件订阅完成
[2025-08-13 17:59:58.080 INF] [IndustrialHMI] [LIN-PC] [276] TestModulePresenter启动完成
[2025-08-13 17:59:58.080 INF] [IndustrialHMI] [LIN-PC] [276] 模块启动完成: 测试模块
[2025-08-13 17:59:58.084 DBG] [IndustrialHMI] [LIN-PC] [276] 处理系统事件: ModuleStarted
[2025-08-13 17:59:58.084 INF] [IndustrialHMI] [LIN-PC] [276] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 17:59:58.084 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 17:59:58.085 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 17:59:58.086 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 17:59:58.089 DBG] [IndustrialHMI] [LIN-PC] [276] 处理模块加载事件: 测试模块
[2025-08-13 17:59:58.089 INF] [IndustrialHMI] [LIN-PC] [276] 模块加载完成，共加载 5 个模块
[2025-08-13 17:59:58.089 INF] [IndustrialHMI] [LIN-PC] [276] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 17:59:58.090 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 报警管理 添加了UI标签页
[2025-08-13 17:59:58.090 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 通信测试 添加了UI标签页
[2025-08-13 17:59:58.090 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 设备监控 添加了UI标签页
[2025-08-13 17:59:58.091 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 17:59:58.092 DBG] [IndustrialHMI] [LIN-PC] [276] 为模块 测试模块 添加了UI标签页
[2025-08-13 17:59:58.092 INF] [IndustrialHMI] [LIN-PC] [276] 步骤5: 初始化主窗体
[2025-08-13 17:59:58.092 DBG] [IndustrialHMI] [LIN-PC] [276] 主窗体初始化完成
[2025-08-13 17:59:58.092 INF] [IndustrialHMI] [LIN-PC] [276] 应用程序初始化完成
[2025-08-13 17:59:58.092 INF] [IndustrialHMI] [LIN-PC] [276] 应用程序初始化成功，启动主窗体
[2025-08-13 17:59:58.092 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到系统启动事件
[2025-08-13 17:59:58.093 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 17:59:58.093 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 17:59:58.094 INF] [IndustrialHMI] [LIN-PC] [276] 模块 测试模块 收到系统启动事件
[2025-08-13 17:59:58.095 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到系统启动事件
[2025-08-13 17:59:58.202 DBG] [IndustrialHMI] [LIN-PC] [276] 主窗体事件订阅完成
[2025-08-13 17:59:58.207 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化事件处理完成
[2025-08-13 17:59:58.209 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 17:59:58.212 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 定时状态更新 - 17:59:58
[2025-08-13 17:59:59.111 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 系统启动后初始化完成
[2025-08-13 18:00:03.080 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化事件处理完成
[2025-08-13 18:00:03.081 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 数据更新: +1, 当前值: 2
[2025-08-13 18:00:03.081 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 定时状态更新 - 18:00:03
[2025-08-13 18:00:08.106 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化事件处理完成
[2025-08-13 18:00:08.107 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 数据更新: +1, 当前值: 3
[2025-08-13 18:00:08.107 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 定时状态更新 - 18:00:08
﻿[2025-08-13 18:00:10.755 INF] [IndustrialHMI] [LIN-PC] [31748] === 应用程序启动 ===
[2025-08-13 18:00:10.788 INF] [IndustrialHMI] [LIN-PC] [31748] 应用程序版本: 1.0.0.0
[2025-08-13 18:00:10.788 INF] [IndustrialHMI] [LIN-PC] [31748] 启动参数: 
[2025-08-13 18:00:10.788 INF] [IndustrialHMI] [LIN-PC] [31748] 开始初始化应用程序
[2025-08-13 18:00:10.788 INF] [IndustrialHMI] [LIN-PC] [31748] 步骤1: 创建服务容器
[2025-08-13 18:00:10.792 INF] [IndustrialHMI] [LIN-PC] [31748] 开始创建DryIoc容器
[2025-08-13 18:00:10.805 DBG] [IndustrialHMI] [LIN-PC] [31748] DryIoc容器创建成功，开始注册服务
[2025-08-13 18:00:10.808 DBG] [IndustrialHMI] [LIN-PC] [31748] 注册自定义日志记录器为单例
[2025-08-13 18:00:10.808 DBG] [IndustrialHMI] [LIN-PC] [31748] 注册EventAggregator为单例
[2025-08-13 18:00:10.817 DBG] [IndustrialHMI] [LIN-PC] [31748] 注册ConfigurationService为单例，已添加多个配置源并启用热更新
[2025-08-13 18:00:10.828 DBG] [IndustrialHMI] [LIN-PC] [31748] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 18:00:10.854 DBG] [IndustrialHMI] [LIN-PC] [31748] 注册MainForm为单例
[2025-08-13 18:00:10.854 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始验证DryIoc容器配置
[2025-08-13 18:00:10.854 DBG] [IndustrialHMI] [LIN-PC] [31748] DryIoc容器配置验证通过
[2025-08-13 18:00:10.854 INF] [IndustrialHMI] [LIN-PC] [31748] DryIoc容器创建和配置完成
[2025-08-13 18:00:10.854 INF] [IndustrialHMI] [LIN-PC] [31748] 步骤2: 创建主窗体
[2025-08-13 18:00:10.854 INF] [IndustrialHMI] [LIN-PC] [31748] 步骤3: 创建模块加载器
[2025-08-13 18:00:10.854 INF] [IndustrialHMI] [LIN-PC] [31748] 步骤4: 加载模块
[2025-08-13 18:00:10.855 INF] [IndustrialHMI] [LIN-PC] [31748] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 18:00:10.856 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 18:00:10.862 DBG] [IndustrialHMI] [LIN-PC] [31748] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 发现模块类型: AlarmModuleMain
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.863 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.864 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载模块: AlarmModuleMain
[2025-08-13 18:00:10.864 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 18:00:10.864 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 18:00:10.865 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 18:00:10.865 INF] [IndustrialHMI] [LIN-PC] [31748] 开始初始化报警管理模块
[2025-08-13 18:00:10.867 DBG] [IndustrialHMI] [LIN-PC] [31748] 报警服务创建完成
[2025-08-13 18:00:10.876 DBG] [IndustrialHMI] [LIN-PC] [31748] 报警视图创建完成
[2025-08-13 18:00:10.878 DBG] [IndustrialHMI] [LIN-PC] [31748] AlarmModel 初始化完成
[2025-08-13 18:00:10.878 DBG] [IndustrialHMI] [LIN-PC] [31748] 报警模型创建完成
[2025-08-13 18:00:10.878 DBG] [IndustrialHMI] [LIN-PC] [31748] AlarmPresenter 初始化完成
[2025-08-13 18:00:10.879 DBG] [IndustrialHMI] [LIN-PC] [31748] 报警表示器创建完成
[2025-08-13 18:00:10.879 INF] [IndustrialHMI] [LIN-PC] [31748] MVP组件创建完成
[2025-08-13 18:00:10.879 DBG] [IndustrialHMI] [LIN-PC] [31748] 系统事件订阅完成
[2025-08-13 18:00:10.879 INF] [IndustrialHMI] [LIN-PC] [31748] 报警管理模块初始化完成
[2025-08-13 18:00:10.879 INF] [IndustrialHMI] [LIN-PC] [31748] 启动报警管理模块
[2025-08-13 18:00:10.879 INF] [IndustrialHMI] [LIN-PC] [31748] 启动报警监控
[2025-08-13 18:00:10.879 INF] [IndustrialHMI] [LIN-PC] [31748] 开始报警监控
[2025-08-13 18:00:10.880 INF] [IndustrialHMI] [LIN-PC] [31748] 报警管理模块启动完成
[2025-08-13 18:00:10.880 INF] [IndustrialHMI] [LIN-PC] [31748] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 18:00:10.881 DBG] [IndustrialHMI] [LIN-PC] [31748] 模块已加载: 报警管理
[2025-08-13 18:00:10.881 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 18:00:10.883 DBG] [IndustrialHMI] [LIN-PC] [31748] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 18:00:10.884 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 18:00:10.884 INF] [IndustrialHMI] [LIN-PC] [31748] 开始初始化 CommunicationTestModule
[2025-08-13 18:00:10.885 INF] [IndustrialHMI] [LIN-PC] [31748] 初始化了 8 个测试用例
[2025-08-13 18:00:10.886 INF] [IndustrialHMI] [LIN-PC] [31748] 性能监控器初始化完成
[2025-08-13 18:00:10.886 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModel 初始化完成
[2025-08-13 18:00:10.891 DBG] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestView 初始化完成
[2025-08-13 18:00:10.894 DBG] [IndustrialHMI] [LIN-PC] [31748] 视图数据初始化完成
[2025-08-13 18:00:10.894 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestPresenter 初始化完成
[2025-08-13 18:00:10.894 DBG] [IndustrialHMI] [LIN-PC] [31748] MVP组件创建完成
[2025-08-13 18:00:10.894 DBG] [IndustrialHMI] [LIN-PC] [31748] 系统事件订阅完成
[2025-08-13 18:00:10.894 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModule 初始化完成
[2025-08-13 18:00:10.894 INF] [IndustrialHMI] [LIN-PC] [31748] 启动 CommunicationTestModule
[2025-08-13 18:00:10.894 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModule 启动完成
[2025-08-13 18:00:10.894 INF] [IndustrialHMI] [LIN-PC] [31748] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 18:00:10.895 DBG] [IndustrialHMI] [LIN-PC] [31748] 模块已加载: 通信测试
[2025-08-13 18:00:10.895 DBG] [IndustrialHMI] [LIN-PC] [31748] 收到模块加载事件: 通信测试
[2025-08-13 18:00:10.895 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 发现模块类型: 
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:10.896 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 18:00:10.897 DBG] [IndustrialHMI] [LIN-PC] [31748] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 发现模块类型: DeviceModuleMain
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载模块: DeviceModuleMain
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 18:00:10.898 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 18:00:10.898 INF] [IndustrialHMI] [LIN-PC] [31748] 开始初始化设备监控模块
[2025-08-13 18:00:10.899 DBG] [IndustrialHMI] [LIN-PC] [31748] 设备服务创建完成
[2025-08-13 18:00:10.902 DBG] [IndustrialHMI] [LIN-PC] [31748] 设备视图创建完成
[2025-08-13 18:00:10.902 DBG] [IndustrialHMI] [LIN-PC] [31748] DeviceModel 初始化完成
[2025-08-13 18:00:10.902 DBG] [IndustrialHMI] [LIN-PC] [31748] 设备模型创建完成
[2025-08-13 18:00:10.903 DBG] [IndustrialHMI] [LIN-PC] [31748] DevicePresenter 初始化完成
[2025-08-13 18:00:10.903 DBG] [IndustrialHMI] [LIN-PC] [31748] 设备表示器创建完成
[2025-08-13 18:00:10.903 INF] [IndustrialHMI] [LIN-PC] [31748] MVP组件创建完成
[2025-08-13 18:00:10.903 DBG] [IndustrialHMI] [LIN-PC] [31748] 系统事件订阅完成
[2025-08-13 18:00:10.903 INF] [IndustrialHMI] [LIN-PC] [31748] 设备监控模块初始化完成
[2025-08-13 18:00:10.903 INF] [IndustrialHMI] [LIN-PC] [31748] 启动设备监控模块
[2025-08-13 18:00:10.903 INF] [IndustrialHMI] [LIN-PC] [31748] 用户请求开始设备监控
[2025-08-13 18:00:10.903 INF] [IndustrialHMI] [LIN-PC] [31748] 开始设备监控
[2025-08-13 18:00:10.904 INF] [IndustrialHMI] [LIN-PC] [31748] 设备监控已启动
[2025-08-13 18:00:10.904 INF] [IndustrialHMI] [LIN-PC] [31748] 设备监控模块启动完成
[2025-08-13 18:00:10.904 INF] [IndustrialHMI] [LIN-PC] [31748] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 18:00:10.904 DBG] [IndustrialHMI] [LIN-PC] [31748] 模块已加载: 设备监控
[2025-08-13 18:00:10.904 DBG] [IndustrialHMI] [LIN-PC] [31748] 收到模块加载事件: 设备监控
[2025-08-13 18:00:10.904 DBG] [IndustrialHMI] [LIN-PC] [31748] 模块已加载: 设备监控
[2025-08-13 18:00:10.904 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 18:00:10.905 DBG] [IndustrialHMI] [LIN-PC] [31748] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 18:00:10.906 INF] [IndustrialHMI] [LIN-PC] [31748] 开始初始化测试框架模块
[2025-08-13 18:00:10.906 DBG] [IndustrialHMI] [LIN-PC] [31748] ConfigurationService未注入（可选）
[2025-08-13 18:00:11.402 DBG] [IndustrialHMI] [LIN-PC] [31748] 初始化TestFrameworkPresenter
[2025-08-13 18:00:11.414 DBG] [IndustrialHMI] [LIN-PC] [31748] TestFrameworkPresenter初始化完成
[2025-08-13 18:00:11.414 DBG] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块事件订阅完成
[2025-08-13 18:00:11.414 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块初始化完成
[2025-08-13 18:00:11.414 INF] [IndustrialHMI] [LIN-PC] [31748] 启动测试框架模块
[2025-08-13 18:00:11.415 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 模型已启动
[2025-08-13 18:00:11.415 DBG] [IndustrialHMI] [LIN-PC] [31748] 加载TestFramework数据
[2025-08-13 18:00:11.416 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:11.416 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 数据加载完成
[2025-08-13 18:00:11.416 DBG] [IndustrialHMI] [LIN-PC] [31748] TestFramework数据加载完成
[2025-08-13 18:00:11.416 INF] [IndustrialHMI] [LIN-PC] [31748] 初始化集成测试套件
[2025-08-13 18:00:11.416 INF] [IndustrialHMI] [LIN-PC] [31748] 集成测试套件初始化完成
[2025-08-13 18:00:11.416 INF] [IndustrialHMI] [LIN-PC] [31748] 初始化性能测试套件
[2025-08-13 18:00:11.537 INF] [IndustrialHMI] [LIN-PC] [31748] 性能测试套件初始化完成
[2025-08-13 18:00:11.537 INF] [IndustrialHMI] [LIN-PC] [31748] 初始化内存泄漏测试套件
[2025-08-13 18:00:11.537 INF] [IndustrialHMI] [LIN-PC] [31748] 内存泄漏测试套件初始化完成
[2025-08-13 18:00:11.537 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块启动完成
[2025-08-13 18:00:11.537 INF] [IndustrialHMI] [LIN-PC] [31748] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 18:00:11.538 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 18:00:11.538 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:11.539 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:00:11.539 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 18:00:11.541 DBG] [IndustrialHMI] [LIN-PC] [31748] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:11.542 DBG] [IndustrialHMI] [LIN-PC] [31748] 发现模块类型: TestModuleMain
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 开始加载模块: TestModuleMain
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 TestModuleMain 注入Logger
[2025-08-13 18:00:11.543 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 18:00:11.544 INF] [IndustrialHMI] [LIN-PC] [31748] 开始初始化模块: 测试模块
[2025-08-13 18:00:11.544 WRN] [IndustrialHMI] [LIN-PC] [31748] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 18:00:11.544 DBG] [IndustrialHMI] [LIN-PC] [31748] 依赖注入验证通过
[2025-08-13 18:00:11.544 DBG] [IndustrialHMI] [LIN-PC] [31748] 创建TestModuleModel成功
[2025-08-13 18:00:11.547 DBG] [IndustrialHMI] [LIN-PC] [31748] 创建TestModuleView成功
[2025-08-13 18:00:11.547 DBG] [IndustrialHMI] [LIN-PC] [31748] TestModulePresenter创建完成
[2025-08-13 18:00:11.547 DBG] [IndustrialHMI] [LIN-PC] [31748] 创建TestModulePresenter成功
[2025-08-13 18:00:11.547 DBG] [IndustrialHMI] [LIN-PC] [31748] 事件订阅完成
[2025-08-13 18:00:11.547 INF] [IndustrialHMI] [LIN-PC] [31748] 模块初始化完成: 测试模块
[2025-08-13 18:00:11.548 INF] [IndustrialHMI] [LIN-PC] [31748] 开始启动模块: 测试模块
[2025-08-13 18:00:11.548 INF] [IndustrialHMI] [LIN-PC] [31748] 启动TestModulePresenter
[2025-08-13 18:00:11.555 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 模型启动完成
[2025-08-13 18:00:11.556 DBG] [IndustrialHMI] [LIN-PC] [31748] 系统事件订阅完成
[2025-08-13 18:00:11.556 INF] [IndustrialHMI] [LIN-PC] [31748] TestModulePresenter启动完成
[2025-08-13 18:00:11.556 INF] [IndustrialHMI] [LIN-PC] [31748] 模块启动完成: 测试模块
[2025-08-13 18:00:11.557 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理系统事件: ModuleStarted
[2025-08-13 18:00:11.558 INF] [IndustrialHMI] [LIN-PC] [31748] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 18:00:11.558 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 18:00:11.559 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:11.560 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:00:11.561 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理模块加载事件: 测试模块
[2025-08-13 18:00:11.561 INF] [IndustrialHMI] [LIN-PC] [31748] 模块加载完成，共加载 5 个模块
[2025-08-13 18:00:11.561 INF] [IndustrialHMI] [LIN-PC] [31748] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 18:00:11.563 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 报警管理 添加了UI标签页
[2025-08-13 18:00:11.563 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 通信测试 添加了UI标签页
[2025-08-13 18:00:11.563 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 设备监控 添加了UI标签页
[2025-08-13 18:00:11.564 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 18:00:11.564 DBG] [IndustrialHMI] [LIN-PC] [31748] 为模块 测试模块 添加了UI标签页
[2025-08-13 18:00:11.564 INF] [IndustrialHMI] [LIN-PC] [31748] 步骤5: 初始化主窗体
[2025-08-13 18:00:11.565 DBG] [IndustrialHMI] [LIN-PC] [31748] 主窗体初始化完成
[2025-08-13 18:00:11.565 INF] [IndustrialHMI] [LIN-PC] [31748] 应用程序初始化完成
[2025-08-13 18:00:11.565 INF] [IndustrialHMI] [LIN-PC] [31748] 应用程序初始化成功，启动主窗体
[2025-08-13 18:00:11.565 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到系统启动事件
[2025-08-13 18:00:11.567 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:11.567 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 18:00:11.567 INF] [IndustrialHMI] [LIN-PC] [31748] 模块 测试模块 收到系统启动事件
[2025-08-13 18:00:11.568 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到系统启动事件
[2025-08-13 18:00:11.665 DBG] [IndustrialHMI] [LIN-PC] [31748] 主窗体事件订阅完成
[2025-08-13 18:00:11.670 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据变化事件处理完成
[2025-08-13 18:00:11.671 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 18:00:11.672 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 定时状态更新 - 18:00:11
[2025-08-13 18:00:12.583 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 系统启动后初始化完成
[2025-08-13 18:00:13.113 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化事件处理完成
[2025-08-13 18:00:13.114 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 数据更新: +1, 当前值: 4
[2025-08-13 18:00:13.114 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 定时状态更新 - 18:00:13
[2025-08-13 18:00:13.705 INF] [IndustrialHMI] [LIN-PC] [31748] 用户请求关闭应用程序
[2025-08-13 18:00:13.705 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:13.706 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:13.706 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 18:00:13.706 INF] [IndustrialHMI] [LIN-PC] [31748] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:00:13.707 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到系统关闭事件
[2025-08-13 18:00:13.709 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据变化事件处理完成
[2025-08-13 18:00:13.709 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 系统关闭前清理完成
[2025-08-13 18:00:13.710 INF] [IndustrialHMI] [LIN-PC] [31748] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:13.710 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载所有模块
[2025-08-13 18:00:13.710 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载模块: 报警管理
[2025-08-13 18:00:13.710 INF] [IndustrialHMI] [LIN-PC] [31748] 停止报警管理模块
[2025-08-13 18:00:13.710 INF] [IndustrialHMI] [LIN-PC] [31748] 停止报警监控
[2025-08-13 18:00:13.710 INF] [IndustrialHMI] [LIN-PC] [31748] 停止报警监控
[2025-08-13 18:00:13.711 INF] [IndustrialHMI] [LIN-PC] [31748] 报警管理模块停止完成
[2025-08-13 18:00:13.711 INF] [IndustrialHMI] [LIN-PC] [31748] 开始释放报警管理模块资源
[2025-08-13 18:00:13.712 INF] [IndustrialHMI] [LIN-PC] [31748] 停止报警监控
[2025-08-13 18:00:13.712 INF] [IndustrialHMI] [LIN-PC] [31748] 停止报警监控
[2025-08-13 18:00:13.712 DBG] [IndustrialHMI] [LIN-PC] [31748] AlarmPresenter 资源释放完成
[2025-08-13 18:00:13.712 INF] [IndustrialHMI] [LIN-PC] [31748] 停止报警监控
[2025-08-13 18:00:13.712 DBG] [IndustrialHMI] [LIN-PC] [31748] AlarmModel 资源释放完成
[2025-08-13 18:00:13.717 INF] [IndustrialHMI] [LIN-PC] [31748] 报警管理模块资源释放完成
[2025-08-13 18:00:13.717 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 18:00:13.718 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:13.718 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:13.719 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理模块卸载事件: 报警管理
[2025-08-13 18:00:13.719 INF] [IndustrialHMI] [LIN-PC] [31748] 模块卸载成功: 报警管理
[2025-08-13 18:00:13.719 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载模块: 通信测试
[2025-08-13 18:00:13.719 INF] [IndustrialHMI] [LIN-PC] [31748] 停止 CommunicationTestModule
[2025-08-13 18:00:13.720 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据变化: EventMonitoring
[2025-08-13 18:00:13.720 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:00:13.720 INF] [IndustrialHMI] [LIN-PC] [31748] 测试已停止
[2025-08-13 18:00:13.720 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModule 停止完成
[2025-08-13 18:00:13.720 INF] [IndustrialHMI] [LIN-PC] [31748] 开始释放 CommunicationTestModule 资源
[2025-08-13 18:00:13.720 INF] [IndustrialHMI] [LIN-PC] [31748] 停止 CommunicationTestModule
[2025-08-13 18:00:13.720 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据变化: EventMonitoring
[2025-08-13 18:00:13.720 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:00:13.720 INF] [IndustrialHMI] [LIN-PC] [31748] 测试已停止
[2025-08-13 18:00:13.720 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModule 停止完成
[2025-08-13 18:00:13.721 DBG] [IndustrialHMI] [LIN-PC] [31748] 系统事件订阅已取消
[2025-08-13 18:00:13.721 DBG] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestPresenter 资源释放完成
[2025-08-13 18:00:13.722 DBG] [IndustrialHMI] [LIN-PC] [31748] EventMonitor 资源释放完成
[2025-08-13 18:00:13.722 INF] [IndustrialHMI] [LIN-PC] [31748] 测试已停止
[2025-08-13 18:00:13.722 DBG] [IndustrialHMI] [LIN-PC] [31748] TestCaseManager 资源释放完成
[2025-08-13 18:00:13.722 DBG] [IndustrialHMI] [LIN-PC] [31748] PerformanceMonitor 资源释放完成
[2025-08-13 18:00:13.722 DBG] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModel 资源释放完成
[2025-08-13 18:00:13.722 INF] [IndustrialHMI] [LIN-PC] [31748] CommunicationTestModule 资源释放完成
[2025-08-13 18:00:13.722 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 18:00:13.723 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:13.723 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:13.723 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理模块卸载事件: 通信测试
[2025-08-13 18:00:13.723 INF] [IndustrialHMI] [LIN-PC] [31748] 模块卸载成功: 通信测试
[2025-08-13 18:00:13.723 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载模块: 设备监控
[2025-08-13 18:00:13.723 INF] [IndustrialHMI] [LIN-PC] [31748] 停止设备监控模块
[2025-08-13 18:00:13.724 INF] [IndustrialHMI] [LIN-PC] [31748] 用户请求停止设备监控
[2025-08-13 18:00:13.724 INF] [IndustrialHMI] [LIN-PC] [31748] 停止设备监控
[2025-08-13 18:00:13.724 INF] [IndustrialHMI] [LIN-PC] [31748] 设备监控已停止
[2025-08-13 18:00:13.724 INF] [IndustrialHMI] [LIN-PC] [31748] 设备监控模块停止完成
[2025-08-13 18:00:13.724 INF] [IndustrialHMI] [LIN-PC] [31748] 开始释放设备监控模块资源
[2025-08-13 18:00:13.725 DBG] [IndustrialHMI] [LIN-PC] [31748] DevicePresenter 资源释放完成
[2025-08-13 18:00:13.725 INF] [IndustrialHMI] [LIN-PC] [31748] 停止设备监控
[2025-08-13 18:00:13.725 DBG] [IndustrialHMI] [LIN-PC] [31748] DeviceModel 资源释放完成
[2025-08-13 18:00:13.725 INF] [IndustrialHMI] [LIN-PC] [31748] 设备监控模块资源释放完成
[2025-08-13 18:00:13.725 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 18:00:13.725 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型数据已更新，视图已刷新
[2025-08-13 18:00:13.726 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:13.726 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理模块卸载事件: 设备监控
[2025-08-13 18:00:13.726 INF] [IndustrialHMI] [LIN-PC] [31748] 模块卸载成功: 设备监控
[2025-08-13 18:00:13.726 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载模块: 测试框架模块
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 停止测试框架模块
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 停止内存泄漏测试套件
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 内存泄漏测试套件已停止
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 停止性能测试套件
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 性能测试套件已停止
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 停止集成测试套件
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 集成测试套件已停止
[2025-08-13 18:00:13.727 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 模型已停止
[2025-08-13 18:00:13.727 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块停止完成
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 开始释放测试框架模块资源
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 停止内存泄漏测试套件
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 内存泄漏测试套件已停止
[2025-08-13 18:00:13.728 DBG] [IndustrialHMI] [LIN-PC] [31748] MemoryLeakTestSuite资源释放完成
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 停止性能测试套件
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 性能测试套件已停止
[2025-08-13 18:00:13.728 DBG] [IndustrialHMI] [LIN-PC] [31748] PerformanceTestSuite资源释放完成
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 停止集成测试套件
[2025-08-13 18:00:13.728 INF] [IndustrialHMI] [LIN-PC] [31748] 集成测试套件已停止
[2025-08-13 18:00:13.728 DBG] [IndustrialHMI] [LIN-PC] [31748] IntegrationTestSuite资源释放完成
[2025-08-13 18:00:13.728 DBG] [IndustrialHMI] [LIN-PC] [31748] TestFrameworkPresenter资源释放完成
[2025-08-13 18:00:13.729 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块资源释放完成
[2025-08-13 18:00:13.729 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 18:00:13.729 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理模块卸载事件: 测试框架模块
[2025-08-13 18:00:13.729 INF] [IndustrialHMI] [LIN-PC] [31748] 模块卸载成功: 测试框架模块
[2025-08-13 18:00:13.729 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载模块: 测试模块
[2025-08-13 18:00:13.730 INF] [IndustrialHMI] [LIN-PC] [31748] 开始停止模块: 测试模块
[2025-08-13 18:00:13.730 INF] [IndustrialHMI] [LIN-PC] [31748] 停止TestModulePresenter
[2025-08-13 18:00:13.730 DBG] [IndustrialHMI] [LIN-PC] [31748] 系统事件取消订阅完成
[2025-08-13 18:00:13.731 DBG] [IndustrialHMI] [LIN-PC] [31748] 模型状态变化: 模型停止完成
[2025-08-13 18:00:13.731 INF] [IndustrialHMI] [LIN-PC] [31748] TestModulePresenter停止完成
[2025-08-13 18:00:13.731 DBG] [IndustrialHMI] [LIN-PC] [31748] 事件取消订阅完成
[2025-08-13 18:00:13.731 INF] [IndustrialHMI] [LIN-PC] [31748] 模块停止完成: 测试模块
[2025-08-13 18:00:13.731 DBG] [IndustrialHMI] [LIN-PC] [31748] 处理系统事件: ModuleStopped
[2025-08-13 18:00:13.731 INF] [IndustrialHMI] [LIN-PC] [31748] 开始释放模块资源: 测试模块
[2025-08-13 18:00:13.732 INF] [IndustrialHMI] [LIN-PC] [31748] 释放TestModulePresenter资源
[2025-08-13 18:00:13.732 INF] [IndustrialHMI] [LIN-PC] [31748] TestModulePresenter资源释放完成
[2025-08-13 18:00:13.737 INF] [IndustrialHMI] [LIN-PC] [31748] 模块资源释放完成: 测试模块
[2025-08-13 18:00:13.737 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 18:00:13.739 ERR] [IndustrialHMI] [LIN-PC] [31748] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 18:00:13.745 INF] [IndustrialHMI] [LIN-PC] [31748] 模块卸载成功: 测试模块
[2025-08-13 18:00:13.745 INF] [IndustrialHMI] [LIN-PC] [31748] 所有模块卸载完成
[2025-08-13 18:00:13.745 INF] [IndustrialHMI] [LIN-PC] [31748] 应用程序关闭流程完成
[2025-08-13 18:00:13.745 INF] [IndustrialHMI] [LIN-PC] [31748] 主窗体已关闭，资源清理完成
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 开始释放应用程序资源
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 开始卸载所有模块
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 所有模块卸载完成
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] 应用程序资源释放完成
[2025-08-13 18:00:13.751 INF] [IndustrialHMI] [LIN-PC] [31748] === 应用程序正常退出 ===
﻿[2025-08-13 18:00:13.867 INF] [IndustrialHMI] [LIN-PC] [26248] === 应用程序启动 ===
[2025-08-13 18:00:13.898 INF] [IndustrialHMI] [LIN-PC] [26248] 应用程序版本: 1.0.0.0
[2025-08-13 18:00:13.898 INF] [IndustrialHMI] [LIN-PC] [26248] 启动参数: 
[2025-08-13 18:00:13.899 INF] [IndustrialHMI] [LIN-PC] [26248] 开始初始化应用程序
[2025-08-13 18:00:13.899 INF] [IndustrialHMI] [LIN-PC] [26248] 步骤1: 创建服务容器
[2025-08-13 18:00:13.903 INF] [IndustrialHMI] [LIN-PC] [26248] 开始创建DryIoc容器
[2025-08-13 18:00:13.917 DBG] [IndustrialHMI] [LIN-PC] [26248] DryIoc容器创建成功，开始注册服务
[2025-08-13 18:00:13.920 DBG] [IndustrialHMI] [LIN-PC] [26248] 注册自定义日志记录器为单例
[2025-08-13 18:00:13.920 DBG] [IndustrialHMI] [LIN-PC] [26248] 注册EventAggregator为单例
[2025-08-13 18:00:13.932 DBG] [IndustrialHMI] [LIN-PC] [26248] 注册ConfigurationService为单例，已添加多个配置源并启用热更新
[2025-08-13 18:00:13.944 DBG] [IndustrialHMI] [LIN-PC] [26248] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 18:00:13.971 DBG] [IndustrialHMI] [LIN-PC] [26248] 注册MainForm为单例
[2025-08-13 18:00:13.972 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始验证DryIoc容器配置
[2025-08-13 18:00:13.972 DBG] [IndustrialHMI] [LIN-PC] [26248] DryIoc容器配置验证通过
[2025-08-13 18:00:13.972 INF] [IndustrialHMI] [LIN-PC] [26248] DryIoc容器创建和配置完成
[2025-08-13 18:00:13.972 INF] [IndustrialHMI] [LIN-PC] [26248] 步骤2: 创建主窗体
[2025-08-13 18:00:13.972 INF] [IndustrialHMI] [LIN-PC] [26248] 步骤3: 创建模块加载器
[2025-08-13 18:00:13.972 INF] [IndustrialHMI] [LIN-PC] [26248] 步骤4: 加载模块
[2025-08-13 18:00:13.972 INF] [IndustrialHMI] [LIN-PC] [26248] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 18:00:13.973 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 18:00:13.981 DBG] [IndustrialHMI] [LIN-PC] [26248] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 发现模块类型: AlarmModuleMain
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:13.983 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载模块: AlarmModuleMain
[2025-08-13 18:00:13.984 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 18:00:13.984 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 18:00:13.985 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 18:00:13.985 INF] [IndustrialHMI] [LIN-PC] [26248] 开始初始化报警管理模块
[2025-08-13 18:00:13.987 DBG] [IndustrialHMI] [LIN-PC] [26248] 报警服务创建完成
[2025-08-13 18:00:13.996 DBG] [IndustrialHMI] [LIN-PC] [26248] 报警视图创建完成
[2025-08-13 18:00:13.997 DBG] [IndustrialHMI] [LIN-PC] [26248] AlarmModel 初始化完成
[2025-08-13 18:00:13.997 DBG] [IndustrialHMI] [LIN-PC] [26248] 报警模型创建完成
[2025-08-13 18:00:13.998 DBG] [IndustrialHMI] [LIN-PC] [26248] AlarmPresenter 初始化完成
[2025-08-13 18:00:13.998 DBG] [IndustrialHMI] [LIN-PC] [26248] 报警表示器创建完成
[2025-08-13 18:00:13.998 INF] [IndustrialHMI] [LIN-PC] [26248] MVP组件创建完成
[2025-08-13 18:00:13.998 DBG] [IndustrialHMI] [LIN-PC] [26248] 系统事件订阅完成
[2025-08-13 18:00:13.998 INF] [IndustrialHMI] [LIN-PC] [26248] 报警管理模块初始化完成
[2025-08-13 18:00:13.999 INF] [IndustrialHMI] [LIN-PC] [26248] 启动报警管理模块
[2025-08-13 18:00:13.999 INF] [IndustrialHMI] [LIN-PC] [26248] 启动报警监控
[2025-08-13 18:00:13.999 INF] [IndustrialHMI] [LIN-PC] [26248] 开始报警监控
[2025-08-13 18:00:13.999 INF] [IndustrialHMI] [LIN-PC] [26248] 报警管理模块启动完成
[2025-08-13 18:00:14.000 INF] [IndustrialHMI] [LIN-PC] [26248] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 18:00:14.001 DBG] [IndustrialHMI] [LIN-PC] [26248] 模块已加载: 报警管理
[2025-08-13 18:00:14.001 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 18:00:14.003 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 18:00:14.004 INF] [IndustrialHMI] [LIN-PC] [26248] 开始初始化 CommunicationTestModule
[2025-08-13 18:00:14.005 INF] [IndustrialHMI] [LIN-PC] [26248] 初始化了 8 个测试用例
[2025-08-13 18:00:14.005 INF] [IndustrialHMI] [LIN-PC] [26248] 性能监控器初始化完成
[2025-08-13 18:00:14.005 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModel 初始化完成
[2025-08-13 18:00:14.013 DBG] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestView 初始化完成
[2025-08-13 18:00:14.016 DBG] [IndustrialHMI] [LIN-PC] [26248] 视图数据初始化完成
[2025-08-13 18:00:14.016 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestPresenter 初始化完成
[2025-08-13 18:00:14.016 DBG] [IndustrialHMI] [LIN-PC] [26248] MVP组件创建完成
[2025-08-13 18:00:14.016 DBG] [IndustrialHMI] [LIN-PC] [26248] 系统事件订阅完成
[2025-08-13 18:00:14.016 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModule 初始化完成
[2025-08-13 18:00:14.016 INF] [IndustrialHMI] [LIN-PC] [26248] 启动 CommunicationTestModule
[2025-08-13 18:00:14.016 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModule 启动完成
[2025-08-13 18:00:14.016 INF] [IndustrialHMI] [LIN-PC] [26248] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 18:00:14.016 DBG] [IndustrialHMI] [LIN-PC] [26248] 模块已加载: 通信测试
[2025-08-13 18:00:14.016 DBG] [IndustrialHMI] [LIN-PC] [26248] 收到模块加载事件: 通信测试
[2025-08-13 18:00:14.016 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 发现模块类型: 
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:00:14.018 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 18:00:14.019 DBG] [IndustrialHMI] [LIN-PC] [26248] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 发现模块类型: DeviceModuleMain
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载模块: DeviceModuleMain
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 18:00:14.020 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 18:00:14.020 INF] [IndustrialHMI] [LIN-PC] [26248] 开始初始化设备监控模块
[2025-08-13 18:00:14.022 DBG] [IndustrialHMI] [LIN-PC] [26248] 设备服务创建完成
[2025-08-13 18:00:14.024 DBG] [IndustrialHMI] [LIN-PC] [26248] 设备视图创建完成
[2025-08-13 18:00:14.025 DBG] [IndustrialHMI] [LIN-PC] [26248] DeviceModel 初始化完成
[2025-08-13 18:00:14.025 DBG] [IndustrialHMI] [LIN-PC] [26248] 设备模型创建完成
[2025-08-13 18:00:14.025 DBG] [IndustrialHMI] [LIN-PC] [26248] DevicePresenter 初始化完成
[2025-08-13 18:00:14.026 DBG] [IndustrialHMI] [LIN-PC] [26248] 设备表示器创建完成
[2025-08-13 18:00:14.026 INF] [IndustrialHMI] [LIN-PC] [26248] MVP组件创建完成
[2025-08-13 18:00:14.026 DBG] [IndustrialHMI] [LIN-PC] [26248] 系统事件订阅完成
[2025-08-13 18:00:14.026 INF] [IndustrialHMI] [LIN-PC] [26248] 设备监控模块初始化完成
[2025-08-13 18:00:14.026 INF] [IndustrialHMI] [LIN-PC] [26248] 启动设备监控模块
[2025-08-13 18:00:14.026 INF] [IndustrialHMI] [LIN-PC] [26248] 用户请求开始设备监控
[2025-08-13 18:00:14.026 INF] [IndustrialHMI] [LIN-PC] [26248] 开始设备监控
[2025-08-13 18:00:14.027 INF] [IndustrialHMI] [LIN-PC] [26248] 设备监控已启动
[2025-08-13 18:00:14.027 INF] [IndustrialHMI] [LIN-PC] [26248] 设备监控模块启动完成
[2025-08-13 18:00:14.027 INF] [IndustrialHMI] [LIN-PC] [26248] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 18:00:14.027 DBG] [IndustrialHMI] [LIN-PC] [26248] 模块已加载: 设备监控
[2025-08-13 18:00:14.027 DBG] [IndustrialHMI] [LIN-PC] [26248] 收到模块加载事件: 设备监控
[2025-08-13 18:00:14.027 DBG] [IndustrialHMI] [LIN-PC] [26248] 模块已加载: 设备监控
[2025-08-13 18:00:14.027 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 18:00:14.028 DBG] [IndustrialHMI] [LIN-PC] [26248] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 18:00:14.029 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 18:00:14.030 INF] [IndustrialHMI] [LIN-PC] [26248] 开始初始化测试框架模块
[2025-08-13 18:00:14.030 DBG] [IndustrialHMI] [LIN-PC] [26248] ConfigurationService未注入（可选）
[2025-08-13 18:00:14.573 DBG] [IndustrialHMI] [LIN-PC] [26248] 初始化TestFrameworkPresenter
[2025-08-13 18:00:14.584 DBG] [IndustrialHMI] [LIN-PC] [26248] TestFrameworkPresenter初始化完成
[2025-08-13 18:00:14.585 DBG] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块事件订阅完成
[2025-08-13 18:00:14.585 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块初始化完成
[2025-08-13 18:00:14.585 INF] [IndustrialHMI] [LIN-PC] [26248] 启动测试框架模块
[2025-08-13 18:00:14.586 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 模型已启动
[2025-08-13 18:00:14.586 DBG] [IndustrialHMI] [LIN-PC] [26248] 加载TestFramework数据
[2025-08-13 18:00:14.586 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:14.588 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 数据加载完成
[2025-08-13 18:00:14.588 DBG] [IndustrialHMI] [LIN-PC] [26248] TestFramework数据加载完成
[2025-08-13 18:00:14.588 INF] [IndustrialHMI] [LIN-PC] [26248] 初始化集成测试套件
[2025-08-13 18:00:14.588 INF] [IndustrialHMI] [LIN-PC] [26248] 集成测试套件初始化完成
[2025-08-13 18:00:14.588 INF] [IndustrialHMI] [LIN-PC] [26248] 初始化性能测试套件
[2025-08-13 18:00:14.703 INF] [IndustrialHMI] [LIN-PC] [26248] 性能测试套件初始化完成
[2025-08-13 18:00:14.704 INF] [IndustrialHMI] [LIN-PC] [26248] 初始化内存泄漏测试套件
[2025-08-13 18:00:14.704 INF] [IndustrialHMI] [LIN-PC] [26248] 内存泄漏测试套件初始化完成
[2025-08-13 18:00:14.704 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块启动完成
[2025-08-13 18:00:14.705 INF] [IndustrialHMI] [LIN-PC] [26248] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 18:00:14.705 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 18:00:14.706 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:14.707 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:00:14.707 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 18:00:14.709 DBG] [IndustrialHMI] [LIN-PC] [26248] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:00:14.709 DBG] [IndustrialHMI] [LIN-PC] [26248] 发现模块类型: TestModuleMain
[2025-08-13 18:00:14.709 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 开始加载模块: TestModuleMain
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 TestModuleMain 注入Logger
[2025-08-13 18:00:14.710 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 18:00:14.710 INF] [IndustrialHMI] [LIN-PC] [26248] 开始初始化模块: 测试模块
[2025-08-13 18:00:14.711 WRN] [IndustrialHMI] [LIN-PC] [26248] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 18:00:14.711 DBG] [IndustrialHMI] [LIN-PC] [26248] 依赖注入验证通过
[2025-08-13 18:00:14.711 DBG] [IndustrialHMI] [LIN-PC] [26248] 创建TestModuleModel成功
[2025-08-13 18:00:14.714 DBG] [IndustrialHMI] [LIN-PC] [26248] 创建TestModuleView成功
[2025-08-13 18:00:14.714 DBG] [IndustrialHMI] [LIN-PC] [26248] TestModulePresenter创建完成
[2025-08-13 18:00:14.714 DBG] [IndustrialHMI] [LIN-PC] [26248] 创建TestModulePresenter成功
[2025-08-13 18:00:14.714 DBG] [IndustrialHMI] [LIN-PC] [26248] 事件订阅完成
[2025-08-13 18:00:14.714 INF] [IndustrialHMI] [LIN-PC] [26248] 模块初始化完成: 测试模块
[2025-08-13 18:00:14.715 INF] [IndustrialHMI] [LIN-PC] [26248] 开始启动模块: 测试模块
[2025-08-13 18:00:14.715 INF] [IndustrialHMI] [LIN-PC] [26248] 启动TestModulePresenter
[2025-08-13 18:00:14.722 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 模型启动完成
[2025-08-13 18:00:14.723 DBG] [IndustrialHMI] [LIN-PC] [26248] 系统事件订阅完成
[2025-08-13 18:00:14.723 INF] [IndustrialHMI] [LIN-PC] [26248] TestModulePresenter启动完成
[2025-08-13 18:00:14.723 INF] [IndustrialHMI] [LIN-PC] [26248] 模块启动完成: 测试模块
[2025-08-13 18:00:14.724 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理系统事件: ModuleStarted
[2025-08-13 18:00:14.724 INF] [IndustrialHMI] [LIN-PC] [26248] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 18:00:14.724 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 18:00:14.725 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:14.725 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:00:14.726 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理模块加载事件: 测试模块
[2025-08-13 18:00:14.726 INF] [IndustrialHMI] [LIN-PC] [26248] 模块加载完成，共加载 5 个模块
[2025-08-13 18:00:14.726 INF] [IndustrialHMI] [LIN-PC] [26248] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 18:00:14.727 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 报警管理 添加了UI标签页
[2025-08-13 18:00:14.727 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 通信测试 添加了UI标签页
[2025-08-13 18:00:14.728 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 设备监控 添加了UI标签页
[2025-08-13 18:00:14.729 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 18:00:14.729 DBG] [IndustrialHMI] [LIN-PC] [26248] 为模块 测试模块 添加了UI标签页
[2025-08-13 18:00:14.729 INF] [IndustrialHMI] [LIN-PC] [26248] 步骤5: 初始化主窗体
[2025-08-13 18:00:14.729 DBG] [IndustrialHMI] [LIN-PC] [26248] 主窗体初始化完成
[2025-08-13 18:00:14.729 INF] [IndustrialHMI] [LIN-PC] [26248] 应用程序初始化完成
[2025-08-13 18:00:14.729 INF] [IndustrialHMI] [LIN-PC] [26248] 应用程序初始化成功，启动主窗体
[2025-08-13 18:00:14.730 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到系统启动事件
[2025-08-13 18:00:14.730 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:14.730 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 18:00:14.731 INF] [IndustrialHMI] [LIN-PC] [26248] 模块 测试模块 收到系统启动事件
[2025-08-13 18:00:14.731 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到系统启动事件
[2025-08-13 18:00:14.802 DBG] [IndustrialHMI] [LIN-PC] [26248] 主窗体事件订阅完成
[2025-08-13 18:00:14.816 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据变化事件处理完成
[2025-08-13 18:00:14.827 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 18:00:14.829 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 定时状态更新 - 18:00:14
[2025-08-13 18:00:15.747 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 系统启动后初始化完成
[2025-08-13 18:00:15.812 INF] [IndustrialHMI] [LIN-PC] [26248] 用户请求关闭应用程序
[2025-08-13 18:00:15.812 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.813 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.813 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 18:00:15.813 INF] [IndustrialHMI] [LIN-PC] [26248] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:00:15.814 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到系统关闭事件
[2025-08-13 18:00:15.814 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据变化事件处理完成
[2025-08-13 18:00:15.815 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 系统关闭前清理完成
[2025-08-13 18:00:15.815 INF] [IndustrialHMI] [LIN-PC] [26248] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.815 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载所有模块
[2025-08-13 18:00:15.815 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载模块: 报警管理
[2025-08-13 18:00:15.816 INF] [IndustrialHMI] [LIN-PC] [26248] 停止报警管理模块
[2025-08-13 18:00:15.816 INF] [IndustrialHMI] [LIN-PC] [26248] 停止报警监控
[2025-08-13 18:00:15.816 INF] [IndustrialHMI] [LIN-PC] [26248] 停止报警监控
[2025-08-13 18:00:15.816 INF] [IndustrialHMI] [LIN-PC] [26248] 报警管理模块停止完成
[2025-08-13 18:00:15.816 INF] [IndustrialHMI] [LIN-PC] [26248] 开始释放报警管理模块资源
[2025-08-13 18:00:15.817 INF] [IndustrialHMI] [LIN-PC] [26248] 停止报警监控
[2025-08-13 18:00:15.817 INF] [IndustrialHMI] [LIN-PC] [26248] 停止报警监控
[2025-08-13 18:00:15.817 DBG] [IndustrialHMI] [LIN-PC] [26248] AlarmPresenter 资源释放完成
[2025-08-13 18:00:15.818 INF] [IndustrialHMI] [LIN-PC] [26248] 停止报警监控
[2025-08-13 18:00:15.818 DBG] [IndustrialHMI] [LIN-PC] [26248] AlarmModel 资源释放完成
[2025-08-13 18:00:15.822 INF] [IndustrialHMI] [LIN-PC] [26248] 报警管理模块资源释放完成
[2025-08-13 18:00:15.823 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 18:00:15.823 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.823 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:15.824 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理模块卸载事件: 报警管理
[2025-08-13 18:00:15.824 INF] [IndustrialHMI] [LIN-PC] [26248] 模块卸载成功: 报警管理
[2025-08-13 18:00:15.824 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载模块: 通信测试
[2025-08-13 18:00:15.824 INF] [IndustrialHMI] [LIN-PC] [26248] 停止 CommunicationTestModule
[2025-08-13 18:00:15.825 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据变化: EventMonitoring
[2025-08-13 18:00:15.825 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:00:15.825 INF] [IndustrialHMI] [LIN-PC] [26248] 测试已停止
[2025-08-13 18:00:15.825 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModule 停止完成
[2025-08-13 18:00:15.825 INF] [IndustrialHMI] [LIN-PC] [26248] 开始释放 CommunicationTestModule 资源
[2025-08-13 18:00:15.825 INF] [IndustrialHMI] [LIN-PC] [26248] 停止 CommunicationTestModule
[2025-08-13 18:00:15.825 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据变化: EventMonitoring
[2025-08-13 18:00:15.825 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:00:15.825 INF] [IndustrialHMI] [LIN-PC] [26248] 测试已停止
[2025-08-13 18:00:15.825 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModule 停止完成
[2025-08-13 18:00:15.825 DBG] [IndustrialHMI] [LIN-PC] [26248] 系统事件订阅已取消
[2025-08-13 18:00:15.826 DBG] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestPresenter 资源释放完成
[2025-08-13 18:00:15.826 DBG] [IndustrialHMI] [LIN-PC] [26248] EventMonitor 资源释放完成
[2025-08-13 18:00:15.826 INF] [IndustrialHMI] [LIN-PC] [26248] 测试已停止
[2025-08-13 18:00:15.826 DBG] [IndustrialHMI] [LIN-PC] [26248] TestCaseManager 资源释放完成
[2025-08-13 18:00:15.827 DBG] [IndustrialHMI] [LIN-PC] [26248] PerformanceMonitor 资源释放完成
[2025-08-13 18:00:15.827 DBG] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModel 资源释放完成
[2025-08-13 18:00:15.827 INF] [IndustrialHMI] [LIN-PC] [26248] CommunicationTestModule 资源释放完成
[2025-08-13 18:00:15.827 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 18:00:15.827 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.828 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:15.828 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理模块卸载事件: 通信测试
[2025-08-13 18:00:15.828 INF] [IndustrialHMI] [LIN-PC] [26248] 模块卸载成功: 通信测试
[2025-08-13 18:00:15.828 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载模块: 设备监控
[2025-08-13 18:00:15.828 INF] [IndustrialHMI] [LIN-PC] [26248] 停止设备监控模块
[2025-08-13 18:00:15.828 INF] [IndustrialHMI] [LIN-PC] [26248] 用户请求停止设备监控
[2025-08-13 18:00:15.829 INF] [IndustrialHMI] [LIN-PC] [26248] 停止设备监控
[2025-08-13 18:00:15.829 INF] [IndustrialHMI] [LIN-PC] [26248] 设备监控已停止
[2025-08-13 18:00:15.829 INF] [IndustrialHMI] [LIN-PC] [26248] 设备监控模块停止完成
[2025-08-13 18:00:15.829 INF] [IndustrialHMI] [LIN-PC] [26248] 开始释放设备监控模块资源
[2025-08-13 18:00:15.830 DBG] [IndustrialHMI] [LIN-PC] [26248] DevicePresenter 资源释放完成
[2025-08-13 18:00:15.830 INF] [IndustrialHMI] [LIN-PC] [26248] 停止设备监控
[2025-08-13 18:00:15.830 DBG] [IndustrialHMI] [LIN-PC] [26248] DeviceModel 资源释放完成
[2025-08-13 18:00:15.830 INF] [IndustrialHMI] [LIN-PC] [26248] 设备监控模块资源释放完成
[2025-08-13 18:00:15.830 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 18:00:15.830 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.831 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:15.831 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理模块卸载事件: 设备监控
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 模块卸载成功: 设备监控
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载模块: 测试框架模块
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 停止测试框架模块
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 停止内存泄漏测试套件
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 内存泄漏测试套件已停止
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 停止性能测试套件
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 性能测试套件已停止
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 停止集成测试套件
[2025-08-13 18:00:15.831 INF] [IndustrialHMI] [LIN-PC] [26248] 集成测试套件已停止
[2025-08-13 18:00:15.832 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 模型已停止
[2025-08-13 18:00:15.832 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块停止完成
[2025-08-13 18:00:15.832 INF] [IndustrialHMI] [LIN-PC] [26248] 开始释放测试框架模块资源
[2025-08-13 18:00:15.832 INF] [IndustrialHMI] [LIN-PC] [26248] 停止内存泄漏测试套件
[2025-08-13 18:00:15.832 INF] [IndustrialHMI] [LIN-PC] [26248] 内存泄漏测试套件已停止
[2025-08-13 18:00:15.832 DBG] [IndustrialHMI] [LIN-PC] [26248] MemoryLeakTestSuite资源释放完成
[2025-08-13 18:00:15.832 INF] [IndustrialHMI] [LIN-PC] [26248] 停止性能测试套件
[2025-08-13 18:00:15.832 INF] [IndustrialHMI] [LIN-PC] [26248] 性能测试套件已停止
[2025-08-13 18:00:15.832 DBG] [IndustrialHMI] [LIN-PC] [26248] PerformanceTestSuite资源释放完成
[2025-08-13 18:00:15.833 INF] [IndustrialHMI] [LIN-PC] [26248] 停止集成测试套件
[2025-08-13 18:00:15.833 INF] [IndustrialHMI] [LIN-PC] [26248] 集成测试套件已停止
[2025-08-13 18:00:15.833 DBG] [IndustrialHMI] [LIN-PC] [26248] IntegrationTestSuite资源释放完成
[2025-08-13 18:00:15.833 DBG] [IndustrialHMI] [LIN-PC] [26248] TestFrameworkPresenter资源释放完成
[2025-08-13 18:00:15.834 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块资源释放完成
[2025-08-13 18:00:15.834 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 18:00:15.834 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理模块卸载事件: 测试框架模块
[2025-08-13 18:00:15.834 INF] [IndustrialHMI] [LIN-PC] [26248] 模块卸载成功: 测试框架模块
[2025-08-13 18:00:15.834 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载模块: 测试模块
[2025-08-13 18:00:15.834 INF] [IndustrialHMI] [LIN-PC] [26248] 开始停止模块: 测试模块
[2025-08-13 18:00:15.834 INF] [IndustrialHMI] [LIN-PC] [26248] 停止TestModulePresenter
[2025-08-13 18:00:15.835 DBG] [IndustrialHMI] [LIN-PC] [26248] 系统事件取消订阅完成
[2025-08-13 18:00:15.835 DBG] [IndustrialHMI] [LIN-PC] [26248] 模型状态变化: 模型停止完成
[2025-08-13 18:00:15.835 INF] [IndustrialHMI] [LIN-PC] [26248] TestModulePresenter停止完成
[2025-08-13 18:00:15.836 DBG] [IndustrialHMI] [LIN-PC] [26248] 事件取消订阅完成
[2025-08-13 18:00:15.836 INF] [IndustrialHMI] [LIN-PC] [26248] 模块停止完成: 测试模块
[2025-08-13 18:00:15.836 DBG] [IndustrialHMI] [LIN-PC] [26248] 处理系统事件: ModuleStopped
[2025-08-13 18:00:15.836 INF] [IndustrialHMI] [LIN-PC] [26248] 开始释放模块资源: 测试模块
[2025-08-13 18:00:15.836 INF] [IndustrialHMI] [LIN-PC] [26248] 释放TestModulePresenter资源
[2025-08-13 18:00:15.837 INF] [IndustrialHMI] [LIN-PC] [26248] TestModulePresenter资源释放完成
[2025-08-13 18:00:15.841 INF] [IndustrialHMI] [LIN-PC] [26248] 模块资源释放完成: 测试模块
[2025-08-13 18:00:15.841 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 18:00:15.844 INF] [IndustrialHMI] [LIN-PC] [276] 用户请求关闭应用程序
[2025-08-13 18:00:15.845 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.845 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.845 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 18:00:15.846 INF] [IndustrialHMI] [LIN-PC] [276] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:00:15.846 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到系统关闭事件
[2025-08-13 18:00:15.846 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化事件处理完成
[2025-08-13 18:00:15.846 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 系统关闭前清理完成
[2025-08-13 18:00:15.847 INF] [IndustrialHMI] [LIN-PC] [276] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.847 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载所有模块
[2025-08-13 18:00:15.847 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载模块: 报警管理
[2025-08-13 18:00:15.847 INF] [IndustrialHMI] [LIN-PC] [276] 停止报警管理模块
[2025-08-13 18:00:15.848 INF] [IndustrialHMI] [LIN-PC] [276] 停止报警监控
[2025-08-13 18:00:15.848 INF] [IndustrialHMI] [LIN-PC] [276] 停止报警监控
[2025-08-13 18:00:15.848 INF] [IndustrialHMI] [LIN-PC] [276] 报警管理模块停止完成
[2025-08-13 18:00:15.848 INF] [IndustrialHMI] [LIN-PC] [276] 开始释放报警管理模块资源
[2025-08-13 18:00:15.849 INF] [IndustrialHMI] [LIN-PC] [276] 停止报警监控
[2025-08-13 18:00:15.849 INF] [IndustrialHMI] [LIN-PC] [276] 停止报警监控
[2025-08-13 18:00:15.849 DBG] [IndustrialHMI] [LIN-PC] [276] AlarmPresenter 资源释放完成
[2025-08-13 18:00:15.850 INF] [IndustrialHMI] [LIN-PC] [276] 停止报警监控
[2025-08-13 18:00:15.850 DBG] [IndustrialHMI] [LIN-PC] [276] AlarmModel 资源释放完成
[2025-08-13 18:00:15.844 ERR] [IndustrialHMI] [LIN-PC] [26248] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 18:00:15.850 INF] [IndustrialHMI] [LIN-PC] [26248] 模块卸载成功: 测试模块
[2025-08-13 18:00:15.850 INF] [IndustrialHMI] [LIN-PC] [26248] 所有模块卸载完成
[2025-08-13 18:00:15.850 INF] [IndustrialHMI] [LIN-PC] [26248] 应用程序关闭流程完成
[2025-08-13 18:00:15.851 INF] [IndustrialHMI] [LIN-PC] [26248] 主窗体已关闭，资源清理完成
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 开始释放应用程序资源
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 开始卸载所有模块
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 所有模块卸载完成
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] 应用程序资源释放完成
[2025-08-13 18:00:15.859 INF] [IndustrialHMI] [LIN-PC] [26248] === 应用程序正常退出 ===
[2025-08-13 18:00:15.860 INF] [IndustrialHMI] [LIN-PC] [276] 报警管理模块资源释放完成
[2025-08-13 18:00:15.860 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 18:00:15.861 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.862 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:15.863 DBG] [IndustrialHMI] [LIN-PC] [276] 处理模块卸载事件: 报警管理
[2025-08-13 18:00:15.863 INF] [IndustrialHMI] [LIN-PC] [276] 模块卸载成功: 报警管理
[2025-08-13 18:00:15.863 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载模块: 通信测试
[2025-08-13 18:00:15.863 INF] [IndustrialHMI] [LIN-PC] [276] 停止 CommunicationTestModule
[2025-08-13 18:00:15.864 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化: EventMonitoring
[2025-08-13 18:00:15.864 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:00:15.864 INF] [IndustrialHMI] [LIN-PC] [276] 测试已停止
[2025-08-13 18:00:15.864 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModule 停止完成
[2025-08-13 18:00:15.864 INF] [IndustrialHMI] [LIN-PC] [276] 开始释放 CommunicationTestModule 资源
[2025-08-13 18:00:15.864 INF] [IndustrialHMI] [LIN-PC] [276] 停止 CommunicationTestModule
[2025-08-13 18:00:15.864 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化: EventMonitoring
[2025-08-13 18:00:15.864 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:00:15.864 INF] [IndustrialHMI] [LIN-PC] [276] 测试已停止
[2025-08-13 18:00:15.864 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModule 停止完成
[2025-08-13 18:00:15.865 DBG] [IndustrialHMI] [LIN-PC] [276] 系统事件订阅已取消
[2025-08-13 18:00:15.865 DBG] [IndustrialHMI] [LIN-PC] [276] CommunicationTestPresenter 资源释放完成
[2025-08-13 18:00:15.866 DBG] [IndustrialHMI] [LIN-PC] [276] EventMonitor 资源释放完成
[2025-08-13 18:00:15.866 INF] [IndustrialHMI] [LIN-PC] [276] 测试已停止
[2025-08-13 18:00:15.866 DBG] [IndustrialHMI] [LIN-PC] [276] TestCaseManager 资源释放完成
[2025-08-13 18:00:15.866 DBG] [IndustrialHMI] [LIN-PC] [276] PerformanceMonitor 资源释放完成
[2025-08-13 18:00:15.866 DBG] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModel 资源释放完成
[2025-08-13 18:00:15.866 INF] [IndustrialHMI] [LIN-PC] [276] CommunicationTestModule 资源释放完成
[2025-08-13 18:00:15.866 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 18:00:15.870 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.873 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:15.875 DBG] [IndustrialHMI] [LIN-PC] [276] 处理模块卸载事件: 通信测试
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 模块卸载成功: 通信测试
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载模块: 设备监控
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 停止设备监控模块
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 用户请求停止设备监控
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 停止设备监控
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 设备监控已停止
[2025-08-13 18:00:15.875 INF] [IndustrialHMI] [LIN-PC] [276] 设备监控模块停止完成
[2025-08-13 18:00:15.876 INF] [IndustrialHMI] [LIN-PC] [276] 开始释放设备监控模块资源
[2025-08-13 18:00:15.877 DBG] [IndustrialHMI] [LIN-PC] [276] DevicePresenter 资源释放完成
[2025-08-13 18:00:15.877 INF] [IndustrialHMI] [LIN-PC] [276] 停止设备监控
[2025-08-13 18:00:15.877 DBG] [IndustrialHMI] [LIN-PC] [276] DeviceModel 资源释放完成
[2025-08-13 18:00:15.878 INF] [IndustrialHMI] [LIN-PC] [276] 设备监控模块资源释放完成
[2025-08-13 18:00:15.878 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 18:00:15.878 DBG] [IndustrialHMI] [LIN-PC] [276] 模型数据已更新，视图已刷新
[2025-08-13 18:00:15.879 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:00:15.880 DBG] [IndustrialHMI] [LIN-PC] [276] 处理模块卸载事件: 设备监控
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 模块卸载成功: 设备监控
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载模块: 测试框架模块
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 停止测试框架模块
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 停止内存泄漏测试套件
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 内存泄漏测试套件已停止
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 停止性能测试套件
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 性能测试套件已停止
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 停止集成测试套件
[2025-08-13 18:00:15.880 INF] [IndustrialHMI] [LIN-PC] [276] 集成测试套件已停止
[2025-08-13 18:00:15.881 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 模型已停止
[2025-08-13 18:00:15.881 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块停止完成
[2025-08-13 18:00:15.881 INF] [IndustrialHMI] [LIN-PC] [276] 开始释放测试框架模块资源
[2025-08-13 18:00:15.881 INF] [IndustrialHMI] [LIN-PC] [276] 停止内存泄漏测试套件
[2025-08-13 18:00:15.881 INF] [IndustrialHMI] [LIN-PC] [276] 内存泄漏测试套件已停止
[2025-08-13 18:00:15.881 DBG] [IndustrialHMI] [LIN-PC] [276] MemoryLeakTestSuite资源释放完成
[2025-08-13 18:00:15.882 INF] [IndustrialHMI] [LIN-PC] [276] 停止性能测试套件
[2025-08-13 18:00:15.882 INF] [IndustrialHMI] [LIN-PC] [276] 性能测试套件已停止
[2025-08-13 18:00:15.882 DBG] [IndustrialHMI] [LIN-PC] [276] PerformanceTestSuite资源释放完成
[2025-08-13 18:00:15.882 INF] [IndustrialHMI] [LIN-PC] [276] 停止集成测试套件
[2025-08-13 18:00:15.882 INF] [IndustrialHMI] [LIN-PC] [276] 集成测试套件已停止
[2025-08-13 18:00:15.882 DBG] [IndustrialHMI] [LIN-PC] [276] IntegrationTestSuite资源释放完成
[2025-08-13 18:00:15.882 DBG] [IndustrialHMI] [LIN-PC] [276] TestFrameworkPresenter资源释放完成
[2025-08-13 18:00:15.883 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块资源释放完成
[2025-08-13 18:00:15.883 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 18:00:15.883 DBG] [IndustrialHMI] [LIN-PC] [276] 处理模块卸载事件: 测试框架模块
[2025-08-13 18:00:15.883 INF] [IndustrialHMI] [LIN-PC] [276] 模块卸载成功: 测试框架模块
[2025-08-13 18:00:15.883 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载模块: 测试模块
[2025-08-13 18:00:15.883 INF] [IndustrialHMI] [LIN-PC] [276] 开始停止模块: 测试模块
[2025-08-13 18:00:15.884 INF] [IndustrialHMI] [LIN-PC] [276] 停止TestModulePresenter
[2025-08-13 18:00:15.884 DBG] [IndustrialHMI] [LIN-PC] [276] 系统事件取消订阅完成
[2025-08-13 18:00:15.886 DBG] [IndustrialHMI] [LIN-PC] [276] 模型状态变化: 模型停止完成
[2025-08-13 18:00:15.886 INF] [IndustrialHMI] [LIN-PC] [276] TestModulePresenter停止完成
[2025-08-13 18:00:15.886 DBG] [IndustrialHMI] [LIN-PC] [276] 事件取消订阅完成
[2025-08-13 18:00:15.886 INF] [IndustrialHMI] [LIN-PC] [276] 模块停止完成: 测试模块
[2025-08-13 18:00:15.887 DBG] [IndustrialHMI] [LIN-PC] [276] 处理系统事件: ModuleStopped
[2025-08-13 18:00:15.887 INF] [IndustrialHMI] [LIN-PC] [276] 开始释放模块资源: 测试模块
[2025-08-13 18:00:15.887 INF] [IndustrialHMI] [LIN-PC] [276] 释放TestModulePresenter资源
[2025-08-13 18:00:15.887 INF] [IndustrialHMI] [LIN-PC] [276] TestModulePresenter资源释放完成
[2025-08-13 18:00:15.892 INF] [IndustrialHMI] [LIN-PC] [276] 模块资源释放完成: 测试模块
[2025-08-13 18:00:15.892 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 18:00:15.895 ERR] [IndustrialHMI] [LIN-PC] [276] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 18:00:15.902 INF] [IndustrialHMI] [LIN-PC] [276] 模块卸载成功: 测试模块
[2025-08-13 18:00:15.902 INF] [IndustrialHMI] [LIN-PC] [276] 所有模块卸载完成
[2025-08-13 18:00:15.902 INF] [IndustrialHMI] [LIN-PC] [276] 应用程序关闭流程完成
[2025-08-13 18:00:15.903 INF] [IndustrialHMI] [LIN-PC] [276] 主窗体已关闭，资源清理完成
[2025-08-13 18:00:15.925 INF] [IndustrialHMI] [LIN-PC] [276] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.925 INF] [IndustrialHMI] [LIN-PC] [276] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:00:15.925 INF] [IndustrialHMI] [LIN-PC] [276] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:00:15.926 INF] [IndustrialHMI] [LIN-PC] [276] 开始释放应用程序资源
[2025-08-13 18:00:15.926 INF] [IndustrialHMI] [LIN-PC] [276] 开始卸载所有模块
[2025-08-13 18:00:15.926 INF] [IndustrialHMI] [LIN-PC] [276] 所有模块卸载完成
[2025-08-13 18:00:15.926 INF] [IndustrialHMI] [LIN-PC] [276] 应用程序资源释放完成
[2025-08-13 18:00:15.926 INF] [IndustrialHMI] [LIN-PC] [276] === 应用程序正常退出 ===
﻿[2025-08-13 18:08:29.916 INF] [IndustrialHMI] [LIN-PC] [4488] === 应用程序启动 ===
[2025-08-13 18:08:29.947 INF] [IndustrialHMI] [LIN-PC] [4488] 应用程序版本: 1.0.0.0
[2025-08-13 18:08:29.947 INF] [IndustrialHMI] [LIN-PC] [4488] 启动参数: 
[2025-08-13 18:08:29.948 INF] [IndustrialHMI] [LIN-PC] [4488] 开始初始化应用程序
[2025-08-13 18:08:29.948 INF] [IndustrialHMI] [LIN-PC] [4488] 步骤1: 创建服务容器
[2025-08-13 18:08:29.952 INF] [IndustrialHMI] [LIN-PC] [4488] 开始创建DryIoc容器
[2025-08-13 18:08:29.968 DBG] [IndustrialHMI] [LIN-PC] [4488] DryIoc容器创建成功，开始注册服务
[2025-08-13 18:08:29.972 DBG] [IndustrialHMI] [LIN-PC] [4488] 注册自定义日志记录器为单例
[2025-08-13 18:08:29.972 DBG] [IndustrialHMI] [LIN-PC] [4488] 注册EventAggregator为单例
[2025-08-13 18:08:29.983 DBG] [IndustrialHMI] [LIN-PC] [4488] 注册ConfigurationService为单例，已添加多个配置源并启用热更新
[2025-08-13 18:08:29.996 DBG] [IndustrialHMI] [LIN-PC] [4488] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 18:08:30.024 DBG] [IndustrialHMI] [LIN-PC] [4488] 注册MainForm为单例
[2025-08-13 18:08:30.024 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始验证DryIoc容器配置
[2025-08-13 18:08:30.024 DBG] [IndustrialHMI] [LIN-PC] [4488] DryIoc容器配置验证通过
[2025-08-13 18:08:30.024 INF] [IndustrialHMI] [LIN-PC] [4488] DryIoc容器创建和配置完成
[2025-08-13 18:08:30.024 INF] [IndustrialHMI] [LIN-PC] [4488] 步骤2: 创建主窗体
[2025-08-13 18:08:30.024 INF] [IndustrialHMI] [LIN-PC] [4488] 步骤3: 创建模块加载器
[2025-08-13 18:08:30.024 INF] [IndustrialHMI] [LIN-PC] [4488] 步骤4: 加载模块
[2025-08-13 18:08:30.025 INF] [IndustrialHMI] [LIN-PC] [4488] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 18:08:30.026 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 18:08:30.032 DBG] [IndustrialHMI] [LIN-PC] [4488] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 发现模块类型: AlarmModuleMain
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载模块: AlarmModuleMain
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 18:08:30.034 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 18:08:30.035 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 18:08:30.036 INF] [IndustrialHMI] [LIN-PC] [4488] 开始初始化报警管理模块
[2025-08-13 18:08:30.037 DBG] [IndustrialHMI] [LIN-PC] [4488] 报警服务创建完成
[2025-08-13 18:08:30.046 DBG] [IndustrialHMI] [LIN-PC] [4488] 报警视图创建完成
[2025-08-13 18:08:30.047 DBG] [IndustrialHMI] [LIN-PC] [4488] AlarmModel 初始化完成
[2025-08-13 18:08:30.047 DBG] [IndustrialHMI] [LIN-PC] [4488] 报警模型创建完成
[2025-08-13 18:08:30.048 DBG] [IndustrialHMI] [LIN-PC] [4488] AlarmPresenter 初始化完成
[2025-08-13 18:08:30.048 DBG] [IndustrialHMI] [LIN-PC] [4488] 报警表示器创建完成
[2025-08-13 18:08:30.048 INF] [IndustrialHMI] [LIN-PC] [4488] MVP组件创建完成
[2025-08-13 18:08:30.049 DBG] [IndustrialHMI] [LIN-PC] [4488] 系统事件订阅完成
[2025-08-13 18:08:30.049 INF] [IndustrialHMI] [LIN-PC] [4488] 报警管理模块初始化完成
[2025-08-13 18:08:30.049 INF] [IndustrialHMI] [LIN-PC] [4488] 启动报警管理模块
[2025-08-13 18:08:30.049 INF] [IndustrialHMI] [LIN-PC] [4488] 启动报警监控
[2025-08-13 18:08:30.049 INF] [IndustrialHMI] [LIN-PC] [4488] 开始报警监控
[2025-08-13 18:08:30.049 INF] [IndustrialHMI] [LIN-PC] [4488] 报警管理模块启动完成
[2025-08-13 18:08:30.050 INF] [IndustrialHMI] [LIN-PC] [4488] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 18:08:30.051 DBG] [IndustrialHMI] [LIN-PC] [4488] 模块已加载: 报警管理
[2025-08-13 18:08:30.051 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.053 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 18:08:30.054 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 18:08:30.054 INF] [IndustrialHMI] [LIN-PC] [4488] 开始初始化 CommunicationTestModule
[2025-08-13 18:08:30.055 INF] [IndustrialHMI] [LIN-PC] [4488] 初始化了 8 个测试用例
[2025-08-13 18:08:30.056 INF] [IndustrialHMI] [LIN-PC] [4488] 性能监控器初始化完成
[2025-08-13 18:08:30.056 INF] [IndustrialHMI] [LIN-PC] [4488] CommunicationTestModel 初始化完成
[2025-08-13 18:08:30.061 DBG] [IndustrialHMI] [LIN-PC] [4488] CommunicationTestView 初始化完成
[2025-08-13 18:08:30.064 DBG] [IndustrialHMI] [LIN-PC] [4488] 视图数据初始化完成
[2025-08-13 18:08:30.064 INF] [IndustrialHMI] [LIN-PC] [4488] CommunicationTestPresenter 初始化完成
[2025-08-13 18:08:30.064 DBG] [IndustrialHMI] [LIN-PC] [4488] MVP组件创建完成
[2025-08-13 18:08:30.065 DBG] [IndustrialHMI] [LIN-PC] [4488] 系统事件订阅完成
[2025-08-13 18:08:30.065 INF] [IndustrialHMI] [LIN-PC] [4488] CommunicationTestModule 初始化完成
[2025-08-13 18:08:30.065 INF] [IndustrialHMI] [LIN-PC] [4488] 启动 CommunicationTestModule
[2025-08-13 18:08:30.065 INF] [IndustrialHMI] [LIN-PC] [4488] CommunicationTestModule 启动完成
[2025-08-13 18:08:30.065 INF] [IndustrialHMI] [LIN-PC] [4488] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 18:08:30.065 DBG] [IndustrialHMI] [LIN-PC] [4488] 模块已加载: 通信测试
[2025-08-13 18:08:30.065 DBG] [IndustrialHMI] [LIN-PC] [4488] 收到模块加载事件: 通信测试
[2025-08-13 18:08:30.065 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 发现模块类型: 
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:30.067 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 18:08:30.068 DBG] [IndustrialHMI] [LIN-PC] [4488] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 发现模块类型: DeviceModuleMain
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.069 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.070 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载模块: DeviceModuleMain
[2025-08-13 18:08:30.070 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 18:08:30.070 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 18:08:30.070 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 18:08:30.070 INF] [IndustrialHMI] [LIN-PC] [4488] 开始初始化设备监控模块
[2025-08-13 18:08:30.071 DBG] [IndustrialHMI] [LIN-PC] [4488] 设备服务创建完成
[2025-08-13 18:08:30.074 DBG] [IndustrialHMI] [LIN-PC] [4488] 设备视图创建完成
[2025-08-13 18:08:30.074 DBG] [IndustrialHMI] [LIN-PC] [4488] DeviceModel 初始化完成
[2025-08-13 18:08:30.074 DBG] [IndustrialHMI] [LIN-PC] [4488] 设备模型创建完成
[2025-08-13 18:08:30.075 DBG] [IndustrialHMI] [LIN-PC] [4488] DevicePresenter 初始化完成
[2025-08-13 18:08:30.075 DBG] [IndustrialHMI] [LIN-PC] [4488] 设备表示器创建完成
[2025-08-13 18:08:30.075 INF] [IndustrialHMI] [LIN-PC] [4488] MVP组件创建完成
[2025-08-13 18:08:30.075 DBG] [IndustrialHMI] [LIN-PC] [4488] 系统事件订阅完成
[2025-08-13 18:08:30.075 INF] [IndustrialHMI] [LIN-PC] [4488] 设备监控模块初始化完成
[2025-08-13 18:08:30.075 INF] [IndustrialHMI] [LIN-PC] [4488] 启动设备监控模块
[2025-08-13 18:08:30.075 INF] [IndustrialHMI] [LIN-PC] [4488] 用户请求开始设备监控
[2025-08-13 18:08:30.075 INF] [IndustrialHMI] [LIN-PC] [4488] 开始设备监控
[2025-08-13 18:08:30.076 INF] [IndustrialHMI] [LIN-PC] [4488] 设备监控已启动
[2025-08-13 18:08:30.076 INF] [IndustrialHMI] [LIN-PC] [4488] 设备监控模块启动完成
[2025-08-13 18:08:30.076 INF] [IndustrialHMI] [LIN-PC] [4488] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 18:08:30.076 DBG] [IndustrialHMI] [LIN-PC] [4488] 模块已加载: 设备监控
[2025-08-13 18:08:30.076 DBG] [IndustrialHMI] [LIN-PC] [4488] 收到模块加载事件: 设备监控
[2025-08-13 18:08:30.076 DBG] [IndustrialHMI] [LIN-PC] [4488] 模块已加载: 设备监控
[2025-08-13 18:08:30.076 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 18:08:30.077 DBG] [IndustrialHMI] [LIN-PC] [4488] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 18:08:30.078 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 18:08:30.078 INF] [IndustrialHMI] [LIN-PC] [4488] 开始初始化测试框架模块
[2025-08-13 18:08:30.079 DBG] [IndustrialHMI] [LIN-PC] [4488] ConfigurationService未注入（可选）
[2025-08-13 18:08:34.828 DBG] [IndustrialHMI] [LIN-PC] [4488] 初始化TestFrameworkPresenter
[2025-08-13 18:08:34.839 DBG] [IndustrialHMI] [LIN-PC] [4488] TestFrameworkPresenter初始化完成
[2025-08-13 18:08:34.839 DBG] [IndustrialHMI] [LIN-PC] [4488] 测试框架模块事件订阅完成
[2025-08-13 18:08:34.839 INF] [IndustrialHMI] [LIN-PC] [4488] 测试框架模块初始化完成
[2025-08-13 18:08:34.840 INF] [IndustrialHMI] [LIN-PC] [4488] 启动测试框架模块
[2025-08-13 18:08:34.840 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 模型已启动
[2025-08-13 18:08:34.840 DBG] [IndustrialHMI] [LIN-PC] [4488] 加载TestFramework数据
[2025-08-13 18:08:34.841 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型数据已更新，视图已刷新
[2025-08-13 18:08:34.842 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 数据加载完成
[2025-08-13 18:08:34.842 DBG] [IndustrialHMI] [LIN-PC] [4488] TestFramework数据加载完成
[2025-08-13 18:08:34.842 INF] [IndustrialHMI] [LIN-PC] [4488] 初始化集成测试套件
[2025-08-13 18:08:34.842 INF] [IndustrialHMI] [LIN-PC] [4488] 集成测试套件初始化完成
[2025-08-13 18:08:34.842 INF] [IndustrialHMI] [LIN-PC] [4488] 初始化性能测试套件
[2025-08-13 18:08:34.962 INF] [IndustrialHMI] [LIN-PC] [4488] 性能测试套件初始化完成
[2025-08-13 18:08:34.962 INF] [IndustrialHMI] [LIN-PC] [4488] 初始化内存泄漏测试套件
[2025-08-13 18:08:34.962 INF] [IndustrialHMI] [LIN-PC] [4488] 内存泄漏测试套件初始化完成
[2025-08-13 18:08:34.962 INF] [IndustrialHMI] [LIN-PC] [4488] 测试框架模块启动完成
[2025-08-13 18:08:34.962 INF] [IndustrialHMI] [LIN-PC] [4488] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 18:08:34.962 INF] [IndustrialHMI] [LIN-PC] [4488] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 18:08:34.962 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型数据已更新，视图已刷新
[2025-08-13 18:08:34.963 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:08:34.963 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 发现模块类型: TestModuleMain
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 开始加载模块: TestModuleMain
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 TestModuleMain 注入Logger
[2025-08-13 18:08:34.964 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 18:08:34.965 INF] [IndustrialHMI] [LIN-PC] [4488] 开始初始化模块: 测试模块
[2025-08-13 18:08:34.965 WRN] [IndustrialHMI] [LIN-PC] [4488] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 18:08:34.965 DBG] [IndustrialHMI] [LIN-PC] [4488] 依赖注入验证通过
[2025-08-13 18:08:34.965 DBG] [IndustrialHMI] [LIN-PC] [4488] 创建TestModuleModel成功
[2025-08-13 18:08:34.968 DBG] [IndustrialHMI] [LIN-PC] [4488] 创建TestModuleView成功
[2025-08-13 18:08:34.969 DBG] [IndustrialHMI] [LIN-PC] [4488] TestModulePresenter创建完成
[2025-08-13 18:08:34.969 DBG] [IndustrialHMI] [LIN-PC] [4488] 创建TestModulePresenter成功
[2025-08-13 18:08:34.969 DBG] [IndustrialHMI] [LIN-PC] [4488] 事件订阅完成
[2025-08-13 18:08:34.969 INF] [IndustrialHMI] [LIN-PC] [4488] 模块初始化完成: 测试模块
[2025-08-13 18:08:34.970 INF] [IndustrialHMI] [LIN-PC] [4488] 开始启动模块: 测试模块
[2025-08-13 18:08:34.970 INF] [IndustrialHMI] [LIN-PC] [4488] 启动TestModulePresenter
[2025-08-13 18:08:34.977 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 模型启动完成
[2025-08-13 18:08:34.978 DBG] [IndustrialHMI] [LIN-PC] [4488] 系统事件订阅完成
[2025-08-13 18:08:34.978 INF] [IndustrialHMI] [LIN-PC] [4488] TestModulePresenter启动完成
[2025-08-13 18:08:34.978 INF] [IndustrialHMI] [LIN-PC] [4488] 模块启动完成: 测试模块
[2025-08-13 18:08:34.979 DBG] [IndustrialHMI] [LIN-PC] [4488] 处理系统事件: ModuleStarted
[2025-08-13 18:08:34.979 INF] [IndustrialHMI] [LIN-PC] [4488] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 18:08:34.979 INF] [IndustrialHMI] [LIN-PC] [4488] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 18:08:34.980 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型数据已更新，视图已刷新
[2025-08-13 18:08:34.980 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:08:34.981 DBG] [IndustrialHMI] [LIN-PC] [4488] 处理模块加载事件: 测试模块
[2025-08-13 18:08:34.981 INF] [IndustrialHMI] [LIN-PC] [4488] 模块加载完成，共加载 5 个模块
[2025-08-13 18:08:34.981 INF] [IndustrialHMI] [LIN-PC] [4488] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 18:08:34.982 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 报警管理 添加了UI标签页
[2025-08-13 18:08:34.982 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 通信测试 添加了UI标签页
[2025-08-13 18:08:34.982 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 设备监控 添加了UI标签页
[2025-08-13 18:08:34.983 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 18:08:34.984 DBG] [IndustrialHMI] [LIN-PC] [4488] 为模块 测试模块 添加了UI标签页
[2025-08-13 18:08:34.984 INF] [IndustrialHMI] [LIN-PC] [4488] 步骤5: 初始化主窗体
[2025-08-13 18:08:34.984 DBG] [IndustrialHMI] [LIN-PC] [4488] 主窗体初始化完成
[2025-08-13 18:08:34.984 INF] [IndustrialHMI] [LIN-PC] [4488] 应用程序初始化完成
[2025-08-13 18:08:34.984 INF] [IndustrialHMI] [LIN-PC] [4488] 应用程序初始化成功，启动主窗体
[2025-08-13 18:08:34.984 INF] [IndustrialHMI] [LIN-PC] [4488] 测试框架模块收到系统启动事件
[2025-08-13 18:08:34.985 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型数据已更新，视图已刷新
[2025-08-13 18:08:34.985 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 18:08:34.985 INF] [IndustrialHMI] [LIN-PC] [4488] 模块 测试模块 收到系统启动事件
[2025-08-13 18:08:34.986 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 收到系统启动事件
[2025-08-13 18:08:35.073 DBG] [IndustrialHMI] [LIN-PC] [4488] 主窗体事件订阅完成
[2025-08-13 18:08:35.079 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型数据变化事件处理完成
[2025-08-13 18:08:35.080 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 18:08:35.081 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 定时状态更新 - 18:08:35
[2025-08-13 18:08:35.995 DBG] [IndustrialHMI] [LIN-PC] [4488] 模型状态变化: 系统启动后初始化完成
﻿[2025-08-13 18:08:37.986 INF] [IndustrialHMI] [LIN-PC] [32208] === 应用程序启动 ===
[2025-08-13 18:08:38.017 INF] [IndustrialHMI] [LIN-PC] [32208] 应用程序版本: 1.0.0.0
[2025-08-13 18:08:38.017 INF] [IndustrialHMI] [LIN-PC] [32208] 启动参数: 
[2025-08-13 18:08:38.018 INF] [IndustrialHMI] [LIN-PC] [32208] 开始初始化应用程序
[2025-08-13 18:08:38.018 INF] [IndustrialHMI] [LIN-PC] [32208] 步骤1: 创建服务容器
[2025-08-13 18:08:38.022 INF] [IndustrialHMI] [LIN-PC] [32208] 开始创建DryIoc容器
[2025-08-13 18:08:38.036 DBG] [IndustrialHMI] [LIN-PC] [32208] DryIoc容器创建成功，开始注册服务
[2025-08-13 18:08:38.040 DBG] [IndustrialHMI] [LIN-PC] [32208] 注册自定义日志记录器为单例
[2025-08-13 18:08:38.040 DBG] [IndustrialHMI] [LIN-PC] [32208] 注册EventAggregator为单例
[2025-08-13 18:08:38.051 DBG] [IndustrialHMI] [LIN-PC] [32208] 注册ConfigurationService为单例，已添加多个配置源并启用热更新
[2025-08-13 18:08:38.062 DBG] [IndustrialHMI] [LIN-PC] [32208] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 18:08:38.098 DBG] [IndustrialHMI] [LIN-PC] [32208] 注册MainForm为单例
[2025-08-13 18:08:38.098 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始验证DryIoc容器配置
[2025-08-13 18:08:38.098 DBG] [IndustrialHMI] [LIN-PC] [32208] DryIoc容器配置验证通过
[2025-08-13 18:08:38.098 INF] [IndustrialHMI] [LIN-PC] [32208] DryIoc容器创建和配置完成
[2025-08-13 18:08:38.098 INF] [IndustrialHMI] [LIN-PC] [32208] 步骤2: 创建主窗体
[2025-08-13 18:08:38.098 INF] [IndustrialHMI] [LIN-PC] [32208] 步骤3: 创建模块加载器
[2025-08-13 18:08:38.098 INF] [IndustrialHMI] [LIN-PC] [32208] 步骤4: 加载模块
[2025-08-13 18:08:38.099 INF] [IndustrialHMI] [LIN-PC] [32208] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 18:08:38.100 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 18:08:38.106 DBG] [IndustrialHMI] [LIN-PC] [32208] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 发现模块类型: AlarmModuleMain
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.107 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.108 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载模块: AlarmModuleMain
[2025-08-13 18:08:38.108 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 18:08:38.109 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 18:08:38.109 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 18:08:38.110 INF] [IndustrialHMI] [LIN-PC] [32208] 开始初始化报警管理模块
[2025-08-13 18:08:38.112 DBG] [IndustrialHMI] [LIN-PC] [32208] 报警服务创建完成
[2025-08-13 18:08:38.120 DBG] [IndustrialHMI] [LIN-PC] [32208] 报警视图创建完成
[2025-08-13 18:08:38.121 DBG] [IndustrialHMI] [LIN-PC] [32208] AlarmModel 初始化完成
[2025-08-13 18:08:38.121 DBG] [IndustrialHMI] [LIN-PC] [32208] 报警模型创建完成
[2025-08-13 18:08:38.122 DBG] [IndustrialHMI] [LIN-PC] [32208] AlarmPresenter 初始化完成
[2025-08-13 18:08:38.122 DBG] [IndustrialHMI] [LIN-PC] [32208] 报警表示器创建完成
[2025-08-13 18:08:38.122 INF] [IndustrialHMI] [LIN-PC] [32208] MVP组件创建完成
[2025-08-13 18:08:38.123 DBG] [IndustrialHMI] [LIN-PC] [32208] 系统事件订阅完成
[2025-08-13 18:08:38.123 INF] [IndustrialHMI] [LIN-PC] [32208] 报警管理模块初始化完成
[2025-08-13 18:08:38.123 INF] [IndustrialHMI] [LIN-PC] [32208] 启动报警管理模块
[2025-08-13 18:08:38.123 INF] [IndustrialHMI] [LIN-PC] [32208] 启动报警监控
[2025-08-13 18:08:38.123 INF] [IndustrialHMI] [LIN-PC] [32208] 开始报警监控
[2025-08-13 18:08:38.123 INF] [IndustrialHMI] [LIN-PC] [32208] 报警管理模块启动完成
[2025-08-13 18:08:38.124 INF] [IndustrialHMI] [LIN-PC] [32208] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 18:08:38.125 DBG] [IndustrialHMI] [LIN-PC] [32208] 模块已加载: 报警管理
[2025-08-13 18:08:38.125 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 18:08:38.128 DBG] [IndustrialHMI] [LIN-PC] [32208] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunPerformanceTestAsync>d__22 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.129 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 18:08:38.130 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 18:08:38.130 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 18:08:38.130 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 18:08:38.130 INF] [IndustrialHMI] [LIN-PC] [32208] 开始初始化 CommunicationTestModule
[2025-08-13 18:08:38.131 INF] [IndustrialHMI] [LIN-PC] [32208] 初始化了 8 个测试用例
[2025-08-13 18:08:38.132 INF] [IndustrialHMI] [LIN-PC] [32208] 性能监控器初始化完成
[2025-08-13 18:08:38.132 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModel 初始化完成
[2025-08-13 18:08:38.137 DBG] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestView 初始化完成
[2025-08-13 18:08:38.140 DBG] [IndustrialHMI] [LIN-PC] [32208] 视图数据初始化完成
[2025-08-13 18:08:38.140 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestPresenter 初始化完成
[2025-08-13 18:08:38.140 DBG] [IndustrialHMI] [LIN-PC] [32208] MVP组件创建完成
[2025-08-13 18:08:38.140 DBG] [IndustrialHMI] [LIN-PC] [32208] 系统事件订阅完成
[2025-08-13 18:08:38.140 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModule 初始化完成
[2025-08-13 18:08:38.141 INF] [IndustrialHMI] [LIN-PC] [32208] 启动 CommunicationTestModule
[2025-08-13 18:08:38.141 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModule 启动完成
[2025-08-13 18:08:38.141 INF] [IndustrialHMI] [LIN-PC] [32208] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 18:08:38.141 DBG] [IndustrialHMI] [LIN-PC] [32208] 模块已加载: 通信测试
[2025-08-13 18:08:38.141 DBG] [IndustrialHMI] [LIN-PC] [32208] 收到模块加载事件: 通信测试
[2025-08-13 18:08:38.141 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 发现模块类型: 
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 18:08:38.143 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 发现模块类型: DeviceModuleMain
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载模块: DeviceModuleMain
[2025-08-13 18:08:38.145 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 18:08:38.146 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 18:08:38.146 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 18:08:38.146 INF] [IndustrialHMI] [LIN-PC] [32208] 开始初始化设备监控模块
[2025-08-13 18:08:38.147 DBG] [IndustrialHMI] [LIN-PC] [32208] 设备服务创建完成
[2025-08-13 18:08:38.149 DBG] [IndustrialHMI] [LIN-PC] [32208] 设备视图创建完成
[2025-08-13 18:08:38.149 DBG] [IndustrialHMI] [LIN-PC] [32208] DeviceModel 初始化完成
[2025-08-13 18:08:38.149 DBG] [IndustrialHMI] [LIN-PC] [32208] 设备模型创建完成
[2025-08-13 18:08:38.150 DBG] [IndustrialHMI] [LIN-PC] [32208] DevicePresenter 初始化完成
[2025-08-13 18:08:38.150 DBG] [IndustrialHMI] [LIN-PC] [32208] 设备表示器创建完成
[2025-08-13 18:08:38.150 INF] [IndustrialHMI] [LIN-PC] [32208] MVP组件创建完成
[2025-08-13 18:08:38.150 DBG] [IndustrialHMI] [LIN-PC] [32208] 系统事件订阅完成
[2025-08-13 18:08:38.150 INF] [IndustrialHMI] [LIN-PC] [32208] 设备监控模块初始化完成
[2025-08-13 18:08:38.150 INF] [IndustrialHMI] [LIN-PC] [32208] 启动设备监控模块
[2025-08-13 18:08:38.151 INF] [IndustrialHMI] [LIN-PC] [32208] 用户请求开始设备监控
[2025-08-13 18:08:38.151 INF] [IndustrialHMI] [LIN-PC] [32208] 开始设备监控
[2025-08-13 18:08:38.151 INF] [IndustrialHMI] [LIN-PC] [32208] 设备监控已启动
[2025-08-13 18:08:38.151 INF] [IndustrialHMI] [LIN-PC] [32208] 设备监控模块启动完成
[2025-08-13 18:08:38.151 INF] [IndustrialHMI] [LIN-PC] [32208] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 18:08:38.151 DBG] [IndustrialHMI] [LIN-PC] [32208] 模块已加载: 设备监控
[2025-08-13 18:08:38.151 DBG] [IndustrialHMI] [LIN-PC] [32208] 收到模块加载事件: 设备监控
[2025-08-13 18:08:38.151 DBG] [IndustrialHMI] [LIN-PC] [32208] 模块已加载: 设备监控
[2025-08-13 18:08:38.151 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 18:08:38.152 DBG] [IndustrialHMI] [LIN-PC] [32208] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 18:08:38.153 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 18:08:38.154 INF] [IndustrialHMI] [LIN-PC] [32208] 开始初始化测试框架模块
[2025-08-13 18:08:38.154 DBG] [IndustrialHMI] [LIN-PC] [32208] ConfigurationService未注入（可选）
[2025-08-13 18:08:38.651 DBG] [IndustrialHMI] [LIN-PC] [32208] 初始化TestFrameworkPresenter
[2025-08-13 18:08:38.661 DBG] [IndustrialHMI] [LIN-PC] [32208] TestFrameworkPresenter初始化完成
[2025-08-13 18:08:38.662 DBG] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块事件订阅完成
[2025-08-13 18:08:38.662 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块初始化完成
[2025-08-13 18:08:38.662 INF] [IndustrialHMI] [LIN-PC] [32208] 启动测试框架模块
[2025-08-13 18:08:38.663 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 模型已启动
[2025-08-13 18:08:38.663 DBG] [IndustrialHMI] [LIN-PC] [32208] 加载TestFramework数据
[2025-08-13 18:08:38.664 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:38.664 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 数据加载完成
[2025-08-13 18:08:38.664 DBG] [IndustrialHMI] [LIN-PC] [32208] TestFramework数据加载完成
[2025-08-13 18:08:38.664 INF] [IndustrialHMI] [LIN-PC] [32208] 初始化集成测试套件
[2025-08-13 18:08:38.664 INF] [IndustrialHMI] [LIN-PC] [32208] 集成测试套件初始化完成
[2025-08-13 18:08:38.664 INF] [IndustrialHMI] [LIN-PC] [32208] 初始化性能测试套件
[2025-08-13 18:08:38.781 INF] [IndustrialHMI] [LIN-PC] [32208] 性能测试套件初始化完成
[2025-08-13 18:08:38.782 INF] [IndustrialHMI] [LIN-PC] [32208] 初始化内存泄漏测试套件
[2025-08-13 18:08:38.782 INF] [IndustrialHMI] [LIN-PC] [32208] 内存泄漏测试套件初始化完成
[2025-08-13 18:08:38.782 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块启动完成
[2025-08-13 18:08:38.782 INF] [IndustrialHMI] [LIN-PC] [32208] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 18:08:38.782 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 18:08:38.783 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:38.783 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:08:38.783 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 发现模块类型: TestModuleMain
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 18:08:38.785 DBG] [IndustrialHMI] [LIN-PC] [32208] 开始加载模块: TestModuleMain
[2025-08-13 18:08:38.786 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 18:08:38.786 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 TestModuleMain 注入Logger
[2025-08-13 18:08:38.786 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 18:08:38.786 INF] [IndustrialHMI] [LIN-PC] [32208] 开始初始化模块: 测试模块
[2025-08-13 18:08:38.786 WRN] [IndustrialHMI] [LIN-PC] [32208] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 18:08:38.786 DBG] [IndustrialHMI] [LIN-PC] [32208] 依赖注入验证通过
[2025-08-13 18:08:38.786 DBG] [IndustrialHMI] [LIN-PC] [32208] 创建TestModuleModel成功
[2025-08-13 18:08:38.789 DBG] [IndustrialHMI] [LIN-PC] [32208] 创建TestModuleView成功
[2025-08-13 18:08:38.789 DBG] [IndustrialHMI] [LIN-PC] [32208] TestModulePresenter创建完成
[2025-08-13 18:08:38.789 DBG] [IndustrialHMI] [LIN-PC] [32208] 创建TestModulePresenter成功
[2025-08-13 18:08:38.790 DBG] [IndustrialHMI] [LIN-PC] [32208] 事件订阅完成
[2025-08-13 18:08:38.790 INF] [IndustrialHMI] [LIN-PC] [32208] 模块初始化完成: 测试模块
[2025-08-13 18:08:38.790 INF] [IndustrialHMI] [LIN-PC] [32208] 开始启动模块: 测试模块
[2025-08-13 18:08:38.790 INF] [IndustrialHMI] [LIN-PC] [32208] 启动TestModulePresenter
[2025-08-13 18:08:38.797 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 模型启动完成
[2025-08-13 18:08:38.799 DBG] [IndustrialHMI] [LIN-PC] [32208] 系统事件订阅完成
[2025-08-13 18:08:38.799 INF] [IndustrialHMI] [LIN-PC] [32208] TestModulePresenter启动完成
[2025-08-13 18:08:38.799 INF] [IndustrialHMI] [LIN-PC] [32208] 模块启动完成: 测试模块
[2025-08-13 18:08:38.800 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理系统事件: ModuleStarted
[2025-08-13 18:08:38.800 INF] [IndustrialHMI] [LIN-PC] [32208] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 18:08:38.800 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 18:08:38.801 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:38.801 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 18:08:38.802 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理模块加载事件: 测试模块
[2025-08-13 18:08:38.802 INF] [IndustrialHMI] [LIN-PC] [32208] 模块加载完成，共加载 5 个模块
[2025-08-13 18:08:38.802 INF] [IndustrialHMI] [LIN-PC] [32208] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 18:08:38.803 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 报警管理 添加了UI标签页
[2025-08-13 18:08:38.803 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 通信测试 添加了UI标签页
[2025-08-13 18:08:38.803 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 设备监控 添加了UI标签页
[2025-08-13 18:08:38.804 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 18:08:38.804 DBG] [IndustrialHMI] [LIN-PC] [32208] 为模块 测试模块 添加了UI标签页
[2025-08-13 18:08:38.804 INF] [IndustrialHMI] [LIN-PC] [32208] 步骤5: 初始化主窗体
[2025-08-13 18:08:38.805 DBG] [IndustrialHMI] [LIN-PC] [32208] 主窗体初始化完成
[2025-08-13 18:08:38.805 INF] [IndustrialHMI] [LIN-PC] [32208] 应用程序初始化完成
[2025-08-13 18:08:38.805 INF] [IndustrialHMI] [LIN-PC] [32208] 应用程序初始化成功，启动主窗体
[2025-08-13 18:08:38.805 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到系统启动事件
[2025-08-13 18:08:38.806 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:38.806 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 18:08:38.806 INF] [IndustrialHMI] [LIN-PC] [32208] 模块 测试模块 收到系统启动事件
[2025-08-13 18:08:38.807 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到系统启动事件
[2025-08-13 18:08:38.939 DBG] [IndustrialHMI] [LIN-PC] [32208] 主窗体事件订阅完成
[2025-08-13 18:08:38.946 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据变化事件处理完成
[2025-08-13 18:08:38.947 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 18:08:38.948 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 定时状态更新 - 18:08:38
[2025-08-13 18:08:39.816 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 系统启动后初始化完成
[2025-08-13 18:08:39.929 INF] [IndustrialHMI] [LIN-PC] [32208] 用户请求关闭应用程序
[2025-08-13 18:08:39.930 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:08:39.930 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:39.930 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 18:08:39.931 INF] [IndustrialHMI] [LIN-PC] [32208] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:08:39.931 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到系统关闭事件
[2025-08-13 18:08:39.932 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据变化事件处理完成
[2025-08-13 18:08:39.932 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 系统关闭前清理完成
[2025-08-13 18:08:39.932 INF] [IndustrialHMI] [LIN-PC] [32208] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:08:39.933 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载所有模块
[2025-08-13 18:08:39.933 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载模块: 报警管理
[2025-08-13 18:08:39.933 INF] [IndustrialHMI] [LIN-PC] [32208] 停止报警管理模块
[2025-08-13 18:08:39.933 INF] [IndustrialHMI] [LIN-PC] [32208] 停止报警监控
[2025-08-13 18:08:39.933 INF] [IndustrialHMI] [LIN-PC] [32208] 停止报警监控
[2025-08-13 18:08:39.933 INF] [IndustrialHMI] [LIN-PC] [32208] 报警管理模块停止完成
[2025-08-13 18:08:39.934 INF] [IndustrialHMI] [LIN-PC] [32208] 开始释放报警管理模块资源
[2025-08-13 18:08:39.934 INF] [IndustrialHMI] [LIN-PC] [32208] 停止报警监控
[2025-08-13 18:08:39.934 INF] [IndustrialHMI] [LIN-PC] [32208] 停止报警监控
[2025-08-13 18:08:39.935 DBG] [IndustrialHMI] [LIN-PC] [32208] AlarmPresenter 资源释放完成
[2025-08-13 18:08:39.935 INF] [IndustrialHMI] [LIN-PC] [32208] 停止报警监控
[2025-08-13 18:08:39.935 DBG] [IndustrialHMI] [LIN-PC] [32208] AlarmModel 资源释放完成
[2025-08-13 18:08:39.940 INF] [IndustrialHMI] [LIN-PC] [32208] 报警管理模块资源释放完成
[2025-08-13 18:08:39.940 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 18:08:39.941 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:39.941 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:08:39.941 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理模块卸载事件: 报警管理
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] 模块卸载成功: 报警管理
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载模块: 通信测试
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] 停止 CommunicationTestModule
[2025-08-13 18:08:39.942 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据变化: EventMonitoring
[2025-08-13 18:08:39.942 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] 测试已停止
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModule 停止完成
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] 开始释放 CommunicationTestModule 资源
[2025-08-13 18:08:39.942 INF] [IndustrialHMI] [LIN-PC] [32208] 停止 CommunicationTestModule
[2025-08-13 18:08:39.943 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据变化: EventMonitoring
[2025-08-13 18:08:39.943 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据变化: PerformanceMonitoring
[2025-08-13 18:08:39.943 INF] [IndustrialHMI] [LIN-PC] [32208] 测试已停止
[2025-08-13 18:08:39.943 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModule 停止完成
[2025-08-13 18:08:39.943 DBG] [IndustrialHMI] [LIN-PC] [32208] 系统事件订阅已取消
[2025-08-13 18:08:39.943 DBG] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestPresenter 资源释放完成
[2025-08-13 18:08:39.944 DBG] [IndustrialHMI] [LIN-PC] [32208] EventMonitor 资源释放完成
[2025-08-13 18:08:39.944 INF] [IndustrialHMI] [LIN-PC] [32208] 测试已停止
[2025-08-13 18:08:39.944 DBG] [IndustrialHMI] [LIN-PC] [32208] TestCaseManager 资源释放完成
[2025-08-13 18:08:39.944 DBG] [IndustrialHMI] [LIN-PC] [32208] PerformanceMonitor 资源释放完成
[2025-08-13 18:08:39.944 DBG] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModel 资源释放完成
[2025-08-13 18:08:39.944 INF] [IndustrialHMI] [LIN-PC] [32208] CommunicationTestModule 资源释放完成
[2025-08-13 18:08:39.944 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 18:08:39.945 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:39.945 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:08:39.945 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理模块卸载事件: 通信测试
[2025-08-13 18:08:39.945 INF] [IndustrialHMI] [LIN-PC] [32208] 模块卸载成功: 通信测试
[2025-08-13 18:08:39.945 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载模块: 设备监控
[2025-08-13 18:08:39.945 INF] [IndustrialHMI] [LIN-PC] [32208] 停止设备监控模块
[2025-08-13 18:08:39.946 INF] [IndustrialHMI] [LIN-PC] [32208] 用户请求停止设备监控
[2025-08-13 18:08:39.946 INF] [IndustrialHMI] [LIN-PC] [32208] 停止设备监控
[2025-08-13 18:08:39.946 INF] [IndustrialHMI] [LIN-PC] [32208] 设备监控已停止
[2025-08-13 18:08:39.946 INF] [IndustrialHMI] [LIN-PC] [32208] 设备监控模块停止完成
[2025-08-13 18:08:39.946 INF] [IndustrialHMI] [LIN-PC] [32208] 开始释放设备监控模块资源
[2025-08-13 18:08:39.947 DBG] [IndustrialHMI] [LIN-PC] [32208] DevicePresenter 资源释放完成
[2025-08-13 18:08:39.947 INF] [IndustrialHMI] [LIN-PC] [32208] 停止设备监控
[2025-08-13 18:08:39.947 DBG] [IndustrialHMI] [LIN-PC] [32208] DeviceModel 资源释放完成
[2025-08-13 18:08:39.947 INF] [IndustrialHMI] [LIN-PC] [32208] 设备监控模块资源释放完成
[2025-08-13 18:08:39.947 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 18:08:39.947 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型数据已更新，视图已刷新
[2025-08-13 18:08:39.948 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 18:08:39.948 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理模块卸载事件: 设备监控
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 模块卸载成功: 设备监控
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载模块: 测试框架模块
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 停止测试框架模块
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 停止内存泄漏测试套件
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 内存泄漏测试套件已停止
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 停止性能测试套件
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 性能测试套件已停止
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 停止集成测试套件
[2025-08-13 18:08:39.948 INF] [IndustrialHMI] [LIN-PC] [32208] 集成测试套件已停止
[2025-08-13 18:08:39.949 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 模型已停止
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块停止完成
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 开始释放测试框架模块资源
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 停止内存泄漏测试套件
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 内存泄漏测试套件已停止
[2025-08-13 18:08:39.949 DBG] [IndustrialHMI] [LIN-PC] [32208] MemoryLeakTestSuite资源释放完成
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 停止性能测试套件
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 性能测试套件已停止
[2025-08-13 18:08:39.949 DBG] [IndustrialHMI] [LIN-PC] [32208] PerformanceTestSuite资源释放完成
[2025-08-13 18:08:39.949 INF] [IndustrialHMI] [LIN-PC] [32208] 停止集成测试套件
[2025-08-13 18:08:39.950 INF] [IndustrialHMI] [LIN-PC] [32208] 集成测试套件已停止
[2025-08-13 18:08:39.950 DBG] [IndustrialHMI] [LIN-PC] [32208] IntegrationTestSuite资源释放完成
[2025-08-13 18:08:39.950 DBG] [IndustrialHMI] [LIN-PC] [32208] TestFrameworkPresenter资源释放完成
[2025-08-13 18:08:39.950 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块资源释放完成
[2025-08-13 18:08:39.951 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 18:08:39.951 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理模块卸载事件: 测试框架模块
[2025-08-13 18:08:39.951 INF] [IndustrialHMI] [LIN-PC] [32208] 模块卸载成功: 测试框架模块
[2025-08-13 18:08:39.951 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载模块: 测试模块
[2025-08-13 18:08:39.951 INF] [IndustrialHMI] [LIN-PC] [32208] 开始停止模块: 测试模块
[2025-08-13 18:08:39.951 INF] [IndustrialHMI] [LIN-PC] [32208] 停止TestModulePresenter
[2025-08-13 18:08:39.951 DBG] [IndustrialHMI] [LIN-PC] [32208] 系统事件取消订阅完成
[2025-08-13 18:08:39.952 DBG] [IndustrialHMI] [LIN-PC] [32208] 模型状态变化: 模型停止完成
[2025-08-13 18:08:39.952 INF] [IndustrialHMI] [LIN-PC] [32208] TestModulePresenter停止完成
[2025-08-13 18:08:39.952 DBG] [IndustrialHMI] [LIN-PC] [32208] 事件取消订阅完成
[2025-08-13 18:08:39.952 INF] [IndustrialHMI] [LIN-PC] [32208] 模块停止完成: 测试模块
[2025-08-13 18:08:39.953 DBG] [IndustrialHMI] [LIN-PC] [32208] 处理系统事件: ModuleStopped
[2025-08-13 18:08:39.953 INF] [IndustrialHMI] [LIN-PC] [32208] 开始释放模块资源: 测试模块
[2025-08-13 18:08:39.953 INF] [IndustrialHMI] [LIN-PC] [32208] 释放TestModulePresenter资源
[2025-08-13 18:08:39.953 INF] [IndustrialHMI] [LIN-PC] [32208] TestModulePresenter资源释放完成
[2025-08-13 18:08:39.958 INF] [IndustrialHMI] [LIN-PC] [32208] 模块资源释放完成: 测试模块
[2025-08-13 18:08:39.958 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 18:08:39.961 ERR] [IndustrialHMI] [LIN-PC] [32208] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 18:08:39.966 INF] [IndustrialHMI] [LIN-PC] [32208] 模块卸载成功: 测试模块
[2025-08-13 18:08:39.966 INF] [IndustrialHMI] [LIN-PC] [32208] 所有模块卸载完成
[2025-08-13 18:08:39.966 INF] [IndustrialHMI] [LIN-PC] [32208] 应用程序关闭流程完成
[2025-08-13 18:08:39.967 INF] [IndustrialHMI] [LIN-PC] [32208] 主窗体已关闭，资源清理完成
[2025-08-13 18:08:39.989 INF] [IndustrialHMI] [LIN-PC] [32208] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 18:08:39.991 INF] [IndustrialHMI] [LIN-PC] [32208] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 18:08:39.991 INF] [IndustrialHMI] [LIN-PC] [32208] 收到系统关闭事件，原因: UserRequest
[2025-08-13 18:08:39.992 INF] [IndustrialHMI] [LIN-PC] [32208] 开始释放应用程序资源
[2025-08-13 18:08:39.992 INF] [IndustrialHMI] [LIN-PC] [32208] 开始卸载所有模块
[2025-08-13 18:08:39.992 INF] [IndustrialHMI] [LIN-PC] [32208] 所有模块卸载完成
[2025-08-13 18:08:39.992 INF] [IndustrialHMI] [LIN-PC] [32208] 应用程序资源释放完成
[2025-08-13 18:08:39.992 INF] [IndustrialHMI] [LIN-PC] [32208] === 应用程序正常退出 ===
