using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Shell.Bootstrapper;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using Services;
using Shell;

namespace ContainerTests
{
    /// <summary>
    /// ContainerBootstrapper测试类
    /// </summary>
    [TestClass]
    public class ContainerBootstrapperTests
    {
        private SimpleContainer _container;

        /// <summary>
        /// 测试清理
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            _container?.Dispose();
        }

        /// <summary>
        /// 测试创建容器
        /// </summary>
        [TestMethod]
        public void CreateContainer_ShouldReturnValidContainer()
        {
            // Act
            _container = ContainerBootstrapper.CreateContainer();

            // Assert
            Assert.IsNotNull(_container);
        }

        /// <summary>
        /// 测试容器中的核心服务注册
        /// </summary>
        [TestMethod]
        public void CreateContainer_ShouldRegisterCoreServices()
        {
            // Act
            _container = ContainerBootstrapper.CreateContainer();

            // Assert
            var logger = _container.Resolve<ILogger>();
            var eventAggregator = _container.Resolve<IEventAggregator>();
            var configService = _container.Resolve<IConfigurationService>();
            var moduleLoader = _container.Resolve<ModuleLoader>();
            var mainForm = _container.Resolve<MainForm>();

            Assert.IsNotNull(logger);
            Assert.IsNotNull(eventAggregator);
            Assert.IsNotNull(configService);
            Assert.IsNotNull(moduleLoader);
            Assert.IsNotNull(mainForm);
        }

        /// <summary>
        /// 测试EventAggregator单例
        /// </summary>
        [TestMethod]
        public void CreateContainer_EventAggregator_ShouldBeSingleton()
        {
            // Act
            _container = ContainerBootstrapper.CreateContainer();
            var eventAggregator1 = _container.Resolve<IEventAggregator>();
            var eventAggregator2 = _container.Resolve<IEventAggregator>();

            // Assert
            Assert.IsNotNull(eventAggregator1);
            Assert.IsNotNull(eventAggregator2);
            Assert.AreSame(eventAggregator1, eventAggregator2);
        }

        /// <summary>
        /// 测试Logger单例
        /// </summary>
        [TestMethod]
        public void CreateContainer_Logger_ShouldBeSingleton()
        {
            // Act
            _container = ContainerBootstrapper.CreateContainer();
            var logger1 = _container.Resolve<ILogger>();
            var logger2 = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(logger1);
            Assert.IsNotNull(logger2);
            Assert.AreSame(logger1, logger2);
        }

        /// <summary>
        /// 测试使用自定义Logger创建容器
        /// </summary>
        [TestMethod]
        public void CreateContainer_WithCustomLogger_ShouldUseCustomLogger()
        {
            // Arrange
            var customLogger = new SerilogLogger();

            // Act
            _container = ContainerBootstrapper.CreateContainer(customLogger);
            var resolvedLogger = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(resolvedLogger);
            Assert.AreSame(customLogger, resolvedLogger);
        }

        /// <summary>
        /// 测试注册额外服务
        /// </summary>
        [TestMethod]
        public void RegisterService_ShouldRegisterCorrectly()
        {
            // Arrange
            _container = ContainerBootstrapper.CreateContainer();

            // Act
            ContainerBootstrapper.RegisterService(_container, typeof(ILogger), typeof(SerilogLogger), Reuse.Transient);
            var logger = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(logger);
            Assert.IsInstanceOfType(logger, typeof(SerilogLogger));
        }

        /// <summary>
        /// 测试注册服务实例
        /// </summary>
        [TestMethod]
        public void RegisterInstance_ShouldRegisterCorrectly()
        {
            // Arrange
            _container = ContainerBootstrapper.CreateContainer();
            var customLogger = new SerilogLogger();

            // Act
            ContainerBootstrapper.RegisterInstance(_container, customLogger, Reuse.Singleton);
            var resolvedLogger = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(resolvedLogger);
            Assert.AreSame(customLogger, resolvedLogger);
        }

        /// <summary>
        /// 测试容器验证
        /// </summary>
        [TestMethod]
        public void CreateContainer_ShouldPassValidation()
        {
            // Act & Assert - 如果验证失败会抛出异常
            _container = ContainerBootstrapper.CreateContainer();

            // 验证所有核心服务都可以解析
            Assert.IsNotNull(_container.Resolve<ILogger>());
            Assert.IsNotNull(_container.Resolve<IEventAggregator>());
            Assert.IsNotNull(_container.Resolve<IConfigurationService>());
            Assert.IsNotNull(_container.Resolve<ModuleLoader>());
            Assert.IsNotNull(_container.Resolve<MainForm>());
        }
    }
}
