﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Serilog</id>
    <version>3.1.1</version>
    <authors>Serilog Contributors</authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>icon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://serilog.net/</projectUrl>
    <description>Simple .NET logging with fully-structured events</description>
    <copyright>Copyright © 2013-23 Serilog Contributors</copyright>
    <tags>serilog logging semantic structured</tags>
    <repository type="git" url="https://github.com/serilog/serilog.git" commit="999d686d1830edde15ccb1d94c7bff313ec7d7a0" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="System.Diagnostics.DiagnosticSource" version="7.0.2" exclude="Build,Analyzers" />
        <dependency id="System.ValueTuple" version="4.5.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETFramework4.7.1">
        <dependency id="System.Diagnostics.DiagnosticSource" version="7.0.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net5.0" />
      <group targetFramework="net6.0" />
      <group targetFramework="net7.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Diagnostics.DiagnosticSource" version="7.0.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Diagnostics.DiagnosticSource" version="7.0.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="Microsoft.CSharp" targetFramework=".NETFramework4.6.2, .NETFramework4.7.1" />
      <frameworkAssembly assemblyName="System.Core" targetFramework=".NETFramework4.6.2, .NETFramework4.7.1" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.6.2, .NETFramework4.7.1" />
    </frameworkAssemblies>
  </metadata>
</package>