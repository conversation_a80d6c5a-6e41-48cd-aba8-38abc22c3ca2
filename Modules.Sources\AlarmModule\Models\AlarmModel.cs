using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Contracts;
using Contracts.Events;
using Contracts.Services;

namespace AlarmModule.Models
{
    /// <summary>
    /// 报警模块数据模型
    /// </summary>
    /// <remarks>
    /// 管理报警数据、规则和业务逻辑，处理报警相关的事件
    /// </remarks>
    public class AlarmModel : IDisposable
    {
        private readonly IAlarmService _alarmService;
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private List<AlarmViewModel> _activeAlarms;
        private List<AlarmViewModel> _alarmHistory;
        private AlarmStatistics _statistics;
        private bool _disposed = false;

        /// <summary>
        /// 活动报警列表变化事件
        /// </summary>
        public event EventHandler<AlarmListChangedEventArgs> ActiveAlarmsChanged;

        /// <summary>
        /// 历史报警列表变化事件
        /// </summary>
        public event EventHandler<AlarmListChangedEventArgs> AlarmHistoryChanged;

        /// <summary>
        /// 报警统计信息变化事件
        /// </summary>
        public event EventHandler<AlarmStatisticsChangedEventArgs> StatisticsChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmService">报警服务</param>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public AlarmModel(IAlarmService alarmService, IEventAggregator eventAggregator, ILogger logger)
        {
            _alarmService = alarmService ?? throw new ArgumentNullException(nameof(alarmService));
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _activeAlarms = new List<AlarmViewModel>();
            _alarmHistory = new List<AlarmViewModel>();
            
            // 订阅系统事件
            SubscribeToEvents();
            
            _logger.Debug("AlarmModel 初始化完成");
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            _eventAggregator.GetEvent<SystemStartupEvent>()
                .Subscribe(OnSystemStartup, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<DeviceDataUpdateEvent>()
                .Subscribe(OnDeviceDataUpdate, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<DeviceConnectionEvent>()
                .Subscribe(OnDeviceConnectionChanged, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<AlarmEvent>()
                .Subscribe(OnAlarmEvent, ThreadOption.UIThread, false);
        }

        /// <summary>
        /// 系统启动事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemStartup(SystemStartupEvent eventData)
        {
            _logger.Info("收到系统启动事件，开始加载报警数据");
            LoadAlarmData();
            StartMonitoring();
        }

        /// <summary>
        /// 设备数据更新事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceDataUpdate(DeviceDataUpdateEvent eventData)
        {
            var dataInfo = eventData.DataInfo;
            _logger.Debug($"收到设备数据更新: {dataInfo.DeviceId}.{dataInfo.DataPointName} = {dataInfo.Value}");

            // 评估报警规则
            _alarmService.EvaluateRules(dataInfo.DeviceId, dataInfo.DataPointName, dataInfo.Value);
            
            // 刷新报警数据
            RefreshActiveAlarms();
        }

        /// <summary>
        /// 设备连接状态变化事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceConnectionChanged(DeviceConnectionEvent eventData)
        {
            var status = eventData.Status;
            _logger.Debug($"收到设备连接状态变化: {status.DeviceId} - {(status.IsConnected ? "已连接" : "已断开")}");
            
            // 评估连接状态报警规则
            _alarmService.EvaluateRules(status.DeviceId, "ConnectionStatus", status.IsConnected);
            
            // 刷新报警数据
            RefreshActiveAlarms();
        }

        /// <summary>
        /// 报警事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnAlarmEvent(AlarmEvent eventData)
        {
            _logger.Info($"收到报警事件: {eventData.AlarmInfo.AlarmId} - {eventData.AlarmInfo.Description}");
            
            // 刷新报警数据
            RefreshActiveAlarms();
            RefreshStatistics();
        }

        /// <summary>
        /// 加载报警数据
        /// </summary>
        public void LoadAlarmData()
        {
            try
            {
                _logger.Info("开始加载报警数据");
                
                RefreshActiveAlarms();
                RefreshAlarmHistory();
                RefreshStatistics();
                
                _logger.Info("报警数据加载完成");
            }
            catch (Exception ex)
            {
                _logger.Error("加载报警数据时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 刷新活动报警列表
        /// </summary>
        public void RefreshActiveAlarms()
        {
            try
            {
                var alarms = _alarmService.GetActiveAlarms();
                var viewModels = alarms.Select(alarm => new AlarmViewModel
                {
                    Id = alarm.AlarmId,
                    DeviceId = alarm.Source ?? "Unknown",
                    AlarmType = alarm.AlarmName,
                    Level = alarm.Level,
                    Message = alarm.Description,
                    Timestamp = alarm.OccurredTime,
                    Status = alarm.Status,
                    AcknowledgedBy = alarm.AcknowledgedBy,
                    AcknowledgedTime = alarm.AcknowledgedTime
                }).ToList();

                _activeAlarms.Clear();
                _activeAlarms.AddRange(viewModels);
                
                // 触发事件
                ActiveAlarmsChanged?.Invoke(this, new AlarmListChangedEventArgs(_activeAlarms));
                
                _logger.Debug($"活动报警列表已刷新，共 {_activeAlarms.Count} 个报警");
            }
            catch (Exception ex)
            {
                _logger.Error("刷新活动报警列表时发生错误", ex);
            }
        }

        /// <summary>
        /// 刷新历史报警列表
        /// </summary>
        public void RefreshAlarmHistory(DateTime? startTime = null, DateTime? endTime = null)
        {
            try
            {
                var alarms = _alarmService.GetAlarmHistory(startTime, endTime);
                var viewModels = alarms.Select(alarm => new AlarmViewModel
                {
                    Id = alarm.AlarmId,
                    DeviceId = alarm.Source ?? "Unknown",
                    AlarmType = alarm.AlarmName,
                    Level = alarm.Level,
                    Message = alarm.Description,
                    Timestamp = alarm.OccurredTime,
                    Status = alarm.Status,
                    AcknowledgedBy = alarm.AcknowledgedBy,
                    AcknowledgedTime = alarm.AcknowledgedTime,
                    ClearedTime = alarm.RecoveredTime
                }).ToList();

                _alarmHistory.Clear();
                _alarmHistory.AddRange(viewModels);
                
                // 触发事件
                AlarmHistoryChanged?.Invoke(this, new AlarmListChangedEventArgs(_alarmHistory));
                
                _logger.Debug($"历史报警列表已刷新，共 {_alarmHistory.Count} 个报警");
            }
            catch (Exception ex)
            {
                _logger.Error("刷新历史报警列表时发生错误", ex);
            }
        }

        /// <summary>
        /// 刷新统计信息
        /// </summary>
        public void RefreshStatistics()
        {
            try
            {
                _statistics = _alarmService.GetAlarmStatistics();
                
                // 触发事件
                StatisticsChanged?.Invoke(this, new AlarmStatisticsChangedEventArgs(_statistics));
                
                _logger.Debug("报警统计信息已刷新");
            }
            catch (Exception ex)
            {
                _logger.Error("刷新报警统计信息时发生错误", ex);
            }
        }

        /// <summary>
        /// 确认报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="acknowledgedBy">确认人</param>
        public void AcknowledgeAlarm(string alarmId, string acknowledgedBy = "User")
        {
            try
            {
                _logger.Info($"确认报警: {alarmId}");
                
                var success = _alarmService.AcknowledgeAlarm(alarmId, acknowledgedBy);
                if (success)
                {
                    // 发布报警确认事件
                    var alarm = _activeAlarms.FirstOrDefault(a => a.Id == alarmId);
                    if (alarm != null)
                    {
                        var acknowledgedEvent = new AlarmAcknowledgedEvent(alarm.Id, acknowledgedBy);
                        _eventAggregator.GetEvent<AlarmAcknowledgedEvent>()
                            .Publish(acknowledgedEvent);
                    }
                    
                    RefreshActiveAlarms();
                    RefreshStatistics();
                    
                    _logger.Info($"报警确认成功: {alarmId}");
                }
                else
                {
                    _logger.Warning($"报警确认失败: {alarmId}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"确认报警时发生错误: {alarmId}", ex);
            }
        }

        /// <summary>
        /// 确认所有报警
        /// </summary>
        /// <param name="acknowledgedBy">确认人</param>
        public void AcknowledgeAllAlarms(string acknowledgedBy = "User")
        {
            try
            {
                _logger.Info("确认所有报警");
                
                var count = _alarmService.AcknowledgeAllAlarms(acknowledgedBy);
                
                RefreshActiveAlarms();
                RefreshStatistics();
                
                _logger.Info($"批量确认报警完成，共确认 {count} 个报警");
            }
            catch (Exception ex)
            {
                _logger.Error("确认所有报警时发生错误", ex);
            }
        }

        /// <summary>
        /// 清除报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        public void ClearAlarm(string alarmId)
        {
            try
            {
                _logger.Info($"清除报警: {alarmId}");
                
                var success = _alarmService.ClearAlarm(alarmId);
                if (success)
                {
                    // 发布报警清除事件
                    var clearedEvent = new AlarmClearedEvent(alarmId);
                    _eventAggregator.GetEvent<AlarmClearedEvent>()
                        .Publish(clearedEvent);
                    
                    RefreshActiveAlarms();
                    RefreshAlarmHistory();
                    RefreshStatistics();
                    
                    _logger.Info($"报警清除成功: {alarmId}");
                }
                else
                {
                    _logger.Warning($"报警清除失败: {alarmId}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"清除报警时发生错误: {alarmId}", ex);
            }
        }

        /// <summary>
        /// 清除已确认的报警
        /// </summary>
        public void ClearAcknowledgedAlarms()
        {
            try
            {
                _logger.Info("清除已确认的报警");
                
                var count = _alarmService.ClearAcknowledgedAlarms();
                
                RefreshActiveAlarms();
                RefreshAlarmHistory();
                RefreshStatistics();
                
                _logger.Info($"批量清除报警完成，共清除 {count} 个报警");
            }
            catch (Exception ex)
            {
                _logger.Error("清除已确认报警时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取活动报警列表
        /// </summary>
        /// <returns>活动报警视图模型列表</returns>
        public List<AlarmViewModel> GetActiveAlarms()
        {
            return new List<AlarmViewModel>(_activeAlarms);
        }

        /// <summary>
        /// 获取历史报警列表
        /// </summary>
        /// <returns>历史报警视图模型列表</returns>
        public List<AlarmViewModel> GetAlarmHistory()
        {
            return new List<AlarmViewModel>(_alarmHistory);
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>报警统计信息</returns>
        public AlarmStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            try
            {
                _logger.Info("开始报警监控");
                _alarmService.StartMonitoring();
            }
            catch (Exception ex)
            {
                _logger.Error("启动报警监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            try
            {
                _logger.Info("停止报警监控");
                _alarmService.StopMonitoring();
            }
            catch (Exception ex)
            {
                _logger.Error("停止报警监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 取消事件订阅
                _eventAggregator?.GetEvent<SystemStartupEvent>()
                    .Unsubscribe(OnSystemStartup);
                    
                _eventAggregator?.GetEvent<DeviceDataUpdateEvent>()
                    .Unsubscribe(OnDeviceDataUpdate);
                    
                _eventAggregator?.GetEvent<DeviceConnectionEvent>()
                    .Unsubscribe(OnDeviceConnectionChanged);
                    
                _eventAggregator?.GetEvent<AlarmEvent>()
                    .Unsubscribe(OnAlarmEvent);

                // 停止监控
                StopMonitoring();

                _logger?.Debug("AlarmModel 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 AlarmModel 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 报警视图模型
    /// </summary>
    public class AlarmViewModel : INotifyPropertyChanged
    {
        private AlarmStatus _status;

        /// <summary>
        /// 报警ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 报警类型
        /// </summary>
        public string AlarmType { get; set; }

        /// <summary>
        /// 报警级别
        /// </summary>
        public AlarmLevel Level { get; set; }

        /// <summary>
        /// 报警消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 报警时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 报警状态
        /// </summary>
        public AlarmStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        /// <summary>
        /// 确认人
        /// </summary>
        public string AcknowledgedBy { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? AcknowledgedTime { get; set; }

        /// <summary>
        /// 清除时间
        /// </summary>
        public DateTime? ClearedTime { get; set; }

        /// <summary>
        /// 属性变化事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性变化事件
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 报警列表变化事件参数
    /// </summary>
    public class AlarmListChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 报警列表
        /// </summary>
        public List<AlarmViewModel> Alarms { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarms">报警列表</param>
        public AlarmListChangedEventArgs(List<AlarmViewModel> alarms)
        {
            Alarms = alarms;
        }
    }

    /// <summary>
    /// 报警统计信息变化事件参数
    /// </summary>
    public class AlarmStatisticsChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 统计信息
        /// </summary>
        public AlarmStatistics Statistics { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statistics">统计信息</param>
        public AlarmStatisticsChangedEventArgs(AlarmStatistics statistics)
        {
            Statistics = statistics;
        }
    }
}
