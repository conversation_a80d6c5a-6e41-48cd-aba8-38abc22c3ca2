using System;
using System.Windows.Forms;
using DeviceModule.Tests.TestFramework;
using static DeviceModule.Tests.TestFramework.SimpleTestFramework;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using Services;
using DeviceModule;

namespace DeviceModule.Tests
{
    /// <summary>
    /// DeviceModuleMain测试类
    /// </summary>
    /// <remarks>
    /// 测试设备模块主类的所有功能
    /// </remarks>
    [TestClass("DeviceModuleMain测试")]
    public class DeviceModuleMainTests
    {
        private DeviceModuleMain _deviceModule;
        private IEventAggregator _eventAggregator;
        private ILogger _logger;

        /// <summary>
        /// 测试初始化
        /// </summary>
        [TestInitialize]
        public void TestInitialize()
        {
            // 创建模拟的依赖项
            _eventAggregator = new EventAggregator();
            _logger = new SerilogLogger();
            
            // 创建设备模块实例
            _deviceModule = new DeviceModuleMain();
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            try
            {
                _deviceModule?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块创建
        /// </summary>
        [TestMethod("模块创建测试", "验证DeviceModuleMain能够正确创建")]
        public void DeviceModuleMain_ShouldBeCreated()
        {
            // Assert
            Assert.IsNotNull(_deviceModule, "设备模块应该能够创建");
            Assert.AreEqual("设备监控", _deviceModule.Name, "模块名称应该正确");
            Assert.AreEqual("设备数据监控和管理模块，提供实时设备状态监控和控制功能", _deviceModule.Description, "模块描述应该正确");
        }

        /// <summary>
        /// 测试依赖注入
        /// </summary>
        [TestMethod("依赖注入测试", "验证EventAggregator和Logger能够正确注入")]
        public void DependencyInjection_ShouldWork()
        {
            // Arrange & Act
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;

            // Assert
            Assert.IsNotNull(_deviceModule.EventAggregator, "EventAggregator应该被正确注入");
            Assert.IsNotNull(_deviceModule.Logger, "Logger应该被正确注入");
            Assert.AreSame(_eventAggregator, _deviceModule.EventAggregator, "EventAggregator应该是同一个实例");
            Assert.AreSame(_logger, _deviceModule.Logger, "Logger应该是同一个实例");
        }

        /// <summary>
        /// 测试模块初始化
        /// </summary>
        [TestMethod("模块初始化测试", "验证模块能够正确初始化")]
        public void Initialize_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModule.Initialize();
                Assert.IsTrue(true, "初始化应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"初始化不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块启动
        /// </summary>
        [TestMethod("模块启动测试", "验证模块能够正确启动")]
        public void Start_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;
            _deviceModule.Initialize();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModule.Start();
                Assert.IsTrue(true, "启动应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"启动不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块停止
        /// </summary>
        [TestMethod("模块停止测试", "验证模块能够正确停止")]
        public void Stop_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;
            _deviceModule.Initialize();
            _deviceModule.Start();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModule.Stop();
                Assert.IsTrue(true, "停止应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"停止不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试获取用户控件
        /// </summary>
        [TestMethod("获取用户控件测试", "验证能够获取有效的用户控件")]
        public void GetUserControl_ShouldReturnValidControl()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;
            _deviceModule.Initialize();

            // Act
            var userControl = _deviceModule.GetUserControl();

            // Assert
            Assert.IsNotNull(userControl, "应该返回有效的用户控件");
            Assert.IsTrue(userControl is UserControl, "返回的应该是UserControl类型");
        }

        /// <summary>
        /// 测试模块释放
        /// </summary>
        [TestMethod("模块释放测试", "验证模块能够正确释放资源")]
        public void Dispose_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;
            _deviceModule.Initialize();
            _deviceModule.Start();

            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceModule.Dispose();
                Assert.IsTrue(true, "释放应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"释放不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试无依赖注入的初始化
        /// </summary>
        [TestMethod("无依赖注入初始化测试", "验证没有依赖注入时的行为")]
        public void Initialize_WithoutDependencies_ShouldHandleGracefully()
        {
            // Act & Assert - 应该能够处理没有依赖注入的情况
            try
            {
                _deviceModule.Initialize();
                Assert.IsTrue(true, "没有依赖注入时初始化应该能够处理");
            }
            catch (Exception ex)
            {
                // 如果抛出异常，应该是可预期的异常
                Assert.IsTrue(ex is NullReferenceException || ex is InvalidOperationException, 
                    $"应该抛出可预期的异常，实际异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试重复初始化
        /// </summary>
        [TestMethod("重复初始化测试", "验证重复初始化的行为")]
        public void Initialize_Multiple_ShouldHandleGracefully()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;

            // Act & Assert - 重复初始化应该能够处理
            try
            {
                _deviceModule.Initialize();
                _deviceModule.Initialize(); // 第二次初始化
                Assert.IsTrue(true, "重复初始化应该能够处理");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"重复初始化不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试模块生命周期
        /// </summary>
        [TestMethod("模块生命周期测试", "验证完整的模块生命周期")]
        public void ModuleLifecycle_ShouldWork()
        {
            // Arrange
            _deviceModule.EventAggregator = _eventAggregator;
            _deviceModule.Logger = _logger;

            // Act & Assert - 完整的生命周期应该正常工作
            try
            {
                // 初始化
                _deviceModule.Initialize();
                
                // 启动
                _deviceModule.Start();
                
                // 获取用户控件
                var control = _deviceModule.GetUserControl();
                Assert.IsNotNull(control, "生命周期中应该能够获取用户控件");
                
                // 停止
                _deviceModule.Stop();
                
                // 释放
                _deviceModule.Dispose();
                
                Assert.IsTrue(true, "完整的模块生命周期应该正常工作");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"模块生命周期不应该抛出异常: {ex.Message}");
            }
        }
    }
}
