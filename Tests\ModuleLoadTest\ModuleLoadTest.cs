using System;
using System.IO;
using System.Linq;
using System.Reflection;

namespace ModuleLoadTest
{
    /// <summary>
    /// 模块加载测试程序
    /// </summary>
    /// <remarks>
    /// 用于诊断模块加载问题
    /// </remarks>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("模块加载诊断测试");
            Console.WriteLine("================");
            Console.WriteLine();

            try
            {
                var modulesDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "Modules");
                Console.WriteLine($"模块目录: {modulesDir}");
                Console.WriteLine($"目录存在: {Directory.Exists(modulesDir)}");
                Console.WriteLine();

                if (!Directory.Exists(modulesDir))
                {
                    Console.WriteLine("❌ 模块目录不存在");
                    return;
                }

                var dllFiles = Directory.GetFiles(modulesDir, "*.dll");
                Console.WriteLine($"发现 {dllFiles.Length} 个DLL文件:");
                foreach (var dll in dllFiles)
                {
                    Console.WriteLine($"  - {Path.GetFileName(dll)}");
                }
                Console.WriteLine();

                foreach (var dllFile in dllFiles)
                {
                    Console.WriteLine($"=== 分析 {Path.GetFileName(dllFile)} ===");
                    AnalyzeAssembly(dllFile);
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void AnalyzeAssembly(string assemblyPath)
        {
            try
            {
                Console.WriteLine($"加载程序集: {assemblyPath}");
                var assembly = Assembly.LoadFrom(assemblyPath);
                Console.WriteLine($"✅ 程序集加载成功: {assembly.FullName}");

                var types = assembly.GetTypes();
                Console.WriteLine($"发现 {types.Length} 个类型");

                var moduleTypes = 0;
                var candidateTypes = 0;

                foreach (var type in types)
                {
                    if (type.IsInterface || type.IsAbstract)
                        continue;

                    candidateTypes++;
                    Console.WriteLine($"  类型: {type.Name}");

                    var interfaces = type.GetInterfaces();
                    Console.WriteLine($"    实现的接口: {string.Join(", ", interfaces.Select(i => i.Name))}");

                    // 检查是否实现IModule接口
                    var implementsIModule = interfaces.Any(i => i.Name == "IModule" || i.FullName == "Contracts.IModule");
                    if (implementsIModule)
                    {
                        moduleTypes++;
                        Console.WriteLine($"    ✅ 实现了IModule接口");

                        // 检查构造函数
                        var constructor = type.GetConstructor(Type.EmptyTypes);
                        if (constructor != null)
                        {
                            Console.WriteLine($"    ✅ 有无参构造函数");

                            // 检查必要的属性和方法
                            var nameProperty = type.GetProperty("Name");
                            var descriptionProperty = type.GetProperty("Description");
                            var initializeMethod = type.GetMethod("Initialize");
                            var startMethod = type.GetMethod("Start");

                            Console.WriteLine($"    Name属性: {(nameProperty != null ? "✅" : "❌")}");
                            Console.WriteLine($"    Description属性: {(descriptionProperty != null ? "✅" : "❌")}");
                            Console.WriteLine($"    Initialize方法: {(initializeMethod != null ? "✅" : "❌")}");
                            Console.WriteLine($"    Start方法: {(startMethod != null ? "✅" : "❌")}");

                            if (nameProperty != null && descriptionProperty != null && 
                                initializeMethod != null && startMethod != null)
                            {
                                Console.WriteLine($"    ✅ 模块接口完整");

                                // 尝试创建实例
                                try
                                {
                                    var instance = Activator.CreateInstance(type);
                                    Console.WriteLine($"    ✅ 实例创建成功");

                                    var name = nameProperty.GetValue(instance)?.ToString();
                                    var description = descriptionProperty.GetValue(instance)?.ToString();
                                    Console.WriteLine($"    模块名称: {name}");
                                    Console.WriteLine($"    模块描述: {description}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"    ❌ 实例创建失败: {ex.Message}");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"    ❌ 模块接口不完整");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"    ❌ 没有无参构造函数");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"    ❌ 未实现IModule接口");
                    }
                }

                Console.WriteLine($"候选类型: {candidateTypes}, 模块类型: {moduleTypes}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序集分析失败: {ex.Message}");
            }
        }
    }
}
