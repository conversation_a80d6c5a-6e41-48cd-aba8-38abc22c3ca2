using System;
using System.Windows.Forms;
using Contracts.Events;

namespace Contracts
{
    /// <summary>
    /// 模块接口，所有模块必须实现此接口
    /// </summary>
    /// <remarks>
    /// 模块是系统的基本功能单元，通过依赖注入获取所需服务，
    /// 通过事件聚合器与其他模块通信
    /// </remarks>
    public interface IModule : IDisposable
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        /// <remarks>用于在UI中显示和日志记录</remarks>
        string Name { get; }

        /// <summary>
        /// 模块描述
        /// </summary>
        /// <remarks>详细说明模块功能和用途</remarks>
        string Description { get; }

        /// <summary>
        /// 事件聚合器，用于模块间通信
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        ILogger Logger { get; set; }

        /// <summary>
        /// 模块初始化
        /// </summary>
        /// <remarks>
        /// 在此方法中进行：
        /// 1. 验证依赖注入是否成功
        /// 2. 订阅需要的事件
        /// 3. 初始化模块内部状态
        /// 4. 创建UI控件（如果需要）
        /// </remarks>
        void Initialize();

        /// <summary>
        /// 获取模块的UI控件
        /// </summary>
        /// <returns>模块的用户界面控件，如果模块没有UI则返回null</returns>
        /// <remarks>
        /// 返回的控件将被添加到主窗体的TabControl中
        /// </remarks>
        UserControl GetUserControl();

        /// <summary>
        /// 启动模块
        /// </summary>
        /// <remarks>
        /// 在Initialize之后调用，用于启动模块的业务逻辑
        /// </remarks>
        void Start();

        /// <summary>
        /// 停止模块
        /// </summary>
        /// <remarks>
        /// 停止模块的业务逻辑，但不释放资源
        /// </remarks>
        void Stop();
    }
}
