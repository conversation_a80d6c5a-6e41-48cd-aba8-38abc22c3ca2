using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Contracts.Events;
using Contracts.Services;

namespace DeviceModule.Services
{
    /// <summary>
    /// 模拟设备服务实现
    /// </summary>
    /// <remarks>
    /// 提供模拟的设备管理和数据访问功能，用于演示和测试
    /// </remarks>
    public class MockDeviceService : IDeviceService
    {
        private readonly List<DeviceInfo> _devices;
        private readonly Dictionary<string, Dictionary<string, object>> _deviceData;
        private readonly Random _random;
        private Timer _dataUpdateTimer;
        private bool _isMonitoring;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MockDeviceService()
        {
            _devices = new List<DeviceInfo>();
            _deviceData = new Dictionary<string, Dictionary<string, object>>();
            _random = new Random();
            
            // 初始化模拟设备
            InitializeMockDevices();
        }

        /// <summary>
        /// 初始化模拟设备
        /// </summary>
        private void InitializeMockDevices()
        {
            var devices = new[]
            {
                new DeviceInfo("PLC001", "主控PLC", "PLC", "************")
                {
                    Port = 502,
                    Description = "主要的可编程逻辑控制器",
                    CurrentValue = "运行中"
                },
                new DeviceInfo("SCADA001", "SCADA系统", "SCADA", "************")
                {
                    Port = 502,
                    Description = "数据采集与监控系统",
                    CurrentValue = "正常"
                },
                new DeviceInfo("SENSOR001", "温度传感器", "Sensor", "************")
                {
                    Port = 1024,
                    Description = "环境温度监测传感器",
                    CurrentValue = "25.6°C"
                },
                new DeviceInfo("MOTOR001", "主电机", "Motor", "************")
                {
                    Port = 2048,
                    Description = "生产线主驱动电机",
                    CurrentValue = "1450 RPM"
                },
                new DeviceInfo("VALVE001", "控制阀门", "Valve", "************")
                {
                    Port = 3072,
                    Description = "流量控制阀门",
                    CurrentValue = "75% 开度"
                }
            };

            foreach (var device in devices)
            {
                _devices.Add(device);
                InitializeDeviceData(device.Id);
            }
        }

        /// <summary>
        /// 初始化设备数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        private void InitializeDeviceData(string deviceId)
        {
            var data = new Dictionary<string, object>();
            
            switch (deviceId)
            {
                case "PLC001":
                    data["Status"] = "Running";
                    data["CpuUsage"] = 15.5;
                    data["MemoryUsage"] = 45.2;
                    data["IOCount"] = 128;
                    break;
                    
                case "SCADA001":
                    data["Status"] = "Online";
                    data["DataPoints"] = 256;
                    data["Alarms"] = 0;
                    data["Trends"] = 12;
                    break;
                    
                case "SENSOR001":
                    data["Temperature"] = 25.6;
                    data["Humidity"] = 65.3;
                    data["Pressure"] = 1013.25;
                    break;
                    
                case "MOTOR001":
                    data["Speed"] = 1450;
                    data["Current"] = 12.5;
                    data["Voltage"] = 380;
                    data["Power"] = 7.5;
                    break;
                    
                case "VALVE001":
                    data["Position"] = 75.0;
                    data["Flow"] = 125.8;
                    data["Pressure"] = 6.5;
                    break;
            }
            
            _deviceData[deviceId] = data;
        }

        public IList<DeviceInfo> GetDevices()
        {
            return _devices.ToList();
        }

        public DeviceInfo GetDevice(string deviceId)
        {
            return _devices.FirstOrDefault(d => d.Id == deviceId);
        }

        public bool ConnectDevice(string deviceId)
        {
            var device = GetDevice(deviceId);
            if (device == null) return false;

            // 模拟连接延迟
            Task.Run(async () =>
            {
                await Task.Delay(1000 + _random.Next(500, 2000));
                
                // 90% 成功率
                bool success = _random.NextDouble() > 0.1;
                device.IsConnected = success;
                device.LastUpdateTime = DateTime.Now;
                
                if (success)
                {
                    device.CurrentValue = GetDeviceStatusText(deviceId);
                }
            });

            return true;
        }

        public bool DisconnectDevice(string deviceId)
        {
            var device = GetDevice(deviceId);
            if (device == null) return false;

            device.IsConnected = false;
            device.CurrentValue = "已断开";
            device.LastUpdateTime = DateTime.Now;
            
            return true;
        }

        public bool ConnectAllDevices()
        {
            foreach (var device in _devices)
            {
                ConnectDevice(device.Id);
            }
            return true;
        }

        public bool DisconnectAllDevices()
        {
            foreach (var device in _devices)
            {
                DisconnectDevice(device.Id);
            }
            return true;
        }

        public bool IsDeviceConnected(string deviceId)
        {
            var device = GetDevice(deviceId);
            return device?.IsConnected ?? false;
        }

        public object ReadDeviceData(string deviceId, string dataPointName)
        {
            if (_deviceData.ContainsKey(deviceId) && _deviceData[deviceId].ContainsKey(dataPointName))
            {
                return _deviceData[deviceId][dataPointName];
            }
            return null;
        }

        public bool WriteDeviceData(string deviceId, string dataPointName, object value)
        {
            if (_deviceData.ContainsKey(deviceId))
            {
                _deviceData[deviceId][dataPointName] = value;
                return true;
            }
            return false;
        }

        public bool SendDeviceCommand(DeviceCommandInfo commandInfo)
        {
            // 模拟命令执行
            return _random.NextDouble() > 0.05; // 95% 成功率
        }

        public Dictionary<string, object> GetDeviceRealTimeData(string deviceId)
        {
            return _deviceData.ContainsKey(deviceId) ? 
                new Dictionary<string, object>(_deviceData[deviceId]) : 
                new Dictionary<string, object>();
        }

        public void StartMonitoring()
        {
            if (_isMonitoring) return;
            
            _isMonitoring = true;
            _dataUpdateTimer = new Timer(UpdateDeviceData, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
        }

        public void StopMonitoring()
        {
            _isMonitoring = false;
            _dataUpdateTimer?.Dispose();
            _dataUpdateTimer = null;
        }

        /// <summary>
        /// 更新设备数据（定时器回调）
        /// </summary>
        /// <param name="state">状态对象</param>
        private void UpdateDeviceData(object state)
        {
            foreach (var device in _devices.Where(d => d.IsConnected))
            {
                UpdateSingleDeviceData(device);
            }
        }

        /// <summary>
        /// 更新单个设备数据
        /// </summary>
        /// <param name="device">设备信息</param>
        private void UpdateSingleDeviceData(DeviceInfo device)
        {
            if (!_deviceData.ContainsKey(device.Id)) return;

            var data = _deviceData[device.Id];
            
            // 根据设备类型更新不同的数据
            switch (device.Id)
            {
                case "SENSOR001":
                    // 温度在20-30度之间波动
                    data["Temperature"] = 20 + _random.NextDouble() * 10;
                    data["Humidity"] = 60 + _random.NextDouble() * 20;
                    device.CurrentValue = $"{data["Temperature"]:F1}°C";
                    break;
                    
                case "MOTOR001":
                    // 转速在1400-1500之间波动
                    data["Speed"] = 1400 + _random.NextDouble() * 100;
                    data["Current"] = 10 + _random.NextDouble() * 5;
                    device.CurrentValue = $"{data["Speed"]:F0} RPM";
                    break;
                    
                case "VALVE001":
                    // 阀门开度在70-80%之间波动
                    data["Position"] = 70 + _random.NextDouble() * 10;
                    data["Flow"] = 100 + _random.NextDouble() * 50;
                    device.CurrentValue = $"{data["Position"]:F1}% 开度";
                    break;
                    
                case "PLC001":
                    data["CpuUsage"] = 10 + _random.NextDouble() * 20;
                    data["MemoryUsage"] = 40 + _random.NextDouble() * 20;
                    device.CurrentValue = "运行中";
                    break;
                    
                case "SCADA001":
                    data["Alarms"] = _random.Next(0, 3);
                    device.CurrentValue = "正常";
                    break;
            }
            
            device.LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取设备状态文本
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>状态文本</returns>
        private string GetDeviceStatusText(string deviceId)
        {
            switch (deviceId)
            {
                case "PLC001": return "运行中";
                case "SCADA001": return "正常";
                case "SENSOR001": return "25.6°C";
                case "MOTOR001": return "1450 RPM";
                case "VALVE001": return "75% 开度";
                default: return "已连接";
            }
        }

        public bool AddDevice(DeviceInfo deviceInfo)
        {
            if (_devices.Any(d => d.Id == deviceInfo.Id)) return false;
            
            _devices.Add(deviceInfo);
            InitializeDeviceData(deviceInfo.Id);
            return true;
        }

        public bool RemoveDevice(string deviceId)
        {
            var device = GetDevice(deviceId);
            if (device == null) return false;
            
            _devices.Remove(device);
            _deviceData.Remove(deviceId);
            return true;
        }

        public bool UpdateDevice(DeviceInfo deviceInfo)
        {
            var existingDevice = GetDevice(deviceInfo.Id);
            if (existingDevice == null) return false;
            
            existingDevice.Name = deviceInfo.Name;
            existingDevice.Type = deviceInfo.Type;
            existingDevice.Address = deviceInfo.Address;
            existingDevice.Port = deviceInfo.Port;
            existingDevice.Description = deviceInfo.Description;
            existingDevice.Configuration = deviceInfo.Configuration;
            
            return true;
        }
    }
}
