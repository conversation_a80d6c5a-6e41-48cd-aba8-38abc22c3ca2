using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Contracts;

namespace CommunicationTestModule.Services
{
    /// <summary>
    /// 性能监控器
    /// </summary>
    /// <remarks>
    /// 监控系统性能指标，包括CPU、内存、事件处理性能等
    /// </remarks>
    public class PerformanceMonitor : IDisposable
    {
        private readonly ILogger _logger;
        private readonly List<PerformanceSnapshot> _snapshots;
        private readonly Timer _monitorTimer;
        private readonly object _lockObject = new object();
        private readonly Process _currentProcess;
        private bool _isMonitoring = false;
        private bool _disposed = false;

        // 系统级性能监控
        private readonly Dictionary<string, PerformanceMetric> _systemMetrics;
        private readonly Stopwatch _applicationUptime;
        private DateTime _applicationStartTime;

        /// <summary>
        /// 性能数据更新事件
        /// </summary>
        public event EventHandler<PerformanceDataEventArgs> PerformanceDataUpdated;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public PerformanceMonitor(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _snapshots = new List<PerformanceSnapshot>();
            _currentProcess = Process.GetCurrentProcess();

            // 系统级性能监控初始化
            _systemMetrics = new Dictionary<string, PerformanceMetric>();
            _applicationUptime = Stopwatch.StartNew();
            _applicationStartTime = DateTime.Now;

            // 创建定时器，但不立即启动
            _monitorTimer = new Timer(CollectPerformanceData, null, Timeout.Infinite, Timeout.Infinite);

            _logger?.Info("性能监控器初始化完成");
        }

        /// <summary>
        /// 开始性能监控
        /// </summary>
        /// <param name="intervalMs">监控间隔（毫秒）</param>
        public void StartMonitoring(int intervalMs = 1000)
        {
            if (_isMonitoring) return;

            _logger.Info($"开始性能监控，间隔: {intervalMs}ms");
            
            _isMonitoring = true;
            _monitorTimer.Change(0, intervalMs);
            
            _logger.Info("性能监控已启动");
        }

        /// <summary>
        /// 停止性能监控
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            _logger.Info("停止性能监控");
            
            _monitorTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _isMonitoring = false;
            
            _logger.Info("性能监控已停止");
        }

        /// <summary>
        /// 收集性能数据
        /// </summary>
        /// <param name="state">状态对象</param>
        private void CollectPerformanceData(object state)
        {
            try
            {
                var snapshot = new PerformanceSnapshot
                {
                    Timestamp = DateTime.Now,
                    CpuUsage = GetCpuUsage(),
                    MemoryUsage = GetMemoryUsage(),
                    ThreadCount = _currentProcess.Threads.Count,
                    HandleCount = _currentProcess.HandleCount,
                    GCMemory = GC.GetTotalMemory(false)
                };

                lock (_lockObject)
                {
                    _snapshots.Add(snapshot);
                    
                    // 限制快照数量，避免内存溢出
                    if (_snapshots.Count > 1000)
                    {
                        _snapshots.RemoveAt(0);
                    }
                }

                // 触发性能数据更新事件
                PerformanceDataUpdated?.Invoke(this, new PerformanceDataEventArgs(snapshot));
                
                _logger.Debug($"性能数据收集完成: CPU={snapshot.CpuUsage:F1}%, Memory={snapshot.MemoryUsage / 1024 / 1024:F1}MB");
            }
            catch (Exception ex)
            {
                _logger.Error("收集性能数据时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        /// <returns>CPU使用率百分比</returns>
        private double GetCpuUsage()
        {
            try
            {
                // 简化实现，实际应该使用PerformanceCounter
                return _currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取内存使用量
        /// </summary>
        /// <returns>内存使用量（字节）</returns>
        private long GetMemoryUsage()
        {
            try
            {
                return _currentProcess.WorkingSet64;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取性能快照
        /// </summary>
        /// <param name="count">获取数量，0表示获取所有</param>
        /// <returns>性能快照列表</returns>
        public List<PerformanceSnapshot> GetPerformanceSnapshots(int count = 0)
        {
            lock (_lockObject)
            {
                if (count <= 0)
                {
                    return new List<PerformanceSnapshot>(_snapshots);
                }
                else
                {
                    var startIndex = Math.Max(0, _snapshots.Count - count);
                    return _snapshots.Skip(startIndex).ToList();
                }
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计信息</returns>
        public PerformanceStatistics GetPerformanceStatistics()
        {
            lock (_lockObject)
            {
                if (_snapshots.Count == 0)
                {
                    return new PerformanceStatistics();
                }

                var stats = new PerformanceStatistics
                {
                    SampleCount = _snapshots.Count,
                    TimeSpan = _snapshots.Last().Timestamp - _snapshots.First().Timestamp,
                    
                    AverageCpuUsage = _snapshots.Average(s => s.CpuUsage),
                    MaxCpuUsage = _snapshots.Max(s => s.CpuUsage),
                    MinCpuUsage = _snapshots.Min(s => s.CpuUsage),
                    
                    AverageMemoryUsage = _snapshots.Average(s => s.MemoryUsage),
                    MaxMemoryUsage = _snapshots.Max(s => s.MemoryUsage),
                    MinMemoryUsage = _snapshots.Min(s => s.MemoryUsage),
                    
                    AverageThreadCount = _snapshots.Average(s => s.ThreadCount),
                    MaxThreadCount = _snapshots.Max(s => s.ThreadCount),
                    MinThreadCount = _snapshots.Min(s => s.ThreadCount),
                    
                    CurrentMemoryUsage = _snapshots.Last().MemoryUsage,
                    CurrentCpuUsage = _snapshots.Last().CpuUsage,
                    CurrentThreadCount = _snapshots.Last().ThreadCount
                };

                return stats;
            }
        }

        /// <summary>
        /// 清除性能数据
        /// </summary>
        public void ClearPerformanceData()
        {
            lock (_lockObject)
            {
                _snapshots.Clear();
                _logger.Info("性能数据已清除");
            }
        }

        /// <summary>
        /// 执行性能测试
        /// </summary>
        /// <param name="testAction">测试动作</param>
        /// <param name="testName">测试名称</param>
        /// <returns>性能测试结果</returns>
        public async Task<PerformanceTestResult> RunPerformanceTestAsync(Func<Task> testAction, string testName)
        {
            var result = new PerformanceTestResult
            {
                TestName = testName,
                StartTime = DateTime.Now
            };

            var startSnapshot = GetCurrentSnapshot();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                await testAction();
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                _logger.Error($"性能测试执行失败: {testName}", ex);
            }
            finally
            {
                stopwatch.Stop();
                var endSnapshot = GetCurrentSnapshot();

                result.EndTime = DateTime.Now;
                result.Duration = stopwatch.Elapsed;
                result.CpuUsageDelta = endSnapshot.CpuUsage - startSnapshot.CpuUsage;
                result.MemoryUsageDelta = endSnapshot.MemoryUsage - startSnapshot.MemoryUsage;
                result.ThreadCountDelta = endSnapshot.ThreadCount - startSnapshot.ThreadCount;
            }

            return result;
        }

        /// <summary>
        /// 获取当前性能快照
        /// </summary>
        /// <returns>当前性能快照</returns>
        private PerformanceSnapshot GetCurrentSnapshot()
        {
            return new PerformanceSnapshot
            {
                Timestamp = DateTime.Now,
                CpuUsage = GetCpuUsage(),
                MemoryUsage = GetMemoryUsage(),
                ThreadCount = _currentProcess.Threads.Count,
                HandleCount = _currentProcess.HandleCount,
                GCMemory = GC.GetTotalMemory(false)
            };
        }

        /// <summary>
        /// 记录系统级性能指标
        /// </summary>
        /// <param name="metricName">指标名称</param>
        /// <param name="value">指标值</param>
        /// <param name="unit">单位</param>
        public void RecordSystemMetric(string metricName, double value, string unit = "")
        {
            lock (_lockObject)
            {
                var metric = new PerformanceMetric
                {
                    Name = metricName,
                    Value = value,
                    Unit = unit,
                    Timestamp = DateTime.Now
                };

                _systemMetrics[metricName] = metric;
                _logger?.Debug($"记录系统指标: {metricName} = {value} {unit}");
            }
        }

        /// <summary>
        /// 记录启动时间指标
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <param name="startupTime">启动时间（毫秒）</param>
        public void RecordStartupTime(string componentName, double startupTime)
        {
            RecordSystemMetric($"StartupTime_{componentName}", startupTime, "ms");
            _logger?.Info($"{componentName} 启动时间: {startupTime:F2}ms");
        }

        /// <summary>
        /// 记录模块加载性能
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="loadTime">加载时间（毫秒）</param>
        /// <param name="success">是否成功</param>
        public void RecordModuleLoadPerformance(string moduleName, double loadTime, bool success)
        {
            RecordSystemMetric($"ModuleLoad_{moduleName}", loadTime, "ms");
            RecordSystemMetric($"ModuleLoadSuccess_{moduleName}", success ? 1 : 0, "bool");

            var status = success ? "成功" : "失败";
            _logger?.Info($"模块 {moduleName} 加载{status}: {loadTime:F2}ms");
        }

        /// <summary>
        /// 记录UI响应时间
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="responseTime">响应时间（毫秒）</param>
        public void RecordUIResponseTime(string operationName, double responseTime)
        {
            RecordSystemMetric($"UIResponse_{operationName}", responseTime, "ms");

            if (responseTime > 100)
            {
                _logger?.Warning($"UI响应时间过长: {operationName} = {responseTime:F2}ms");
            }
            else
            {
                _logger?.Debug($"UI响应时间: {operationName} = {responseTime:F2}ms");
            }
        }

        /// <summary>
        /// 获取应用程序运行时间
        /// </summary>
        /// <returns>运行时间</returns>
        public TimeSpan GetApplicationUptime()
        {
            return _applicationUptime.Elapsed;
        }

        /// <summary>
        /// 获取系统性能报告
        /// </summary>
        /// <returns>性能报告</returns>
        public string GenerateSystemPerformanceReport()
        {
            lock (_lockObject)
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 系统性能报告 ===");
                report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"应用启动时间: {_applicationStartTime:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"运行时长: {GetApplicationUptime():hh\\:mm\\:ss}");
                report.AppendLine();

                // 启动时间指标
                var startupMetrics = _systemMetrics.Where(m => m.Key.StartsWith("StartupTime_")).ToList();
                if (startupMetrics.Any())
                {
                    report.AppendLine("=== 启动时间分析 ===");
                    foreach (var metric in startupMetrics.OrderBy(m => m.Value.Value))
                    {
                        var componentName = metric.Key.Replace("StartupTime_", "");
                        report.AppendLine($"{componentName}: {metric.Value.Value:F2}ms");
                    }
                    report.AppendLine();
                }

                // 模块加载性能
                var moduleMetrics = _systemMetrics.Where(m => m.Key.StartsWith("ModuleLoad_") && !m.Key.Contains("Success")).ToList();
                if (moduleMetrics.Any())
                {
                    report.AppendLine("=== 模块加载性能 ===");
                    foreach (var metric in moduleMetrics.OrderBy(m => m.Value.Value))
                    {
                        var moduleName = metric.Key.Replace("ModuleLoad_", "");
                        var successKey = $"ModuleLoadSuccess_{moduleName}";
                        var success = _systemMetrics.ContainsKey(successKey) && _systemMetrics[successKey].Value > 0;
                        var status = success ? "✅" : "❌";
                        report.AppendLine($"{status} {moduleName}: {metric.Value.Value:F2}ms");
                    }
                    report.AppendLine();
                }

                // UI响应时间
                var uiMetrics = _systemMetrics.Where(m => m.Key.StartsWith("UIResponse_")).ToList();
                if (uiMetrics.Any())
                {
                    report.AppendLine("=== UI响应时间分析 ===");
                    var avgResponseTime = uiMetrics.Average(m => m.Value.Value);
                    var maxResponseTime = uiMetrics.Max(m => m.Value.Value);
                    var slowOperations = uiMetrics.Where(m => m.Value.Value > 100).ToList();

                    report.AppendLine($"平均响应时间: {avgResponseTime:F2}ms");
                    report.AppendLine($"最大响应时间: {maxResponseTime:F2}ms");
                    report.AppendLine($"慢操作数量: {slowOperations.Count}");

                    if (slowOperations.Any())
                    {
                        report.AppendLine("慢操作详情:");
                        foreach (var op in slowOperations.OrderByDescending(m => m.Value.Value))
                        {
                            var operationName = op.Key.Replace("UIResponse_", "");
                            report.AppendLine($"  {operationName}: {op.Value.Value:F2}ms");
                        }
                    }
                    report.AppendLine();
                }

                // 当前系统状态
                var currentSnapshot = GetCurrentSnapshot();
                report.AppendLine("=== 当前系统状态 ===");
                report.AppendLine($"CPU使用率: {currentSnapshot.CpuUsage:F1}%");
                report.AppendLine($"内存使用: {currentSnapshot.MemoryUsage / 1024 / 1024:F1} MB");
                report.AppendLine($"GC内存: {currentSnapshot.GCMemory / 1024 / 1024:F1} MB");
                report.AppendLine($"线程数: {currentSnapshot.ThreadCount}");
                report.AppendLine($"句柄数: {currentSnapshot.HandleCount}");

                return report.ToString();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                StopMonitoring();
                _monitorTimer?.Dispose();
                _currentProcess?.Dispose();
                _logger?.Debug("PerformanceMonitor 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 PerformanceMonitor 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 性能快照
    /// </summary>
    public class PerformanceSnapshot
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage { get; set; }

        /// <summary>
        /// 线程数量
        /// </summary>
        public int ThreadCount { get; set; }

        /// <summary>
        /// 句柄数量
        /// </summary>
        public int HandleCount { get; set; }

        /// <summary>
        /// GC内存使用量
        /// </summary>
        public long GCMemory { get; set; }
    }

    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStatistics
    {
        /// <summary>
        /// 样本数量
        /// </summary>
        public int SampleCount { get; set; }

        /// <summary>
        /// 时间跨度
        /// </summary>
        public TimeSpan TimeSpan { get; set; }

        /// <summary>
        /// 平均CPU使用率
        /// </summary>
        public double AverageCpuUsage { get; set; }

        /// <summary>
        /// 最大CPU使用率
        /// </summary>
        public double MaxCpuUsage { get; set; }

        /// <summary>
        /// 最小CPU使用率
        /// </summary>
        public double MinCpuUsage { get; set; }

        /// <summary>
        /// 平均内存使用量
        /// </summary>
        public double AverageMemoryUsage { get; set; }

        /// <summary>
        /// 最大内存使用量
        /// </summary>
        public long MaxMemoryUsage { get; set; }

        /// <summary>
        /// 最小内存使用量
        /// </summary>
        public long MinMemoryUsage { get; set; }

        /// <summary>
        /// 平均线程数量
        /// </summary>
        public double AverageThreadCount { get; set; }

        /// <summary>
        /// 最大线程数量
        /// </summary>
        public int MaxThreadCount { get; set; }

        /// <summary>
        /// 最小线程数量
        /// </summary>
        public int MinThreadCount { get; set; }

        /// <summary>
        /// 当前内存使用量
        /// </summary>
        public long CurrentMemoryUsage { get; set; }

        /// <summary>
        /// 当前CPU使用率
        /// </summary>
        public double CurrentCpuUsage { get; set; }

        /// <summary>
        /// 当前线程数量
        /// </summary>
        public int CurrentThreadCount { get; set; }
    }

    /// <summary>
    /// 性能测试结果
    /// </summary>
    public class PerformanceTestResult
    {
        /// <summary>
        /// 测试名称
        /// </summary>
        public string TestName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// CPU使用率变化
        /// </summary>
        public double CpuUsageDelta { get; set; }

        /// <summary>
        /// 内存使用量变化
        /// </summary>
        public long MemoryUsageDelta { get; set; }

        /// <summary>
        /// 线程数量变化
        /// </summary>
        public int ThreadCountDelta { get; set; }
    }

    /// <summary>
    /// 性能数据事件参数
    /// </summary>
    public class PerformanceDataEventArgs : EventArgs
    {
        /// <summary>
        /// 性能快照
        /// </summary>
        public PerformanceSnapshot Snapshot { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="snapshot">性能快照</param>
        public PerformanceDataEventArgs(PerformanceSnapshot snapshot)
        {
            Snapshot = snapshot;
        }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetric
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 指标值
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
