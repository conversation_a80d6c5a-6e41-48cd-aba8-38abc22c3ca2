using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;

namespace PerformanceAnalysis
{
    /// <summary>
    /// 性能分析工具
    /// </summary>
    /// <remarks>
    /// 用于分析工业HMI框架的性能表现
    /// </remarks>
    public class PerformanceAnalyzer
    {
        private readonly List<PerformanceMetric> _metrics;
        private readonly Stopwatch _globalStopwatch;
        private readonly Process _currentProcess;

        /// <summary>
        /// 构造函数
        /// </summary>
        public PerformanceAnalyzer()
        {
            _metrics = new List<PerformanceMetric>();
            _globalStopwatch = Stopwatch.StartNew();
            _currentProcess = Process.GetCurrentProcess();
        }

        /// <summary>
        /// 开始测量性能指标
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>性能测量令牌</returns>
        public PerformanceMeasurement StartMeasurement(string operationName)
        {
            return new PerformanceMeasurement(this, operationName);
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="metric">性能指标</param>
        internal void RecordMetric(PerformanceMetric metric)
        {
            _metrics.Add(metric);
        }

        /// <summary>
        /// 获取当前内存使用情况
        /// </summary>
        /// <returns>内存使用信息</returns>
        public MemoryUsage GetCurrentMemoryUsage()
        {
            _currentProcess.Refresh();
            return new MemoryUsage
            {
                WorkingSet = _currentProcess.WorkingSet64,
                VirtualMemory = _currentProcess.VirtualMemorySize64,
                PrivateMemory = _currentProcess.PrivateMemorySize64,
                GCMemory = GC.GetTotalMemory(false),
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        /// <returns>性能报告</returns>
        public string GenerateReport()
        {
            var report = new StringBuilder();
            report.AppendLine("=== 工业HMI框架性能分析报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"总运行时间: {_globalStopwatch.Elapsed.TotalMilliseconds:F2}ms");
            report.AppendLine();

            // 启动时间分析
            var startupMetrics = _metrics.Where(m => m.Category == "Startup").ToList();
            if (startupMetrics.Any())
            {
                report.AppendLine("=== 启动时间分析 ===");
                foreach (var metric in startupMetrics.OrderBy(m => m.StartTime))
                {
                    report.AppendLine($"{metric.OperationName}: {metric.Duration.TotalMilliseconds:F2}ms");
                }
                report.AppendLine($"总启动时间: {startupMetrics.Sum(m => m.Duration.TotalMilliseconds):F2}ms");
                report.AppendLine();
            }

            // 模块加载分析
            var moduleMetrics = _metrics.Where(m => m.Category == "Module").ToList();
            if (moduleMetrics.Any())
            {
                report.AppendLine("=== 模块加载分析 ===");
                foreach (var metric in moduleMetrics.OrderBy(m => m.StartTime))
                {
                    report.AppendLine($"{metric.OperationName}: {metric.Duration.TotalMilliseconds:F2}ms");
                }
                report.AppendLine($"总模块加载时间: {moduleMetrics.Sum(m => m.Duration.TotalMilliseconds):F2}ms");
                report.AppendLine();
            }

            // UI响应分析
            var uiMetrics = _metrics.Where(m => m.Category == "UI").ToList();
            if (uiMetrics.Any())
            {
                report.AppendLine("=== UI响应分析 ===");
                foreach (var metric in uiMetrics.OrderBy(m => m.StartTime))
                {
                    report.AppendLine($"{metric.OperationName}: {metric.Duration.TotalMilliseconds:F2}ms");
                }
                var avgResponseTime = uiMetrics.Average(m => m.Duration.TotalMilliseconds);
                report.AppendLine($"平均UI响应时间: {avgResponseTime:F2}ms");
                report.AppendLine();
            }

            // 内存使用分析
            var memoryMetrics = _metrics.Where(m => m.MemoryUsage != null).ToList();
            if (memoryMetrics.Any())
            {
                report.AppendLine("=== 内存使用分析 ===");
                var firstMemory = memoryMetrics.First().MemoryUsage;
                var lastMemory = memoryMetrics.Last().MemoryUsage;
                
                report.AppendLine($"初始内存使用:");
                report.AppendLine($"  工作集: {firstMemory.WorkingSet / 1024 / 1024:F2} MB");
                report.AppendLine($"  虚拟内存: {firstMemory.VirtualMemory / 1024 / 1024:F2} MB");
                report.AppendLine($"  GC内存: {firstMemory.GCMemory / 1024 / 1024:F2} MB");
                
                report.AppendLine($"当前内存使用:");
                report.AppendLine($"  工作集: {lastMemory.WorkingSet / 1024 / 1024:F2} MB");
                report.AppendLine($"  虚拟内存: {lastMemory.VirtualMemory / 1024 / 1024:F2} MB");
                report.AppendLine($"  GC内存: {lastMemory.GCMemory / 1024 / 1024:F2} MB");
                
                report.AppendLine($"内存增长:");
                report.AppendLine($"  工作集: {(lastMemory.WorkingSet - firstMemory.WorkingSet) / 1024 / 1024:F2} MB");
                report.AppendLine($"  GC内存: {(lastMemory.GCMemory - firstMemory.GCMemory) / 1024 / 1024:F2} MB");
                report.AppendLine();
            }

            // 性能评估
            report.AppendLine("=== 性能评估 ===");
            var totalStartupTime = startupMetrics.Sum(m => m.Duration.TotalMilliseconds);
            var avgUiResponse = uiMetrics.Any() ? uiMetrics.Average(m => m.Duration.TotalMilliseconds) : 0;
            
            report.AppendLine($"启动时间评估: {(totalStartupTime < 10000 ? "✅ 优秀" : "❌ 需要优化")} ({totalStartupTime:F2}ms < 10000ms)");
            report.AppendLine($"UI响应评估: {(avgUiResponse < 100 ? "✅ 优秀" : "❌ 需要优化")} ({avgUiResponse:F2}ms < 100ms)");
            
            if (memoryMetrics.Any())
            {
                var memoryGrowth = (memoryMetrics.Last().MemoryUsage.GCMemory - memoryMetrics.First().MemoryUsage.GCMemory) / 1024 / 1024;
                report.AppendLine($"内存稳定性: {(memoryGrowth < 50 ? "✅ 稳定" : "❌ 需要优化")} (增长{memoryGrowth:F2}MB)");
            }

            return report.ToString();
        }

        /// <summary>
        /// 保存报告到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public void SaveReport(string filePath)
        {
            var report = GenerateReport();
            File.WriteAllText(filePath, report, Encoding.UTF8);
        }
    }

    /// <summary>
    /// 性能测量令牌
    /// </summary>
    public class PerformanceMeasurement : IDisposable
    {
        private readonly PerformanceAnalyzer _analyzer;
        private readonly string _operationName;
        private readonly Stopwatch _stopwatch;
        private readonly DateTime _startTime;
        private readonly MemoryUsage _startMemory;
        private string _category = "General";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="analyzer">性能分析器</param>
        /// <param name="operationName">操作名称</param>
        internal PerformanceMeasurement(PerformanceAnalyzer analyzer, string operationName)
        {
            _analyzer = analyzer;
            _operationName = operationName;
            _startTime = DateTime.Now;
            _startMemory = analyzer.GetCurrentMemoryUsage();
            _stopwatch = Stopwatch.StartNew();
        }

        /// <summary>
        /// 设置分类
        /// </summary>
        /// <param name="category">分类</param>
        /// <returns>当前实例</returns>
        public PerformanceMeasurement WithCategory(string category)
        {
            _category = category;
            return this;
        }

        /// <summary>
        /// 释放资源并记录性能指标
        /// </summary>
        public void Dispose()
        {
            _stopwatch.Stop();
            var endMemory = _analyzer.GetCurrentMemoryUsage();
            
            var metric = new PerformanceMetric
            {
                OperationName = _operationName,
                Category = _category,
                StartTime = _startTime,
                Duration = _stopwatch.Elapsed,
                MemoryUsage = endMemory
            };
            
            _analyzer.RecordMetric(metric);
        }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetric
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 持续时间
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 内存使用情况
        /// </summary>
        public MemoryUsage MemoryUsage { get; set; }
    }

    /// <summary>
    /// 内存使用信息
    /// </summary>
    public class MemoryUsage
    {
        /// <summary>
        /// 工作集大小
        /// </summary>
        public long WorkingSet { get; set; }

        /// <summary>
        /// 虚拟内存大小
        /// </summary>
        public long VirtualMemory { get; set; }

        /// <summary>
        /// 私有内存大小
        /// </summary>
        public long PrivateMemory { get; set; }

        /// <summary>
        /// GC管理的内存
        /// </summary>
        public long GCMemory { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
