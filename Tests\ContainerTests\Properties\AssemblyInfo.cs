using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("ContainerTests")]
[assembly: AssemblyDescription("WinForms工业通用架构 - 容器测试")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Industrial Automation Team")]
[assembly: AssemblyProduct("IndustrialHMI")]
[assembly: AssemblyCopyright("Copyright © 2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("f7e8a9b0-c1d2-3456-789a-bcdef0123456")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
