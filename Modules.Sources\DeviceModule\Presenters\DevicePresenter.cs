using System;
using System.Threading.Tasks;
using Contracts;
using DeviceModule.Models;
using DeviceModule.Views;

namespace DeviceModule.Presenters
{
    /// <summary>
    /// 设备监控表示器
    /// </summary>
    /// <remarks>
    /// 协调设备视图和模型之间的交互，处理用户操作和业务逻辑
    /// </remarks>
    public class DevicePresenter : IPresenter, IDisposable
    {
        private readonly DeviceView _view;
        private readonly DeviceModel _model;
        private readonly ILogger _logger;
        private bool _disposed = false;
        private bool _isMonitoring = false;

        /// <summary>
        /// 关联的视图
        /// </summary>
        public IView View { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="view">设备视图</param>
        /// <param name="model">设备模型</param>
        /// <param name="logger">日志记录器</param>
        public DevicePresenter(DeviceView view, DeviceModel model, ILogger logger)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _model = model ?? throw new ArgumentNullException(nameof(model));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            View = _view; // 设置IPresenter.View属性

            // 绑定视图事件
            BindViewEvents();

            // 绑定模型事件
            BindModelEvents();

            _logger.Debug("DevicePresenter 初始化完成");
        }

        /// <summary>
        /// 绑定视图事件
        /// </summary>
        private void BindViewEvents()
        {
            _view.RefreshRequested += OnRefreshRequested;
            _view.ConnectAllRequested += OnConnectAllRequested;
            _view.DisconnectAllRequested += OnDisconnectAllRequested;
            _view.StartMonitoringRequested += OnStartMonitoringRequested;
            _view.StopMonitoringRequested += OnStopMonitoringRequested;
            _view.DeviceConnectRequested += OnDeviceConnectRequested;
            _view.DeviceDisconnectRequested += OnDeviceDisconnectRequested;
        }

        /// <summary>
        /// 绑定模型事件
        /// </summary>
        private void BindModelEvents()
        {
            _model.DeviceListChanged += OnDeviceListChanged;
            _model.DeviceStatusChanged += OnDeviceStatusChanged;
        }

        /// <summary>
        /// 刷新请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnRefreshRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求刷新设备列表");
                _view.UpdateStatus("正在刷新设备列表...");
                _view.ShowProgress(true, 0);

                await Task.Run(() =>
                {
                    _model.LoadDeviceList();
                });

                _view.ShowProgress(false);
                _view.UpdateStatus("设备列表刷新完成");
                _logger.Info("设备列表刷新完成");
            }
            catch (Exception ex)
            {
                _view.ShowProgress(false);
                _view.ShowError($"刷新设备列表失败: {ex.Message}");
                _logger.Error("刷新设备列表时发生错误", ex);
            }
        }

        /// <summary>
        /// 连接所有设备请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnConnectAllRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求连接所有设备");
                _view.UpdateStatus("正在连接所有设备...");
                _view.ShowProgress(true, 0);

                await Task.Run(() =>
                {
                    _model.ConnectAllDevices();
                });

                _view.ShowProgress(false);
                _view.UpdateStatus("连接所有设备请求已发送");
                _logger.Info("连接所有设备请求已发送");
            }
            catch (Exception ex)
            {
                _view.ShowProgress(false);
                _view.ShowError($"连接所有设备失败: {ex.Message}");
                _logger.Error("连接所有设备时发生错误", ex);
            }
        }

        /// <summary>
        /// 断开所有设备请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnDisconnectAllRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求断开所有设备");
                _view.UpdateStatus("正在断开所有设备...");
                _view.ShowProgress(true, 0);

                await Task.Run(() =>
                {
                    _model.DisconnectAllDevices();
                });

                _view.ShowProgress(false);
                _view.UpdateStatus("断开所有设备完成");
                _logger.Info("断开所有设备完成");
            }
            catch (Exception ex)
            {
                _view.ShowProgress(false);
                _view.ShowError($"断开所有设备失败: {ex.Message}");
                _logger.Error("断开所有设备时发生错误", ex);
            }
        }

        /// <summary>
        /// 开始监控请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnStartMonitoringRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求开始设备监控");
                _model.StartMonitoring();
                _isMonitoring = true;
                _view.SetMonitoringState(true);
                _logger.Info("设备监控已启动");
            }
            catch (Exception ex)
            {
                _view.ShowError($"启动设备监控失败: {ex.Message}");
                _logger.Error("启动设备监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 停止监控请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnStopMonitoringRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求停止设备监控");
                _model.StopMonitoring();
                _isMonitoring = false;
                _view.SetMonitoringState(false);
                _logger.Info("设备监控已停止");
            }
            catch (Exception ex)
            {
                _view.ShowError($"停止设备监控失败: {ex.Message}");
                _logger.Error("停止设备监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 设备连接请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnDeviceConnectRequested(object sender, DeviceActionEventArgs e)
        {
            try
            {
                _logger.Info($"用户请求连接设备: {e.DeviceId}");
                _view.UpdateStatus($"正在连接设备: {e.DeviceId}");

                await Task.Run(() =>
                {
                    _model.ConnectDevice(e.DeviceId);
                });

                _view.UpdateStatus($"设备连接请求已发送: {e.DeviceId}");
                _logger.Info($"设备连接请求已发送: {e.DeviceId}");
            }
            catch (Exception ex)
            {
                _view.ShowError($"连接设备失败: {ex.Message}");
                _logger.Error($"连接设备时发生错误: {e.DeviceId}", ex);
            }
        }

        /// <summary>
        /// 设备断开请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnDeviceDisconnectRequested(object sender, DeviceActionEventArgs e)
        {
            try
            {
                _logger.Info($"用户请求断开设备: {e.DeviceId}");
                _view.UpdateStatus($"正在断开设备: {e.DeviceId}");

                await Task.Run(() =>
                {
                    _model.DisconnectDevice(e.DeviceId);
                });

                _view.UpdateStatus($"设备已断开: {e.DeviceId}");
                _logger.Info($"设备已断开: {e.DeviceId}");
            }
            catch (Exception ex)
            {
                _view.ShowError($"断开设备失败: {ex.Message}");
                _logger.Error($"断开设备时发生错误: {e.DeviceId}", ex);
            }
        }

        /// <summary>
        /// 设备列表变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnDeviceListChanged(object sender, DeviceListChangedEventArgs e)
        {
            try
            {
                _logger.Debug($"设备列表已更新，共 {e.Devices.Count} 个设备");
                _view.ShowDevices(e.Devices);
            }
            catch (Exception ex)
            {
                _view.ShowError($"更新设备列表显示失败: {ex.Message}");
                _logger.Error("更新设备列表显示时发生错误", ex);
            }
        }

        /// <summary>
        /// 设备状态变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnDeviceStatusChanged(object sender, DeviceStatusChangedEventArgs e)
        {
            try
            {
                _logger.Debug($"设备状态已更新: {e.Device.DeviceId} - {e.Device.Status}");
                _view.UpdateDeviceStatus(e.Device.DeviceId, e.Device.Status);
            }
            catch (Exception ex)
            {
                _view.ShowError($"更新设备状态显示失败: {ex.Message}");
                _logger.Error("更新设备状态显示时发生错误", ex);
            }
        }

        /// <summary>
        /// 初始化表示器
        /// </summary>
        public void Initialize()
        {
            try
            {
                _logger.Info("初始化设备表示器");

                // 加载初始设备列表
                LoadData();

                _view.UpdateStatus("设备监控模块已就绪");
                _logger.Info("设备表示器初始化完成");
            }
            catch (Exception ex)
            {
                _view.ShowError($"初始化设备表示器失败: {ex.Message}");
                _logger.Error("初始化设备表示器时发生错误", ex);
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        public void LoadData()
        {
            try
            {
                _logger.Debug("加载设备数据");
                _model.LoadDeviceList();
            }
            catch (Exception ex)
            {
                _view.ShowError($"加载设备数据失败: {ex.Message}");
                _logger.Error("加载设备数据时发生错误", ex);
            }
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <returns>保存是否成功</returns>
        public bool SaveData()
        {
            // 设备监控模块通常不需要保存数据
            return true;
        }

        /// <summary>
        /// 验证数据
        /// </summary>
        /// <returns>验证是否通过</returns>
        public bool ValidateData()
        {
            // 设备监控模块通常不需要验证数据
            return true;
        }

        /// <summary>
        /// 启动监控
        /// </summary>
        public void StartMonitoring()
        {
            if (!_isMonitoring)
            {
                OnStartMonitoringRequested(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (_isMonitoring)
            {
                OnStopMonitoringRequested(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 停止监控
                StopMonitoring();

                // 取消视图事件绑定
                if (_view != null)
                {
                    _view.RefreshRequested -= OnRefreshRequested;
                    _view.ConnectAllRequested -= OnConnectAllRequested;
                    _view.DisconnectAllRequested -= OnDisconnectAllRequested;
                    _view.StartMonitoringRequested -= OnStartMonitoringRequested;
                    _view.StopMonitoringRequested -= OnStopMonitoringRequested;
                    _view.DeviceConnectRequested -= OnDeviceConnectRequested;
                    _view.DeviceDisconnectRequested -= OnDeviceDisconnectRequested;
                }

                // 取消模型事件绑定
                if (_model != null)
                {
                    _model.DeviceListChanged -= OnDeviceListChanged;
                    _model.DeviceStatusChanged -= OnDeviceStatusChanged;
                }

                _logger?.Debug("DevicePresenter 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 DevicePresenter 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
