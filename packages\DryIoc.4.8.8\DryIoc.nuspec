﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="3.3.0">
    <id>DryIoc</id>
    <version>4.8.8</version>
    <authors><PERSON><PERSON><PERSON></authors>
    <owners><PERSON><PERSON><PERSON></owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>logo.png</icon>
    <projectUrl>https://github.com/dadhi/DryIoc</projectUrl>
    <iconUrl>https://github.com/dadhi/DryIoc/blob/master/logo.png</iconUrl>
    <description>DryIoc is fast, small, full-featured IoC Container for .NET</description>
    <releaseNotes>## v4.8.8 Bug-fix release

- fixed: #460 Getting instance from parent scope even if replaced by Use

## v4.8.7 Bug-fix release

- fixed: #435 hangfire use dryioc report ContainerIsDisposed
- fixed: #449 Optional dependency shouldn't treat its own dependencies as optional
- fixed: #451 Compiler-generated type as a service
- fixed: #456 One more regression

## v4.8.6 Bug-fix release

- fixed: #446 Resolving a record without registration causes a StackOverflowException

## v4.8.5 Bug-fix release

- fixed: NY bug of RegisterInstance of null string

## v4.8.4 Bug-fix release

- fixed: #434 ReturnDefaultIfNotRegistered is not respected between scopes
- fixed: #435 hangfire use dryioc report ContainerIsDisposed

## v4.8.3 Bug-fix release

- fixed: #418 Resolving interfaces with covariant type parameter fails when using RegisterMapping
- fixed: #432 Resolving interfaces with contravariant type parameter fails with RegisteringImplementationNotAssignableToServiceType error

## v4.8.2 Improvement release

- fixed: #429 Memory leak on MS DI with Disposable Transient

## v4.8.1 Bug-fix release

- fixed: #412 ResolveMany not work with generics after any Unregister

## v4.8.0 Small feature release

- added: #406 Allow the registration of the partially closed implementation type</releaseNotes>
    <copyright>Copyright © 2013-2022 Maksim Volkau</copyright>
    <tags>IoC Container Inversion-of-Control DI Dependency-Injection DRY Service-Provider Factory</tags>
    <dependencies>
      <group targetFramework=".NETFramework3.5" />
      <group targetFramework=".NETFramework4.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETStandard1.0">
        <dependency id="NETStandard.Library" version="1.6.1" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="NETStandard.Library" version="1.6.1" />
        <dependency id="System.Reflection.Emit.Lightweight" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Reflection.Emit.Lightweight" version="4.3.0" />
      </group>
      <group targetFramework=".NETPortable0.0-Profile328" />
      <group targetFramework=".NETPortable0.0-Profile259" />
    </dependencies>
    <contentFiles>
      <files include="cs/**/*.cs" buildAction="Compile" />
      <files include="cs/**/*.tt" buildAction="None" />
      <files include="cs/**/*.ttinclude" buildAction="None" />
    </contentFiles>
  </metadata>
</package>