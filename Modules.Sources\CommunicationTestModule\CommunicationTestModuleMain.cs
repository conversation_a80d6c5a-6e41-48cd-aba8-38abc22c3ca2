using System;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using CommunicationTestModule.Models;
using CommunicationTestModule.Presenters;
using CommunicationTestModule.Views;

namespace CommunicationTestModule
{
    /// <summary>
    /// 通信测试模块主类
    /// </summary>
    /// <remarks>
    /// 模块间通信验证模块，用于测试和验证模块间事件通信的稳定性和性能
    /// </remarks>
    public class CommunicationTestModuleMain : IModule, IDisposable
    {
        private CommunicationTestView _view;
        private CommunicationTestModel _model;
        private CommunicationTestPresenter _presenter;
        private bool _disposed = false;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string Name => "通信测试";

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description => "模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告";

        /// <summary>
        /// 模块视图
        /// </summary>
        public Control View => _view;

        /// <summary>
        /// 获取用户控件
        /// </summary>
        /// <returns>用户控件</returns>
        public UserControl GetUserControl()
        {
            return _view;
        }

        /// <summary>
        /// 事件聚合器
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器
        /// </summary>
        /// <remarks>由ModuleLoader注入</remarks>
        public ILogger Logger { get; set; }

        /// <summary>
        /// 无参构造函数
        /// </summary>
        /// <remarks>ModuleLoader要求必须有无参构造函数</remarks>
        public CommunicationTestModuleMain()
        {
            // 空构造函数，实际初始化在Initialize方法中进行
        }

        /// <summary>
        /// 初始化模块
        /// </summary>
        public void Initialize()
        {
            try
            {
                // 验证依赖注入
                if (EventAggregator == null)
                    throw new InvalidOperationException("EventAggregator 未被注入");
                if (Logger == null)
                    throw new InvalidOperationException("Logger 未被注入");

                Logger.Info("开始初始化 CommunicationTestModule");

                // 创建MVP组件
                CreateMVPComponents();

                // 订阅系统事件
                SubscribeToSystemEvents();

                Logger.Info("CommunicationTestModule 初始化完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("初始化 CommunicationTestModule 时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 创建MVP组件
        /// </summary>
        private void CreateMVPComponents()
        {
            try
            {
                // 创建模型
                _model = new CommunicationTestModel(EventAggregator, Logger);

                // 创建视图
                _view = new CommunicationTestView(Logger);

                // 创建表示器
                _presenter = new CommunicationTestPresenter(_view, _model, Logger);

                Logger.Debug("MVP组件创建完成");
            }
            catch (Exception ex)
            {
                Logger.Error("创建MVP组件时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 订阅系统事件
        /// </summary>
        private void SubscribeToSystemEvents()
        {
            try
            {
                // 订阅系统启动事件
                EventAggregator.GetEvent<SystemStartupEvent>()
                    .Subscribe(OnSystemStartupEvent, ThreadOption.UIThread, false);

                // 订阅系统关闭事件
                EventAggregator.GetEvent<SystemShutdownEvent>()
                    .Subscribe(OnSystemShutdownEvent, ThreadOption.UIThread, false);

                // 订阅模块加载事件
                EventAggregator.GetEvent<ModuleLoadedEvent>()
                    .Subscribe(OnModuleLoadedEvent, ThreadOption.UIThread, false);

                // 订阅模块卸载事件
                EventAggregator.GetEvent<ModuleUnloadedEvent>()
                    .Subscribe(OnModuleUnloadedEvent, ThreadOption.UIThread, false);

                Logger.Debug("系统事件订阅完成");
            }
            catch (Exception ex)
            {
                Logger.Error("订阅系统事件时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 取消系统事件订阅
        /// </summary>
        private void UnsubscribeFromSystemEvents()
        {
            try
            {
                EventAggregator?.GetEvent<SystemStartupEvent>()
                    .Unsubscribe(OnSystemStartupEvent);

                EventAggregator?.GetEvent<SystemShutdownEvent>()
                    .Unsubscribe(OnSystemShutdownEvent);

                EventAggregator?.GetEvent<ModuleLoadedEvent>()
                    .Unsubscribe(OnModuleLoadedEvent);

                EventAggregator?.GetEvent<ModuleUnloadedEvent>()
                    .Unsubscribe(OnModuleUnloadedEvent);

                Logger?.Debug("系统事件订阅已取消");
            }
            catch (Exception ex)
            {
                Logger?.Error("取消系统事件订阅时发生错误", ex);
            }
        }

        // 系统事件处理方法
        private void OnSystemStartupEvent(SystemStartupEvent eventData)
        {
            try
            {
                Logger.Info("收到系统启动事件");
                
                // 可以在这里执行系统启动后的初始化操作
                // 例如自动开始事件监控
            }
            catch (Exception ex)
            {
                Logger.Error("处理系统启动事件时发生错误", ex);
            }
        }

        private void OnSystemShutdownEvent(SystemShutdownEvent eventData)
        {
            try
            {
                Logger.Info("收到系统关闭事件");
                
                // 在系统关闭前停止所有监控
                _model?.StopEventMonitoring();
                _model?.StopPerformanceMonitoring();
                _model?.StopTests();
            }
            catch (Exception ex)
            {
                Logger.Error("处理系统关闭事件时发生错误", ex);
            }
        }

        private void OnModuleLoadedEvent(ModuleLoadedEvent eventData)
        {
            try
            {
                Logger.Debug($"收到模块加载事件: {eventData.ModuleName}");
            }
            catch (Exception ex)
            {
                Logger.Error("处理模块加载事件时发生错误", ex);
            }
        }

        private void OnModuleUnloadedEvent(ModuleUnloadedEvent eventData)
        {
            try
            {
                Logger.Debug($"收到模块卸载事件: {eventData.ModuleName}");
            }
            catch (Exception ex)
            {
                Logger.Error("处理模块卸载事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 启动模块
        /// </summary>
        public void Start()
        {
            try
            {
                Logger.Info("启动 CommunicationTestModule");

                // 模块启动时可以执行的操作
                // 例如显示欢迎消息或自动开始某些监控

                Logger.Info("CommunicationTestModule 启动完成");
            }
            catch (Exception ex)
            {
                Logger.Error("启动 CommunicationTestModule 时发生错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止模块
        /// </summary>
        public void Stop()
        {
            try
            {
                Logger.Info("停止 CommunicationTestModule");

                // 停止所有监控和测试
                _model?.StopEventMonitoring();
                _model?.StopPerformanceMonitoring();
                _model?.StopTests();

                Logger.Info("CommunicationTestModule 停止完成");
            }
            catch (Exception ex)
            {
                Logger.Error("停止 CommunicationTestModule 时发生错误", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Logger?.Info("开始释放 CommunicationTestModule 资源");

                // 停止模块
                Stop();

                // 取消事件订阅
                UnsubscribeFromSystemEvents();

                // 释放MVP组件
                _presenter?.Dispose();
                _model?.Dispose();
                _view?.Dispose();

                Logger?.Info("CommunicationTestModule 资源释放完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("释放 CommunicationTestModule 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
