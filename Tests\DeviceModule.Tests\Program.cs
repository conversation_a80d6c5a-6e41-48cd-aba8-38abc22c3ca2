using System;
using System.Collections.Generic;
using DeviceModule.Tests.TestFramework;

namespace DeviceModule.Tests
{
    /// <summary>
    /// 测试程序主入口
    /// </summary>
    /// <remarks>
    /// 运行DeviceModule的所有单元测试
    /// </remarks>
    class Program
    {
        /// <summary>
        /// 程序主入口
        /// </summary>
        /// <param name="args">命令行参数</param>
        static void Main(string[] args)
        {
            Console.WriteLine("DeviceModule 单元测试");
            Console.WriteLine("===================");
            Console.WriteLine();

            try
            {
                // 检查测试环境
                if (!TestRunner.CheckTestEnvironment())
                {
                    Console.WriteLine("测试环境检查失败，退出测试");
                    Console.ReadKey();
                    return;
                }

                Console.WriteLine();

                // 运行所有测试
                var results = TestRunner.RunAllTests();

                // 生成测试报告
                TestRunner.GenerateReport(results);

                // 检查是否有失败的测试
                var hasFailures = results.Exists(r => !r.Passed);
                if (hasFailures)
                {
                    Console.WriteLine();
                    Console.WriteLine("⚠️  存在失败的测试，请检查代码实现");
                    Environment.ExitCode = 1;
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("🎉 所有测试通过！");
                    Environment.ExitCode = 0;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试运行时发生异常: {ex.Message}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Environment.ExitCode = 1;
            }

            // 等待用户输入
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
