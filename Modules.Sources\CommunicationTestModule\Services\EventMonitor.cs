using System;
using System.Collections.Generic;
using System.Linq;
using Contracts;
using Contracts.Events;

namespace CommunicationTestModule.Services
{
    /// <summary>
    /// 事件监控器
    /// </summary>
    /// <remarks>
    /// 监控系统中所有事件的传递情况，记录事件数据和性能指标
    /// </remarks>
    public class EventMonitor : IDisposable
    {
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private readonly List<EventRecord> _eventRecords;
        private readonly object _lockObject = new object();
        private bool _isMonitoring = false;
        private bool _disposed = false;

        /// <summary>
        /// 事件记录变化事件
        /// </summary>
        public event EventHandler<EventRecordChangedEventArgs> EventRecordChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public EventMonitor(IEventAggregator eventAggregator, ILogger logger)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _eventRecords = new List<EventRecord>();
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            if (_isMonitoring) return;

            _logger.Info("开始事件监控");
            
            // 订阅所有系统事件
            SubscribeToEvents();
            
            _isMonitoring = true;
            _logger.Info("事件监控已启动");
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            _logger.Info("停止事件监控");
            
            // 取消事件订阅
            UnsubscribeFromEvents();
            
            _isMonitoring = false;
            _logger.Info("事件监控已停止");
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            // 订阅系统事件
            _eventAggregator.GetEvent<SystemStartupEvent>()
                .Subscribe(OnSystemStartupEvent, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<SystemShutdownEvent>()
                .Subscribe(OnSystemShutdownEvent, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<ModuleLoadedEvent>()
                .Subscribe(OnModuleLoadedEvent, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<ModuleUnloadedEvent>()
                .Subscribe(OnModuleUnloadedEvent, ThreadOption.UIThread, false);

            // 订阅设备事件
            _eventAggregator.GetEvent<DeviceDataUpdateEvent>()
                .Subscribe(OnDeviceDataUpdateEvent, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<DeviceConnectionEvent>()
                .Subscribe(OnDeviceConnectionEvent, ThreadOption.UIThread, false);

            // 订阅报警事件
            _eventAggregator.GetEvent<AlarmEvent>()
                .Subscribe(OnAlarmEvent, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<AlarmAcknowledgedEvent>()
                .Subscribe(OnAlarmAcknowledgedEvent, ThreadOption.UIThread, false);
                
            _eventAggregator.GetEvent<AlarmClearedEvent>()
                .Subscribe(OnAlarmClearedEvent, ThreadOption.UIThread, false);
        }

        /// <summary>
        /// 取消事件订阅
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            _eventAggregator?.GetEvent<SystemStartupEvent>()
                .Unsubscribe(OnSystemStartupEvent);
                
            _eventAggregator?.GetEvent<SystemShutdownEvent>()
                .Unsubscribe(OnSystemShutdownEvent);
                
            _eventAggregator?.GetEvent<ModuleLoadedEvent>()
                .Unsubscribe(OnModuleLoadedEvent);
                
            _eventAggregator?.GetEvent<ModuleUnloadedEvent>()
                .Unsubscribe(OnModuleUnloadedEvent);

            _eventAggregator?.GetEvent<DeviceDataUpdateEvent>()
                .Unsubscribe(OnDeviceDataUpdateEvent);
                
            _eventAggregator?.GetEvent<DeviceConnectionEvent>()
                .Unsubscribe(OnDeviceConnectionEvent);

            _eventAggregator?.GetEvent<AlarmEvent>()
                .Unsubscribe(OnAlarmEvent);
                
            _eventAggregator?.GetEvent<AlarmAcknowledgedEvent>()
                .Unsubscribe(OnAlarmAcknowledgedEvent);
                
            _eventAggregator?.GetEvent<AlarmClearedEvent>()
                .Unsubscribe(OnAlarmClearedEvent);
        }

        /// <summary>
        /// 记录事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="source">事件源</param>
        private void RecordEvent(string eventType, object eventData, string source = "Unknown")
        {
            lock (_lockObject)
            {
                var record = new EventRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    EventType = eventType,
                    EventData = eventData,
                    Source = source,
                    Timestamp = DateTime.Now,
                    ProcessingTime = TimeSpan.Zero // 将在处理完成后更新
                };

                _eventRecords.Add(record);
                
                // 限制记录数量，避免内存溢出
                if (_eventRecords.Count > 1000)
                {
                    _eventRecords.RemoveAt(0);
                }

                // 触发事件记录变化事件
                EventRecordChanged?.Invoke(this, new EventRecordChangedEventArgs(record));
                
                _logger.Debug($"记录事件: {eventType} from {source}");
            }
        }

        // 事件处理方法
        private void OnSystemStartupEvent(SystemStartupEvent eventData)
        {
            RecordEvent("SystemStartupEvent", eventData, "System");
        }

        private void OnSystemShutdownEvent(SystemShutdownEvent eventData)
        {
            RecordEvent("SystemShutdownEvent", eventData, "System");
        }

        private void OnModuleLoadedEvent(ModuleLoadedEvent eventData)
        {
            RecordEvent("ModuleLoadedEvent", eventData, "ModuleLoader");
        }

        private void OnModuleUnloadedEvent(ModuleUnloadedEvent eventData)
        {
            RecordEvent("ModuleUnloadedEvent", eventData, "ModuleLoader");
        }

        private void OnDeviceDataUpdateEvent(DeviceDataUpdateEvent eventData)
        {
            RecordEvent("DeviceDataUpdateEvent", eventData, "DeviceModule");
        }

        private void OnDeviceConnectionEvent(DeviceConnectionEvent eventData)
        {
            RecordEvent("DeviceConnectionEvent", eventData, "DeviceModule");
        }

        private void OnAlarmEvent(AlarmEvent eventData)
        {
            RecordEvent("AlarmEvent", eventData, "AlarmModule");
        }

        private void OnAlarmAcknowledgedEvent(AlarmAcknowledgedEvent eventData)
        {
            RecordEvent("AlarmAcknowledgedEvent", eventData, "AlarmModule");
        }

        private void OnAlarmClearedEvent(AlarmClearedEvent eventData)
        {
            RecordEvent("AlarmClearedEvent", eventData, "AlarmModule");
        }

        /// <summary>
        /// 获取事件记录
        /// </summary>
        /// <param name="count">获取数量，0表示获取所有</param>
        /// <returns>事件记录列表</returns>
        public List<EventRecord> GetEventRecords(int count = 0)
        {
            lock (_lockObject)
            {
                if (count <= 0)
                {
                    return new List<EventRecord>(_eventRecords);
                }
                else
                {
                    var startIndex = Math.Max(0, _eventRecords.Count - count);
                    return _eventRecords.Skip(startIndex).ToList();
                }
            }
        }

        /// <summary>
        /// 清除事件记录
        /// </summary>
        public void ClearEventRecords()
        {
            lock (_lockObject)
            {
                _eventRecords.Clear();
                _logger.Info("事件记录已清除");
            }
        }

        /// <summary>
        /// 获取事件统计信息
        /// </summary>
        /// <returns>事件统计信息</returns>
        public EventStatistics GetEventStatistics()
        {
            lock (_lockObject)
            {
                var now = DateTime.Now;
                var lastHour = now.AddHours(-1);
                var lastMinute = now.AddMinutes(-1);

                var stats = new EventStatistics
                {
                    TotalEventCount = _eventRecords.Count,
                    LastHourEventCount = _eventRecords.Count(r => r.Timestamp >= lastHour),
                    LastMinuteEventCount = _eventRecords.Count(r => r.Timestamp >= lastMinute),
                    EventTypeStatistics = _eventRecords
                        .GroupBy(r => r.EventType)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    SourceStatistics = _eventRecords
                        .GroupBy(r => r.Source)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    LastEventTime = _eventRecords.LastOrDefault()?.Timestamp
                };

                return stats;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                StopMonitoring();
                _logger?.Debug("EventMonitor 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 EventMonitor 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 事件记录
    /// </summary>
    public class EventRecord
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        public string EventType { get; set; }

        /// <summary>
        /// 事件数据
        /// </summary>
        public object EventData { get; set; }

        /// <summary>
        /// 事件源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public TimeSpan ProcessingTime { get; set; }
    }

    /// <summary>
    /// 事件统计信息
    /// </summary>
    public class EventStatistics
    {
        /// <summary>
        /// 总事件数量
        /// </summary>
        public int TotalEventCount { get; set; }

        /// <summary>
        /// 最近一小时事件数量
        /// </summary>
        public int LastHourEventCount { get; set; }

        /// <summary>
        /// 最近一分钟事件数量
        /// </summary>
        public int LastMinuteEventCount { get; set; }

        /// <summary>
        /// 按事件类型统计
        /// </summary>
        public Dictionary<string, int> EventTypeStatistics { get; set; }

        /// <summary>
        /// 按事件源统计
        /// </summary>
        public Dictionary<string, int> SourceStatistics { get; set; }

        /// <summary>
        /// 最后事件时间
        /// </summary>
        public DateTime? LastEventTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public EventStatistics()
        {
            EventTypeStatistics = new Dictionary<string, int>();
            SourceStatistics = new Dictionary<string, int>();
        }
    }

    /// <summary>
    /// 事件记录变化事件参数
    /// </summary>
    public class EventRecordChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 新增的事件记录
        /// </summary>
        public EventRecord NewRecord { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="newRecord">新增的事件记录</param>
        public EventRecordChangedEventArgs(EventRecord newRecord)
        {
            NewRecord = newRecord;
        }
    }
}
