using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Contracts.Events;

namespace Services
{
    /// <summary>
    /// 事件聚合器实现
    /// </summary>
    /// <remarks>
    /// 提供线程安全的事件发布订阅机制，支持弱引用防止内存泄漏，
    /// 自动处理UI线程调度
    /// </remarks>
    public class EventAggregator : IEventAggregator
    {
        private readonly ConcurrentDictionary<Type, object> _events = new ConcurrentDictionary<Type, object>();

        /// <summary>
        /// 获取指定类型的事件
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <returns>事件对象</returns>
        public IEvent<T> GetEvent<T>() where T : class
        {
            return (IEvent<T>)_events.GetOrAdd(typeof(T), _ => new Event<T>());
        }
    }

    /// <summary>
    /// 事件实现
    /// </summary>
    /// <typeparam name="T">事件数据类型</typeparam>
    /// <remarks>
    /// 使用弱引用存储事件处理器，防止内存泄漏
    /// </remarks>
    internal class Event<T> : IEvent<T> where T : class
    {
        private readonly List<WeakEventSubscription> _subscriptions = new List<WeakEventSubscription>();
        private readonly object _lock = new object();

        /// <summary>
        /// 发布事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        public void Publish(T eventData)
        {
            if (eventData == null) return;

            List<WeakEventSubscription> subscriptionsToCall;

            lock (_lock)
            {
                // 清理已被回收的弱引用
                _subscriptions.RemoveAll(s => !s.IsAlive);

                // 获取所有有效的订阅
                subscriptionsToCall = _subscriptions
                    .Where(s => s.IsAlive)
                    .ToList();
            }

            // 调用事件处理器
            foreach (var subscription in subscriptionsToCall)
            {
                try
                {
                    subscription.Invoke(eventData);
                }
                catch (Exception)
                {
                    // 忽略处理器中的异常，避免影响其他订阅者
                    // 在实际应用中可以记录日志
                }
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <param name="threadOption">线程选项</param>
        /// <param name="keepSubscriberReferenceAlive">是否保持订阅者引用</param>
        public void Subscribe(Action<T> handler, ThreadOption threadOption = ThreadOption.UIThread, bool keepSubscriberReferenceAlive = false)
        {
            if (handler == null) return;

            lock (_lock)
            {
                var subscription = new WeakEventSubscription(handler, threadOption, keepSubscriberReferenceAlive);
                _subscriptions.Add(subscription);
            }
        }

        /// <summary>
        /// 取消订阅
        /// </summary>
        /// <param name="handler">事件处理器</param>
        public void Unsubscribe(Action<T> handler)
        {
            if (handler == null) return;

            lock (_lock)
            {
                _subscriptions.RemoveAll(s =>
                    !s.IsAlive || ReferenceEquals(s.Handler?.Target, handler));
            }
        }

        /// <summary>
        /// 弱事件订阅
        /// </summary>
        private class WeakEventSubscription
        {
            public WeakReference Handler { get; private set; }
            public ThreadOption ThreadOption { get; private set; }
            public bool KeepAlive { get; private set; }

            public WeakEventSubscription(Action<T> handler, ThreadOption threadOption, bool keepAlive)
            {
                Handler = new WeakReference(handler);
                ThreadOption = threadOption;
                KeepAlive = keepAlive;
            }

            public bool IsAlive => Handler.IsAlive;

            public void Invoke(T eventData)
            {
                var handler = Handler.Target as Action<T>;
                if (handler == null) return;

                switch (ThreadOption)
                {
                    case ThreadOption.UIThread:
                        InvokeOnUIThread(() => handler(eventData));
                        break;
                    case ThreadOption.BackgroundThread:
                        Task.Run(() => handler(eventData));
                        break;
                    case ThreadOption.PublisherThread:
                    default:
                        handler(eventData);
                        break;
                }
            }

            private void InvokeOnUIThread(Action action)
            {
                if (Application.OpenForms.Count > 0)
                {
                    var mainForm = Application.OpenForms[0];
                    if (mainForm.InvokeRequired)
                    {
                        mainForm.BeginInvoke(action);
                    }
                    else
                    {
                        action();
                    }
                }
                else
                {
                    // 如果没有UI窗体，直接在当前线程执行
                    action();
                }
            }
        }
    }
}
