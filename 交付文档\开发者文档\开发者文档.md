# 工业HMI框架开发者文档

## 版本信息

- **软件版本**: 1.0.0
- **文档版本**: 1.0.0
- **发布日期**: 2025-08-13
- **目标读者**: 开发人员、架构师

## 目录

1. [架构概述](#架构概述)
2. [开发环境搭建](#开发环境搭建)
3. [核心接口](#核心接口)
4. [模块开发指南](#模块开发指南)
5. [事件系统](#事件系统)
6. [性能优化](#性能优化)
7. [测试指南](#测试指南)
8. [部署和发布](#部署和发布)

## 架构概述

### 系统架构

工业HMI框架采用模块化MVP（Model-View-Presenter）架构，支持动态模块加载和事件驱动通信。

```
┌─────────────────────────────────────────────────────────┐
│                    Shell (主程序)                        │
├─────────────────────────────────────────────────────────┤
│                  Services (核心服务)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │ModuleLoader │  │EventAggregator│  │Logger       │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                  Contracts (接口层)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │IModule      │  │IEventAggregator│  │ILogger      │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                    Modules (功能模块)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │DeviceModule │  │AlarmModule  │  │TestModule   │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 核心组件

**Shell (主程序)**:

- 应用程序入口点
- 模块容器管理
- 主窗口和UI框架

**Services (核心服务)**:

- ModuleLoader: 动态模块加载器
- EventAggregator: 事件聚合器
- SerilogLogger: 日志记录服务

**Contracts (接口层)**:

- IModule: 模块基础接口
- IEventAggregator: 事件聚合器接口
- ILogger: 日志记录接口

**Modules (功能模块)**:

- 独立的功能单元
- 实现IModule接口
- 支持热插拔

### 设计原则

1. **单一职责**: 每个模块负责单一功能领域
2. **开放封闭**: 对扩展开放，对修改封闭
3. **依赖倒置**: 依赖抽象而非具体实现
4. **接口隔离**: 使用最小化接口
5. **松耦合**: 通过事件系统实现模块间通信

## 开发环境搭建

### 必需软件

**开发工具**:

- Visual Studio 2019/2022 Community 或更高版本
- .NET Framework 4.8 SDK
- Git for Windows

**可选工具**:

- ReSharper (代码质量工具)
- NuGet Package Manager
- PowerShell 5.1+

### 项目结构

```
IndustrialHMI/
├── Shell/                      # 主程序项目
│   ├── Program.cs              # 程序入口
│   ├── MainForm.cs             # 主窗口
│   └── Shell.csproj
├── Contracts/                  # 接口定义项目
│   ├── IModule.cs              # 模块接口
│   ├── IEventAggregator.cs     # 事件聚合器接口
│   ├── ILogger.cs              # 日志接口
│   └── Contracts.csproj
├── Services/                   # 核心服务项目
│   ├── ModuleLoader.cs         # 模块加载器
│   ├── EventAggregator.cs      # 事件聚合器实现
│   ├── SerilogLogger.cs        # 日志实现
│   └── Services.csproj
├── Modules.Sources/            # 模块源代码
│   ├── DeviceModule/           # 设备监控模块
│   ├── AlarmModule/            # 报警管理模块
│   └── CommunicationTestModule/ # 通信测试模块
├── Tests/                      # 测试项目
└── IndustrialHMI.sln          # 解决方案文件
```

### 编译配置

**Debug配置**:

- 输出路径: `bin\Debug\`
- 模块输出: `bin\Debug\Modules\`
- 日志级别: Debug
- 符号调试: 启用

**Release配置**:

- 输出路径: `bin\Release\`
- 模块输出: `bin\Release\Modules\`
- 日志级别: Information
- 代码优化: 启用

## 核心接口

### IModule接口

```csharp
/// <summary>
/// 模块基础接口
/// </summary>
public interface IModule : IDisposable
{
    /// <summary>
    /// 模块名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 模块描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 事件聚合器（由框架注入）
    /// </summary>
    IEventAggregator EventAggregator { get; set; }

    /// <summary>
    /// 日志记录器（由框架注入）
    /// </summary>
    ILogger Logger { get; set; }

    /// <summary>
    /// 初始化模块
    /// </summary>
    void Initialize();

    /// <summary>
    /// 启动模块
    /// </summary>
    void Start();

    /// <summary>
    /// 停止模块
    /// </summary>
    void Stop();

    /// <summary>
    /// 获取用户控件
    /// </summary>
    /// <returns>用户控件实例</returns>
    UserControl GetUserControl();
}
```

### IEventAggregator接口

```csharp
/// <summary>
/// 事件聚合器接口
/// </summary>
public interface IEventAggregator
{
    /// <summary>
    /// 发布事件
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    /// <param name="eventObj">事件对象</param>
    void Publish<T>(T eventObj);

    /// <summary>
    /// 订阅事件
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    /// <param name="handler">事件处理器</param>
    /// <param name="keepSubscriberReferenceAlive">是否保持订阅者引用</param>
    void Subscribe<T>(Action<T> handler, bool keepSubscriberReferenceAlive = true);

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <typeparam name="T">事件类型</typeparam>
    /// <param name="handler">事件处理器</param>
    void Unsubscribe<T>(Action<T> handler);
}
```

### ILogger接口

```csharp
/// <summary>
/// 日志记录器接口
/// </summary>
public interface ILogger
{
    /// <summary>
    /// 记录调试信息
    /// </summary>
    void Debug(string message);
    void Debug(string message, Exception exception);

    /// <summary>
    /// 记录一般信息
    /// </summary>
    void Info(string message);
    void Info(string message, Exception exception);

    /// <summary>
    /// 记录警告信息
    /// </summary>
    void Warning(string message);
    void Warning(string message, Exception exception);

    /// <summary>
    /// 记录错误信息
    /// </summary>
    void Error(string message);
    void Error(string message, Exception exception);

    /// <summary>
    /// 记录致命错误
    /// </summary>
    void Fatal(string message);
    void Fatal(string message, Exception exception);
}
```

## 模块开发指南

### 创建新模块

**步骤1: 创建模块项目**

```xml
<!-- MyModule.csproj -->
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <OutputPath>..\..\bin\Debug\Modules\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Contracts\Contracts.csproj" />
  </ItemGroup>
</Project>
```

**步骤2: 实现模块主类**

```csharp
/// <summary>
/// 我的模块
/// </summary>
public class MyModule : IModule
{
    public string Name => "我的模块";
    public string Description => "模块功能描述";
    
    public IEventAggregator EventAggregator { get; set; }
    public ILogger Logger { get; set; }

    private MyView _view;
    private MyPresenter _presenter;
    private MyModel _model;

    public void Initialize()
    {
        Logger?.Info($"{Name} 开始初始化");
        
        // 创建MVP组件
        _model = new MyModel(EventAggregator, Logger);
        _view = new MyView();
        _presenter = new MyPresenter(_view, _model, EventAggregator, Logger);
        
        Logger?.Info($"{Name} 初始化完成");
    }

    public void Start()
    {
        Logger?.Info($"{Name} 启动");
        _presenter?.Start();
    }

    public void Stop()
    {
        Logger?.Info($"{Name} 停止");
        _presenter?.Stop();
    }

    public UserControl GetUserControl()
    {
        return _view;
    }

    public void Dispose()
    {
        _presenter?.Dispose();
        _model?.Dispose();
        _view?.Dispose();
    }
}
```

**步骤3: 实现MVP组件**

*Model (数据模型)*:

```csharp
public class MyModel : IDisposable
{
    private readonly IEventAggregator _eventAggregator;
    private readonly ILogger _logger;

    public MyModel(IEventAggregator eventAggregator, ILogger logger)
    {
        _eventAggregator = eventAggregator;
        _logger = logger;
        
        // 订阅相关事件
        _eventAggregator?.Subscribe<SomeEvent>(OnSomeEvent, false);
    }

    private void OnSomeEvent(SomeEvent eventObj)
    {
        // 处理事件
    }

    public void Dispose()
    {
        // 取消事件订阅
        _eventAggregator?.Unsubscribe<SomeEvent>(OnSomeEvent);
    }
}
```

*View (用户界面)*:

```csharp
public partial class MyView : UserControl, IMyView
{
    public event Action<string> SomeAction;

    public MyView()
    {
        InitializeComponent();
    }

    public void UpdateData(string data)
    {
        // 更新UI
        if (InvokeRequired)
        {
            Invoke(new Action<string>(UpdateData), data);
            return;
        }
        
        // 实际更新UI代码
    }
}
```

*Presenter (表示器)*:

```csharp
public class MyPresenter : IDisposable
{
    private readonly IMyView _view;
    private readonly MyModel _model;
    private readonly IEventAggregator _eventAggregator;
    private readonly ILogger _logger;

    public MyPresenter(IMyView view, MyModel model, 
                      IEventAggregator eventAggregator, ILogger logger)
    {
        _view = view;
        _model = model;
        _eventAggregator = eventAggregator;
        _logger = logger;

        // 绑定视图事件
        _view.SomeAction += OnSomeAction;
    }

    public void Start()
    {
        // 启动逻辑
    }

    public void Stop()
    {
        // 停止逻辑
    }

    private void OnSomeAction(string parameter)
    {
        // 处理视图事件
    }

    public void Dispose()
    {
        _view.SomeAction -= OnSomeAction;
    }
}
```

### 模块开发最佳实践

1. **依赖注入验证**

```csharp
public void Initialize()
{
    if (EventAggregator == null)
        throw new InvalidOperationException("EventAggregator未注入");
    if (Logger == null)
        throw new InvalidOperationException("Logger未注入");
}
```

2. **异常处理**

```csharp
public void SomeMethod()
{
    try
    {
        // 业务逻辑
    }
    catch (Exception ex)
    {
        Logger?.Error($"操作失败: {ex.Message}", ex);
        // 不要让异常传播到框架
    }
}
```

3. **UI线程安全**

```csharp
private void SafeUpdateUI(Action action)
{
    if (_view.InvokeRequired)
    {
        _view.Invoke(action);
    }
    else
    {
        action();
    }
}
```

4. **资源释放**

```csharp
public void Dispose()
{
    // 取消事件订阅
    EventAggregator?.Unsubscribe<SomeEvent>(OnSomeEvent);
    
    // 释放其他资源
    _timer?.Dispose();
    _httpClient?.Dispose();
}
```

## 事件系统

### 事件定义

```csharp
/// <summary>
/// 设备连接事件
/// </summary>
public class DeviceConnectionEvent
{
    public string DeviceId { get; set; }
    public bool IsConnected { get; set; }
    public DateTime Timestamp { get; set; }
    public string Message { get; set; }
}
```

### 事件发布

```csharp
// 发布事件
var connectionEvent = new DeviceConnectionEvent
{
    DeviceId = "Device001",
    IsConnected = true,
    Timestamp = DateTime.Now,
    Message = "设备连接成功"
};

EventAggregator?.Publish(connectionEvent);
```

### 事件订阅

```csharp
// 订阅事件
EventAggregator?.Subscribe<DeviceConnectionEvent>(OnDeviceConnection, false);

private void OnDeviceConnection(DeviceConnectionEvent eventObj)
{
    Logger?.Info($"设备 {eventObj.DeviceId} 连接状态: {eventObj.IsConnected}");
    
    // 更新UI
    SafeUpdateUI(() => {
        // UI更新代码
    });
}
```

### 事件最佳实践

1. **事件命名**: 使用描述性名称，以Event结尾
2. **事件数据**: 包含足够的上下文信息
3. **订阅管理**: 设置keepSubscriberReferenceAlive为false
4. **异常处理**: 事件处理器中捕获所有异常
5. **UI更新**: 使用SafeUpdateUI确保线程安全

## 性能优化

### 启动优化

1. **延迟初始化**

```csharp
private MyExpensiveObject _expensiveObject;
public MyExpensiveObject ExpensiveObject
{
    get
    {
        if (_expensiveObject == null)
        {
            _expensiveObject = new MyExpensiveObject();
        }
        return _expensiveObject;
    }
}
```

2. **异步加载**

```csharp
public async void Initialize()
{
    // 同步的关键初始化
    InitializeCriticalComponents();
    
    // 异步的非关键初始化
    await Task.Run(() => InitializeNonCriticalComponents());
}
```

### 内存优化

1. **及时释放资源**

```csharp
public void Dispose()
{
    _timer?.Stop();
    _timer?.Dispose();
    _httpClient?.Dispose();
    
    // 清理事件订阅
    EventAggregator?.Unsubscribe<SomeEvent>(OnSomeEvent);
}
```

2. **避免内存泄漏**

```csharp
// 使用弱引用避免循环引用
private readonly WeakReference<IView> _viewRef;

// 及时取消事件订阅
_view.SomeEvent -= OnSomeEvent;
```

### UI性能优化

1. **虚拟化大数据集**

```csharp
// 使用分页或虚拟化控件
dataGridView.VirtualMode = true;
dataGridView.CellValueNeeded += OnCellValueNeeded;
```

2. **批量UI更新**

```csharp
private void BatchUpdateUI(List<UpdateItem> updates)
{
    listView.BeginUpdate();
    try
    {
        foreach (var update in updates)
        {
            // 更新UI项
        }
    }
    finally
    {
        listView.EndUpdate();
    }
}
```

## 测试指南

### 单元测试

使用项目自带的SimpleTestFramework进行单元测试：

```csharp
[TestClass("我的模块测试")]
public class MyModuleTests
{
    private MyModule _module;
    private MockEventAggregator _eventAggregator;
    private MockLogger _logger;

    [TestInitialize]
    public void Setup()
    {
        _eventAggregator = new MockEventAggregator();
        _logger = new MockLogger();
        _module = new MyModule();
        _module.EventAggregator = _eventAggregator;
        _module.Logger = _logger;
    }

    [TestMethod("模块初始化测试", "验证模块能够正确初始化")]
    public void Initialize_ShouldWork()
    {
        // Act
        _module.Initialize();

        // Assert
        Assert.IsNotNull(_module.GetUserControl());
    }

    [TestCleanup]
    public void Cleanup()
    {
        _module?.Dispose();
    }
}
```

### 集成测试

```csharp
[TestMethod("模块间通信测试", "验证模块间事件通信")]
public void ModuleCommunication_ShouldWork()
{
    // Arrange
    var module1 = new Module1();
    var module2 = new Module2();
    var eventAggregator = new EventAggregator();
    
    module1.EventAggregator = eventAggregator;
    module2.EventAggregator = eventAggregator;
    
    module1.Initialize();
    module2.Initialize();

    // Act
    module1.TriggerSomeAction();

    // Assert
    // 验证module2收到了相应的事件
}
```

### 性能测试

```csharp
[TestMethod("启动性能测试", "验证模块启动时间")]
public void StartupPerformance_ShouldBeFast()
{
    // Arrange
    var stopwatch = Stopwatch.StartNew();

    // Act
    _module.Initialize();
    _module.Start();
    
    stopwatch.Stop();

    // Assert
    Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000, 
                 $"启动时间过长: {stopwatch.ElapsedMilliseconds}ms");
}
```

## 配置管理系统

### 概述

工业HMI框架提供了企业级的配置管理系统，支持多配置源、热更新、类型安全等特性。

### 配置服务接口

```csharp
public interface IConfigurationService
{
    // 基本配置读取
    string GetString(string key, string defaultValue = "");
    int GetInt(string key, int defaultValue = 0);
    bool GetBool(string key, bool defaultValue = false);
    double GetDouble(string key, double defaultValue = 0.0);
    T GetValue<T>(string key, T defaultValue = default(T));

    // 配置管理
    void SetValue(string key, object value);
    bool ContainsKey(string key);
    bool RemoveKey(string key);

    // 配置源管理
    void AddSource(IConfigurationSource source);
    void RemoveSource(IConfigurationSource source);
    IEnumerable<IConfigurationSource> GetSources();

    // 热更新
    void EnableHotReload();
    void DisableHotReload();
    bool IsHotReloadEnabled { get; }

    // 事件
    event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
}
```

### 配置源类型

#### 1. 文件配置源

```csharp
// 添加XML文件配置源
var fileSource = new FileConfigurationSource(
    filePath: "config/application.config",
    priority: 50,
    canWrite: true,
    name: "CustomConfig"
);
configService.AddSource(fileSource);
```

#### 2. 环境变量配置源

```csharp
// 添加环境变量配置源
var envSource = new EnvironmentConfigurationSource(
    prefix: "INDUSTRIAL_HMI",
    priority: 100,
    removePrefix: true,
    name: "Environment"
);
configService.AddSource(envSource);
```

### 配置优先级

配置源按优先级从高到低读取：

1. **环境变量** (优先级: 100)
2. **自定义配置文件** (优先级: 50)
3. **默认配置文件** (优先级: 10)

### 使用示例

#### 基本配置读取

```csharp
public class MyModule : IModule
{
    public IConfigurationService ConfigService { get; set; }

    public void Initialize()
    {
        // 读取配置
        var logLevel = ConfigService.GetString("LogLevel", "Info");
        var maxConnections = ConfigService.GetInt("MaxConnections", 10);
        var enableFeature = ConfigService.GetBool("EnableAdvancedFeatures", false);

        // 读取配置节
        var dbSettings = ConfigService.GetSection("database");
        foreach (var setting in dbSettings)
        {
            Console.WriteLine($"{setting.Key} = {setting.Value}");
        }
    }
}
```

#### 配置热更新

```csharp
public void SetupConfigurationMonitoring()
{
    // 启用热更新
    ConfigService.EnableHotReload();

    // 监听配置变更
    ConfigService.ConfigurationChanged += OnConfigurationChanged;
}

private void OnConfigurationChanged(object sender, ConfigurationChangedEventArgs e)
{
    Logger.Info($"配置已更新: {e.Key} = {e.NewValue} (来源: {e.SourceName})");

    // 根据配置变更调整行为
    if (e.Key == "LogLevel")
    {
        UpdateLogLevel(e.NewValue?.ToString());
    }
}
```

### 配置文件格式

#### XML配置文件示例

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="LogLevel" value="Debug" />
    <add key="MaxModules" value="10" />
    <add key="EnableAdvancedFeatures" value="true" />
  </appSettings>

  <!-- 自定义配置节 -->
  <database>
    <connectionString value="Server=localhost;Database=HMI" />
    <timeout value="30" />
  </database>

  <performance>
    <monitoring enabled="true" interval="5000" />
    <cache maxSize="1000" timeout="300" />
  </performance>
</configuration>
```

#### 环境变量示例

```bash
# 设置环境变量（Windows）
set INDUSTRIAL_HMI_LogLevel=Debug
set INDUSTRIAL_HMI_MaxConnections=20
set INDUSTRIAL_HMI_Database_Host=production-server

# 设置环境变量（Linux/Mac）
export INDUSTRIAL_HMI_LogLevel=Debug
export INDUSTRIAL_HMI_MaxConnections=20
export INDUSTRIAL_HMI_Database_Host=production-server
```

### 最佳实践

1. **配置键命名**
   - 使用PascalCase命名
   - 使用点号分隔层次: `Database.ConnectionString`
   - 避免特殊字符

2. **默认值处理**

   ```csharp
   // 总是提供合理的默认值
   var timeout = ConfigService.GetInt("RequestTimeout", 30000);
   var retryCount = ConfigService.GetInt("RetryCount", 3);
   ```

3. **配置验证**

   ```csharp
   public void ValidateConfiguration()
   {
       var connectionString = ConfigService.GetString("Database.ConnectionString");
       if (string.IsNullOrEmpty(connectionString))
       {
           throw new ConfigurationException("数据库连接字符串未配置");
       }
   }
   ```

4. **配置缓存**

   ```csharp
   // 对于频繁访问的配置，考虑缓存
   private readonly int _maxConnections;

   public MyService(IConfigurationService configService)
   {
       _maxConnections = configService.GetInt("MaxConnections", 10);
   }
   ```

## 部署和发布

### 构建脚本

```powershell
# Build.ps1
param(
    [string]$Configuration = "Release"
)

# 清理输出目录
Remove-Item "bin\$Configuration" -Recurse -Force -ErrorAction SilentlyContinue

# 构建解决方案
& "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" `
  "IndustrialHMI.sln" /p:Configuration=$Configuration

# 复制依赖文件
Copy-Item "packages\System.Diagnostics.DiagnosticSource.7.0.2\lib\net462\*.dll" "bin\$Configuration\"

Write-Host "构建完成: bin\$Configuration"
```

### 发布检查清单

- [ ] 所有项目编译成功，无警告
- [ ] 单元测试全部通过
- [ ] 系统验收测试通过
- [ ] 性能测试达标
- [ ] 依赖文件完整
- [ ] 配置文件正确
- [ ] 文档更新完成
- [ ] 版本号更新

### 版本管理

使用语义化版本控制：

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

示例: `1.2.3`

- 1: 主版本
- 2: 次版本  
- 3: 修订版本

---

**注意事项**:

- 严格遵循接口契约
- 保持向下兼容性
- 充分测试模块间交互
- 及时更新文档

## 附录

### 常用代码模板

**模块模板**: 参考`Modules.Sources/DeviceModule`
**测试模板**: 参考`Tests/DeviceModule.Tests`
**事件模板**: 参考`Contracts/Events`

### 相关文档

- [用户操作手册](../用户手册/用户操作手册.md)
- [部署指南](../部署指南/部署指南.md)
- [API参考文档](./API文档.md)
