using System;
using System.Threading.Tasks;
using Contracts;
using CommunicationTestModule.Models;
using CommunicationTestModule.Services;
using CommunicationTestModule.Views;

namespace CommunicationTestModule.Presenters
{
    /// <summary>
    /// 通信测试表示器
    /// </summary>
    /// <remarks>
    /// 协调视图和模型之间的交互，处理用户操作和数据更新
    /// </remarks>
    public class CommunicationTestPresenter : IPresenter, IDisposable
    {
        private readonly CommunicationTestView _view;
        private readonly CommunicationTestModel _model;
        private readonly ILogger _logger;
        private bool _disposed = false;

        /// <summary>
        /// 视图属性
        /// </summary>
        public IView View { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="view">视图</param>
        /// <param name="model">模型</param>
        /// <param name="logger">日志记录器</param>
        public CommunicationTestPresenter(CommunicationTestView view, CommunicationTestModel model, ILogger logger)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _model = model ?? throw new ArgumentNullException(nameof(model));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 设置IPresenter.View属性
            View = _view;

            InitializePresenter();
            _logger.Info("CommunicationTestPresenter 初始化完成");
        }

        /// <summary>
        /// 初始化表示器（IPresenter接口方法）
        /// </summary>
        public void Initialize()
        {
            InitializePresenter();
        }

        /// <summary>
        /// 加载数据（IPresenter接口方法）
        /// </summary>
        public void LoadData()
        {
            InitializeViewData();
        }

        /// <summary>
        /// 保存数据（IPresenter接口方法）
        /// </summary>
        /// <returns>保存是否成功</returns>
        public bool SaveData()
        {
            // 通信测试模块不需要保存数据
            return true;
        }

        /// <summary>
        /// 验证数据（IPresenter接口方法）
        /// </summary>
        /// <returns>验证结果</returns>
        public bool ValidateData()
        {
            return true; // 通信测试模块不需要验证数据
        }

        /// <summary>
        /// 初始化表示器
        /// </summary>
        private void InitializePresenter()
        {
            // 订阅视图事件
            _view.EventMonitorActionRequested += OnEventMonitorActionRequested;
            _view.TestExecutionActionRequested += OnTestExecutionActionRequested;
            _view.PerformanceMonitorActionRequested += OnPerformanceMonitorActionRequested;
            _view.ResultActionRequested += OnResultActionRequested;

            // 订阅模型事件
            _model.EventRecordChanged += OnEventRecordChanged;
            _model.TestProgressChanged += OnTestProgressChanged;
            _model.TestCompleted += OnTestCompleted;
            _model.PerformanceDataUpdated += OnPerformanceDataUpdated;
            _model.ModelDataChanged += OnModelDataChanged;

            // 初始化视图数据
            InitializeViewData();
        }

        /// <summary>
        /// 初始化视图数据
        /// </summary>
        private void InitializeViewData()
        {
            try
            {
                // 加载测试用例
                var testCases = _model.GetTestCases();
                _view.UpdateTestCases(testCases);

                // 加载测试分类
                var categories = _model.GetTestCategories();
                _view.UpdateTestCategories(categories);

                // 初始化状态
                _view.UpdateEventMonitoringStatus(false);
                _view.UpdatePerformanceMonitoringStatus(false);
                _view.UpdateTestExecutionStatus(false);
                _view.UpdateTestProgress(0, "就绪");

                _logger.Debug("视图数据初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error("初始化视图数据时发生错误", ex);
                _view.ShowMessage($"初始化失败: {ex.Message}", "错误", System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        // 视图事件处理方法
        private void OnEventMonitorActionRequested(object sender, EventMonitorActionEventArgs e)
        {
            try
            {
                _logger.Debug($"处理事件监控操作: {e.Action}");

                switch (e.Action)
                {
                    case "Start":
                        _model.StartEventMonitoring();
                        _view.UpdateEventMonitoringStatus(true);
                        _view.ShowMessage("事件监控已启动", "信息");
                        break;

                    case "Stop":
                        _model.StopEventMonitoring();
                        _view.UpdateEventMonitoringStatus(false);
                        _view.ShowMessage("事件监控已停止", "信息");
                        break;

                    case "Clear":
                        _model.ClearEventRecords();
                        _view.UpdateEventRecords(null);
                        _view.ShowMessage("事件记录已清除", "信息");
                        break;

                    default:
                        _logger.Warning($"未知的事件监控操作: {e.Action}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"处理事件监控操作时发生错误: {e.Action}", ex);
                _view.ShowMessage($"操作失败: {ex.Message}", "错误", System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        private async void OnTestExecutionActionRequested(object sender, TestExecutionActionEventArgs e)
        {
            try
            {
                _logger.Debug($"处理测试执行操作: {e.Action}");

                switch (e.Action)
                {
                    case "RunAll":
                        _view.UpdateTestExecutionStatus(true);
                        _view.UpdateTestProgress(0, "开始运行所有测试...");
                        await Task.Run(() => _model.RunAllTests());
                        break;

                    case "RunCategory":
                        if (!string.IsNullOrEmpty(e.Parameter))
                        {
                            _view.UpdateTestExecutionStatus(true);
                            _view.UpdateTestProgress(0, $"开始运行分类测试: {e.Parameter}...");
                            await Task.Run(() => _model.RunTestsByCategory(e.Parameter));
                        }
                        else
                        {
                            _view.ShowMessage("请选择测试分类", "提示", System.Windows.Forms.MessageBoxIcon.Warning);
                        }
                        break;

                    case "Stop":
                        _model.StopTests();
                        _view.UpdateTestExecutionStatus(false);
                        _view.UpdateTestProgress(0, "测试已停止");
                        _view.ShowMessage("测试已停止", "信息");
                        break;

                    default:
                        _logger.Warning($"未知的测试执行操作: {e.Action}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"处理测试执行操作时发生错误: {e.Action}", ex);
                _view.ShowMessage($"操作失败: {ex.Message}", "错误", System.Windows.Forms.MessageBoxIcon.Error);
                _view.UpdateTestExecutionStatus(false);
            }
        }

        private void OnPerformanceMonitorActionRequested(object sender, PerformanceMonitorActionEventArgs e)
        {
            try
            {
                _logger.Debug($"处理性能监控操作: {e.Action}");

                switch (e.Action)
                {
                    case "Start":
                        _model.StartPerformanceMonitoring(1000); // 1秒间隔
                        _view.UpdatePerformanceMonitoringStatus(true);
                        _view.ShowMessage("性能监控已启动", "信息");
                        break;

                    case "Stop":
                        _model.StopPerformanceMonitoring();
                        _view.UpdatePerformanceMonitoringStatus(false);
                        _view.ShowMessage("性能监控已停止", "信息");
                        break;

                    case "Clear":
                        _model.ClearPerformanceData();
                        _view.UpdatePerformanceData(null);
                        _view.ShowMessage("性能数据已清除", "信息");
                        break;

                    default:
                        _logger.Warning($"未知的性能监控操作: {e.Action}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"处理性能监控操作时发生错误: {e.Action}", ex);
                _view.ShowMessage($"操作失败: {ex.Message}", "错误", System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        private void OnResultActionRequested(object sender, ResultActionEventArgs e)
        {
            try
            {
                _logger.Debug($"处理结果操作: {e.Action}");

                switch (e.Action)
                {
                    case "GenerateReport":
                        var report = _model.GenerateTestReport();
                        _view.UpdateTestSummary(report.TestSummary);
                        _view.ShowMessage("测试报告已生成", "信息");
                        break;

                    case "ClearResults":
                        _model.ClearTestResults();
                        _view.UpdateTestResults(null);
                        _view.UpdateTestSummary(null);
                        _view.ShowMessage("测试结果已清除", "信息");
                        break;

                    default:
                        _logger.Warning($"未知的结果操作: {e.Action}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"处理结果操作时发生错误: {e.Action}", ex);
                _view.ShowMessage($"操作失败: {ex.Message}", "错误", System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        // 模型事件处理方法
        private void OnEventRecordChanged(object sender, EventRecordChangedEventArgs e)
        {
            try
            {
                // 更新事件记录列表
                var eventRecords = _model.GetEventRecords(100);
                _view.UpdateEventRecords(eventRecords);

                // 更新事件统计
                var eventStats = _model.GetEventStatistics();
                _view.UpdateEventStatistics(eventStats);
            }
            catch (Exception ex)
            {
                _logger.Error("处理事件记录变化时发生错误", ex);
            }
        }

        private void OnTestProgressChanged(object sender, TestProgressEventArgs e)
        {
            try
            {
                _view.UpdateTestProgress(e.ProgressPercentage, e.CurrentTestName);
            }
            catch (Exception ex)
            {
                _logger.Error("处理测试进度变化时发生错误", ex);
            }
        }

        private void OnTestCompleted(object sender, TestCompletedEventArgs e)
        {
            try
            {
                _view.UpdateTestExecutionStatus(false);
                _view.UpdateTestProgress(100, "测试完成");

                // 更新测试结果
                var testResults = _model.GetTestResults();
                _view.UpdateTestResults(testResults);

                // 更新测试摘要
                _view.UpdateTestSummary(e.Summary);

                // 显示完成消息
                var message = $"测试完成！\n" +
                             $"总数: {e.Summary.TotalTests}\n" +
                             $"通过: {e.Summary.PassedTests}\n" +
                             $"失败: {e.Summary.FailedTests}\n" +
                             $"成功率: {e.Summary.SuccessRate:F1}%";

                _view.ShowMessage(message, "测试完成");
            }
            catch (Exception ex)
            {
                _logger.Error("处理测试完成时发生错误", ex);
            }
        }

        private void OnPerformanceDataUpdated(object sender, PerformanceDataEventArgs e)
        {
            try
            {
                // 更新性能数据列表
                var snapshots = _model.GetPerformanceSnapshots(50);
                _view.UpdatePerformanceData(snapshots);

                // 更新性能统计
                var perfStats = _model.GetPerformanceStatistics();
                _view.UpdatePerformanceStatistics(perfStats);
            }
            catch (Exception ex)
            {
                _logger.Error("处理性能数据更新时发生错误", ex);
            }
        }

        private void OnModelDataChanged(object sender, ModelDataChangedEventArgs e)
        {
            try
            {
                _logger.Debug($"模型数据变化: {e.PropertyName}");

                // 根据变化的属性更新相应的视图
                switch (e.PropertyName)
                {
                    case "EventMonitoring":
                        _view.UpdateEventMonitoringStatus((bool)e.Value);
                        break;
                    case "PerformanceMonitoring":
                        _view.UpdatePerformanceMonitoringStatus((bool)e.Value);
                        break;
                    case "EventRecords":
                        _view.UpdateEventRecords(null);
                        break;
                    case "TestResults":
                        _view.UpdateTestResults(null);
                        break;
                    case "PerformanceData":
                        _view.UpdatePerformanceData(null);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"处理模型数据变化时发生错误: {e.PropertyName}", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 取消视图事件订阅
                if (_view != null)
                {
                    _view.EventMonitorActionRequested -= OnEventMonitorActionRequested;
                    _view.TestExecutionActionRequested -= OnTestExecutionActionRequested;
                    _view.PerformanceMonitorActionRequested -= OnPerformanceMonitorActionRequested;
                    _view.ResultActionRequested -= OnResultActionRequested;
                }

                // 取消模型事件订阅
                if (_model != null)
                {
                    _model.EventRecordChanged -= OnEventRecordChanged;
                    _model.TestProgressChanged -= OnTestProgressChanged;
                    _model.TestCompleted -= OnTestCompleted;
                    _model.PerformanceDataUpdated -= OnPerformanceDataUpdated;
                    _model.ModelDataChanged -= OnModelDataChanged;
                }

                _logger?.Debug("CommunicationTestPresenter 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 CommunicationTestPresenter 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
