using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Shell.Bootstrapper;
using Contracts;
using Services;

namespace ContainerTests
{
    /// <summary>
    /// SimpleContainer测试类
    /// </summary>
    [TestClass]
    public class SimpleContainerTests
    {
        private SimpleContainer _container;

        /// <summary>
        /// 测试初始化
        /// </summary>
        [TestInitialize]
        public void TestInitialize()
        {
            _container = new SimpleContainer();
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            _container?.Dispose();
        }

        /// <summary>
        /// 测试注册和解析单例服务实例
        /// </summary>
        [TestMethod]
        public void RegisterInstance_ShouldResolveCorrectly()
        {
            // Arrange
            var logger = new SerilogLogger();

            // Act
            _container.RegisterInstance<ILogger>(logger, Reuse.Singleton);
            var resolvedLogger = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(resolvedLogger);
            Assert.AreSame(logger, resolvedLogger);
        }

        /// <summary>
        /// 测试注册和解析服务类型
        /// </summary>
        [TestMethod]
        public void Register_ShouldCreateInstance()
        {
            // Act
            _container.Register<ILogger, SerilogLogger>(Reuse.Singleton);
            var logger = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(logger);
            Assert.IsInstanceOfType(logger, typeof(SerilogLogger));
        }

        /// <summary>
        /// 测试单例生命周期
        /// </summary>
        [TestMethod]
        public void Register_Singleton_ShouldReturnSameInstance()
        {
            // Act
            _container.Register<ILogger, SerilogLogger>(Reuse.Singleton);
            var logger1 = _container.Resolve<ILogger>();
            var logger2 = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(logger1);
            Assert.IsNotNull(logger2);
            Assert.AreSame(logger1, logger2);
        }

        /// <summary>
        /// 测试瞬态生命周期
        /// </summary>
        [TestMethod]
        public void Register_Transient_ShouldReturnDifferentInstances()
        {
            // Act
            _container.Register<ILogger, SerilogLogger>(Reuse.Transient);
            var logger1 = _container.Resolve<ILogger>();
            var logger2 = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNotNull(logger1);
            Assert.IsNotNull(logger2);
            Assert.AreNotSame(logger1, logger2);
        }

        /// <summary>
        /// 测试解析不存在的服务
        /// </summary>
        [TestMethod]
        public void Resolve_NonExistentService_ShouldReturnNull()
        {
            // Act
            var service = _container.Resolve<ILogger>();

            // Assert
            Assert.IsNull(service);
        }

        /// <summary>
        /// 测试按类型注册和解析
        /// </summary>
        [TestMethod]
        public void RegisterInstance_ByType_ShouldResolveCorrectly()
        {
            // Arrange
            var logger = new SerilogLogger();

            // Act
            _container.RegisterInstance(typeof(ILogger), logger, Reuse.Singleton);
            var resolvedLogger = _container.Resolve(typeof(ILogger));

            // Assert
            Assert.IsNotNull(resolvedLogger);
            Assert.AreSame(logger, resolvedLogger);
        }

        /// <summary>
        /// 测试容器释放
        /// </summary>
        [TestMethod]
        public void Dispose_ShouldDisposeServices()
        {
            // Arrange
            var logger = new SerilogLogger();
            _container.RegisterInstance<ILogger>(logger, Reuse.Singleton);

            // Act
            _container.Dispose();

            // Assert - 容器应该正常释放，不抛出异常
            Assert.IsTrue(true);
        }
    }
}
