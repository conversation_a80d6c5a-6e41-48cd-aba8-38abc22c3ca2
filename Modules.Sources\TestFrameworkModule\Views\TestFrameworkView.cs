using System;
using System.Drawing;
using System.Windows.Forms;
using Contracts;

namespace TestFrameworkModule.Views
{
    /// <summary>
    /// 测试框架模块视图
    /// </summary>
    /// <remarks>
    /// 提供测试执行和结果显示的用户界面
    /// </remarks>
    public partial class TestFrameworkView : UserControl, IView
    {
        #region 事件定义

        /// <summary>
        /// 运行集成测试事件
        /// </summary>
        public event EventHandler RunIntegrationTestsClicked;

        /// <summary>
        /// 运行性能测试事件
        /// </summary>
        public event EventHandler RunPerformanceTestsClicked;

        /// <summary>
        /// 运行内存泄漏测试事件
        /// </summary>
        public event EventHandler RunMemoryLeakTestsClicked;

        /// <summary>
        /// 清除日志事件
        /// </summary>
        public event EventHandler ClearLogClicked;

        /// <summary>
        /// 导出报告事件
        /// </summary>
        public event EventHandler ExportReportClicked;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TestFrameworkView()
        {
            InitializeComponent();
            InitializeEvents();
        }

        #endregion

        #region IView 实现

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        public void ShowError(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                AppendLog($"[错误] {message}", Color.Red);
            });
        }

        /// <summary>
        /// 显示信息消息
        /// </summary>
        /// <param name="message">信息消息</param>
        public void ShowMessage(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                AppendLog($"[信息] {message}", Color.Blue);
            });
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message">警告消息</param>
        public void ShowWarning(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                AppendLog($"[警告] {message}", Color.Orange);
            });
        }

        /// <summary>
        /// 设置加载状态
        /// </summary>
        /// <param name="isLoading">是否正在加载</param>
        /// <param name="message">加载提示信息</param>
        public void SetLoadingState(bool isLoading, string message = "")
        {
            SafeUpdateUI(() =>
            {
                btnRunIntegrationTests.Enabled = !isLoading;
                btnRunPerformanceTests.Enabled = !isLoading;
                btnRunMemoryLeakTests.Enabled = !isLoading;
                btnExportReport.Enabled = !isLoading;

                if (isLoading)
                {
                    progressBar.Style = ProgressBarStyle.Marquee;
                    lblStatus.Text = string.IsNullOrEmpty(message) ? "正在执行测试..." : message;
                }
                else
                {
                    progressBar.Style = ProgressBarStyle.Blocks;
                    progressBar.Value = 0;
                    lblStatus.Text = string.IsNullOrEmpty(message) ? "就绪" : message;
                }
            });
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshData()
        {
            SafeUpdateUI(() =>
            {
                // 刷新测试统计信息
                UpdateTestStatistics();
                AppendLog("[系统] 数据已刷新", Color.Green);
            });
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加日志条目
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="color">文字颜色</param>
        public void AppendLog(string message, Color color)
        {
            SafeUpdateUI(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
                var logEntry = $"[{timestamp}] {message}";

                rtbLog.SelectionStart = rtbLog.TextLength;
                rtbLog.SelectionLength = 0;
                rtbLog.SelectionColor = color;
                rtbLog.AppendText(logEntry + Environment.NewLine);
                rtbLog.SelectionColor = rtbLog.ForeColor;

                // 自动滚动到底部
                rtbLog.ScrollToCaret();

                // 限制日志长度
                if (rtbLog.Lines.Length > 1000)
                {
                    var lines = rtbLog.Lines;
                    var newLines = new string[500];
                    Array.Copy(lines, lines.Length - 500, newLines, 0, 500);
                    rtbLog.Lines = newLines;
                }
            });
        }

        /// <summary>
        /// 更新测试进度
        /// </summary>
        /// <param name="progress">进度百分比（0-100）</param>
        /// <param name="status">状态文本</param>
        public void UpdateProgress(int progress, string status)
        {
            SafeUpdateUI(() =>
            {
                progressBar.Style = ProgressBarStyle.Blocks;
                progressBar.Value = Math.Max(0, Math.Min(100, progress));
                lblStatus.Text = status;
            });
        }

        /// <summary>
        /// 更新测试统计信息
        /// </summary>
        public void UpdateTestStatistics()
        {
            SafeUpdateUI(() =>
            {
                // 这里可以显示测试统计信息
                // 例如：总测试数、通过数、失败数等
            });
        }

        /// <summary>
        /// 清除日志
        /// </summary>
        public void ClearLog()
        {
            SafeUpdateUI(() =>
            {
                rtbLog.Clear();
                AppendLog("[系统] 日志已清除", Color.Green);
            });
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化事件处理
        /// </summary>
        private void InitializeEvents()
        {
            btnRunIntegrationTests.Click += (s, e) => RunIntegrationTestsClicked?.Invoke(s, e);
            btnRunPerformanceTests.Click += (s, e) => RunPerformanceTestsClicked?.Invoke(s, e);
            btnRunMemoryLeakTests.Click += (s, e) => RunMemoryLeakTestsClicked?.Invoke(s, e);
            btnClearLog.Click += (s, e) => ClearLogClicked?.Invoke(s, e);
            btnExportReport.Click += (s, e) => ExportReportClicked?.Invoke(s, e);
        }

        /// <summary>
        /// 线程安全的UI更新
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private void SafeUpdateUI(Action action)
        {
            if (InvokeRequired)
            {
                BeginInvoke(action);
            }
            else
            {
                action();
            }
        }

        #endregion
    }
}
