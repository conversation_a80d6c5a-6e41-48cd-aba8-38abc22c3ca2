using System;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using TestModule.Views;
using TestModule.Presenters;
using TestModule.Models;

namespace TestModule
{
    /// <summary>
    /// 测试模块主类
    /// </summary>
    /// <remarks>
    /// 用于验证模块加载器的功能，包括依赖注入、生命周期管理和异常处理
    /// </remarks>
    public class TestModuleMain : IModule
    {
        private TestModuleView _view;
        private TestModulePresenter _presenter;
        private TestModuleModel _model;
        private bool _isInitialized = false;
        private bool _isStarted = false;
        private bool _isDisposed = false;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string Name => "测试模块";

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description => "用于验证模块加载器功能的测试模块，包含完整的MVP架构";

        /// <summary>
        /// 事件聚合器（由ModuleLoader注入）
        /// </summary>
        public IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器（由ModuleLoader注入）
        /// </summary>
        public ILogger Logger { get; set; }

        /// <summary>
        /// 配置服务（由ModuleLoader通过DryIoc容器注入）
        /// </summary>
        public IConfigurationService ConfigurationService { get; set; }

        /// <summary>
        /// 无参构造函数（ModuleLoader要求）
        /// </summary>
        public TestModuleMain()
        {
            // 构造函数中不进行复杂操作，等待Initialize调用
        }

        /// <summary>
        /// 初始化模块
        /// </summary>
        public void Initialize()
        {
            try
            {
                Logger?.Info($"开始初始化模块: {Name}");

                // 验证依赖注入
                ValidateDependencies();

                // 创建模型
                _model = new TestModuleModel();
                Logger?.Debug("创建TestModuleModel成功");

                // 创建视图
                _view = new TestModuleView();
                Logger?.Debug("创建TestModuleView成功");

                // 创建表示器
                _presenter = new TestModulePresenter(_view, _model, EventAggregator, Logger);
                Logger?.Debug("创建TestModulePresenter成功");

                // 订阅系统事件
                SubscribeToEvents();

                _isInitialized = true;
                Logger?.Info($"模块初始化完成: {Name}");

                // 发布模块初始化完成事件
                EventAggregator?.GetEvent<SystemEvent>()
                    .Publish(new SystemEvent("ModuleInitialized", $"模块 {Name} 初始化完成"));
            }
            catch (Exception ex)
            {
                Logger?.Error($"模块初始化失败: {Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 启动模块
        /// </summary>
        public void Start()
        {
            try
            {
                if (!_isInitialized)
                {
                    throw new InvalidOperationException("模块未初始化，无法启动");
                }

                Logger?.Info($"开始启动模块: {Name}");

                // 启动表示器
                _presenter?.Start();

                _isStarted = true;
                Logger?.Info($"模块启动完成: {Name}");

                // 发布模块启动完成事件
                EventAggregator?.GetEvent<SystemEvent>()
                    .Publish(new SystemEvent("ModuleStarted", $"模块 {Name} 启动完成"));
            }
            catch (Exception ex)
            {
                Logger?.Error($"模块启动失败: {Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止模块
        /// </summary>
        public void Stop()
        {
            try
            {
                if (!_isStarted)
                {
                    Logger?.Warning($"模块 {Name} 未启动，无需停止");
                    return;
                }

                Logger?.Info($"开始停止模块: {Name}");

                // 停止表示器
                _presenter?.Stop();

                // 取消事件订阅
                UnsubscribeFromEvents();

                _isStarted = false;
                Logger?.Info($"模块停止完成: {Name}");

                // 发布模块停止事件
                EventAggregator?.GetEvent<SystemEvent>()
                    .Publish(new SystemEvent("ModuleStopped", $"模块 {Name} 停止完成"));
            }
            catch (Exception ex)
            {
                Logger?.Error($"模块停止失败: {Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取模块视图
        /// </summary>
        /// <returns>模块的用户控件</returns>
        public UserControl GetUserControl()
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("模块未初始化，无法获取视图");
            }

            return _view;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                Logger?.Info($"开始释放模块资源: {Name}");

                // 停止模块（如果还在运行）
                if (_isStarted)
                {
                    Stop();
                }

                // 释放组件
                _presenter?.Dispose();
                _view?.Dispose();
                _model?.Dispose();

                _isDisposed = true;
                Logger?.Info($"模块资源释放完成: {Name}");
            }
            catch (Exception ex)
            {
                Logger?.Error($"模块资源释放失败: {Name}", ex);
            }
        }

        /// <summary>
        /// 验证依赖注入
        /// </summary>
        private void ValidateDependencies()
        {
            if (EventAggregator == null)
            {
                throw new InvalidOperationException("EventAggregator未注入");
            }

            if (Logger == null)
            {
                throw new InvalidOperationException("Logger未注入");
            }

            // ConfigurationService是可选的，但如果容器中有，应该被注入
            if (ConfigurationService != null)
            {
                Logger.Debug("ConfigurationService注入成功");
            }
            else
            {
                Logger.Warning("ConfigurationService未注入（可能容器中未注册）");
            }

            Logger.Debug("依赖注入验证通过");
        }

        /// <summary>
        /// 订阅系统事件
        /// </summary>
        private void SubscribeToEvents()
        {
            try
            {
                // 订阅系统启动事件
                EventAggregator.GetEvent<SystemStartupEvent>()
                    .Subscribe(OnSystemStartup, keepSubscriberReferenceAlive: false);

                // 订阅系统关闭事件
                EventAggregator.GetEvent<SystemShutdownEvent>()
                    .Subscribe(OnSystemShutdown, keepSubscriberReferenceAlive: false);

                Logger?.Debug("事件订阅完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("事件订阅失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 取消事件订阅
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            try
            {
                // 取消系统启动事件订阅
                EventAggregator?.GetEvent<SystemStartupEvent>()
                    .Unsubscribe(OnSystemStartup);

                // 取消系统关闭事件订阅
                EventAggregator?.GetEvent<SystemShutdownEvent>()
                    .Unsubscribe(OnSystemShutdown);

                Logger?.Debug("事件取消订阅完成");
            }
            catch (Exception ex)
            {
                Logger?.Error("事件取消订阅失败", ex);
            }
        }

        /// <summary>
        /// 处理系统启动事件
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnSystemStartup(SystemStartupEvent eventArgs)
        {
            try
            {
                Logger?.Info($"模块 {Name} 收到系统启动事件");
                _model?.OnSystemStartup();
            }
            catch (Exception ex)
            {
                Logger?.Error($"模块 {Name} 处理系统启动事件失败", ex);
            }
        }

        /// <summary>
        /// 处理系统关闭事件
        /// </summary>
        /// <param name="eventArgs">事件参数</param>
        private void OnSystemShutdown(SystemShutdownEvent eventArgs)
        {
            try
            {
                Logger?.Info($"模块 {Name} 收到系统关闭事件: {eventArgs.Reason}");
                _model?.OnSystemShutdown();
            }
            catch (Exception ex)
            {
                Logger?.Error($"模块 {Name} 处理系统关闭事件失败", ex);
            }
        }
    }
}
