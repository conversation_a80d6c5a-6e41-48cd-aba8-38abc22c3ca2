﻿[2025-08-13 16:45:22.083 INF] [IndustrialHMI] [LIN-PC] [25824] === 应用程序启动 ===
[2025-08-13 16:45:22.105 INF] [IndustrialHMI] [LIN-PC] [25824] 应用程序版本: 1.0.0.0
[2025-08-13 16:45:22.105 INF] [IndustrialHMI] [LIN-PC] [25824] 启动参数: 
[2025-08-13 16:45:22.106 INF] [IndustrialHMI] [LIN-PC] [25824] 开始初始化应用程序
[2025-08-13 16:45:22.106 INF] [IndustrialHMI] [LIN-PC] [25824] 步骤1: 创建服务容器
[2025-08-13 16:45:22.109 INF] [IndustrialHMI] [LIN-PC] [25824] 开始创建DryIoc容器
[2025-08-13 16:45:22.120 DBG] [IndustrialHMI] [LIN-PC] [25824] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:45:22.123 DBG] [IndustrialHMI] [LIN-PC] [25824] 注册自定义日志记录器为单例
[2025-08-13 16:45:22.126 DBG] [IndustrialHMI] [LIN-PC] [25824] 注册EventAggregator为单例
[2025-08-13 16:45:22.132 DBG] [IndustrialHMI] [LIN-PC] [25824] 注册ConfigurationService为单例
[2025-08-13 16:45:22.142 DBG] [IndustrialHMI] [LIN-PC] [25824] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:45:22.206 DBG] [IndustrialHMI] [LIN-PC] [25824] 注册MainForm为单例
[2025-08-13 16:45:22.206 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始验证DryIoc容器配置
[2025-08-13 16:45:22.206 DBG] [IndustrialHMI] [LIN-PC] [25824] DryIoc容器配置验证通过
[2025-08-13 16:45:22.206 INF] [IndustrialHMI] [LIN-PC] [25824] DryIoc容器创建和配置完成
[2025-08-13 16:45:22.206 INF] [IndustrialHMI] [LIN-PC] [25824] 步骤2: 创建主窗体
[2025-08-13 16:45:22.206 INF] [IndustrialHMI] [LIN-PC] [25824] 步骤3: 创建模块加载器
[2025-08-13 16:45:22.206 INF] [IndustrialHMI] [LIN-PC] [25824] 步骤4: 加载模块
[2025-08-13 16:45:22.207 INF] [IndustrialHMI] [LIN-PC] [25824] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:45:22.207 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:45:22.215 DBG] [IndustrialHMI] [LIN-PC] [25824] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 发现模块类型: AlarmModuleMain
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.216 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载模块: AlarmModuleMain
[2025-08-13 16:45:22.217 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:45:22.217 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:45:22.217 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:45:22.218 INF] [IndustrialHMI] [LIN-PC] [25824] 开始初始化报警管理模块
[2025-08-13 16:45:22.219 DBG] [IndustrialHMI] [LIN-PC] [25824] 报警服务创建完成
[2025-08-13 16:45:22.231 DBG] [IndustrialHMI] [LIN-PC] [25824] 报警视图创建完成
[2025-08-13 16:45:22.232 DBG] [IndustrialHMI] [LIN-PC] [25824] AlarmModel 初始化完成
[2025-08-13 16:45:22.232 DBG] [IndustrialHMI] [LIN-PC] [25824] 报警模型创建完成
[2025-08-13 16:45:22.233 DBG] [IndustrialHMI] [LIN-PC] [25824] AlarmPresenter 初始化完成
[2025-08-13 16:45:22.233 DBG] [IndustrialHMI] [LIN-PC] [25824] 报警表示器创建完成
[2025-08-13 16:45:22.233 INF] [IndustrialHMI] [LIN-PC] [25824] MVP组件创建完成
[2025-08-13 16:45:22.234 DBG] [IndustrialHMI] [LIN-PC] [25824] 系统事件订阅完成
[2025-08-13 16:45:22.234 INF] [IndustrialHMI] [LIN-PC] [25824] 报警管理模块初始化完成
[2025-08-13 16:45:22.234 INF] [IndustrialHMI] [LIN-PC] [25824] 启动报警管理模块
[2025-08-13 16:45:22.234 INF] [IndustrialHMI] [LIN-PC] [25824] 启动报警监控
[2025-08-13 16:45:22.234 INF] [IndustrialHMI] [LIN-PC] [25824] 开始报警监控
[2025-08-13 16:45:22.234 INF] [IndustrialHMI] [LIN-PC] [25824] 报警管理模块启动完成
[2025-08-13 16:45:22.235 INF] [IndustrialHMI] [LIN-PC] [25824] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:45:22.236 DBG] [IndustrialHMI] [LIN-PC] [25824] 模块已加载: 报警管理
[2025-08-13 16:45:22.236 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:45:22.237 DBG] [IndustrialHMI] [LIN-PC] [25824] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.238 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:45:22.239 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:45:22.239 INF] [IndustrialHMI] [LIN-PC] [25824] 开始初始化 CommunicationTestModule
[2025-08-13 16:45:22.240 INF] [IndustrialHMI] [LIN-PC] [25824] 初始化了 8 个测试用例
[2025-08-13 16:45:22.240 INF] [IndustrialHMI] [LIN-PC] [25824] CommunicationTestModel 初始化完成
[2025-08-13 16:45:22.246 DBG] [IndustrialHMI] [LIN-PC] [25824] CommunicationTestView 初始化完成
[2025-08-13 16:45:22.250 DBG] [IndustrialHMI] [LIN-PC] [25824] 视图数据初始化完成
[2025-08-13 16:45:22.250 INF] [IndustrialHMI] [LIN-PC] [25824] CommunicationTestPresenter 初始化完成
[2025-08-13 16:45:22.250 DBG] [IndustrialHMI] [LIN-PC] [25824] MVP组件创建完成
[2025-08-13 16:45:22.250 DBG] [IndustrialHMI] [LIN-PC] [25824] 系统事件订阅完成
[2025-08-13 16:45:22.250 INF] [IndustrialHMI] [LIN-PC] [25824] CommunicationTestModule 初始化完成
[2025-08-13 16:45:22.250 INF] [IndustrialHMI] [LIN-PC] [25824] 启动 CommunicationTestModule
[2025-08-13 16:45:22.250 INF] [IndustrialHMI] [LIN-PC] [25824] CommunicationTestModule 启动完成
[2025-08-13 16:45:22.250 INF] [IndustrialHMI] [LIN-PC] [25824] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:45:22.250 DBG] [IndustrialHMI] [LIN-PC] [25824] 模块已加载: 通信测试
[2025-08-13 16:45:22.250 DBG] [IndustrialHMI] [LIN-PC] [25824] 收到模块加载事件: 通信测试
[2025-08-13 16:45:22.250 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:45:22.252 DBG] [IndustrialHMI] [LIN-PC] [25824] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 发现模块类型: 
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:45:22.253 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:45:22.254 DBG] [IndustrialHMI] [LIN-PC] [25824] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 发现模块类型: DeviceModuleMain
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载模块: DeviceModuleMain
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:45:22.255 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:45:22.255 INF] [IndustrialHMI] [LIN-PC] [25824] 开始初始化设备监控模块
[2025-08-13 16:45:22.256 DBG] [IndustrialHMI] [LIN-PC] [25824] 设备服务创建完成
[2025-08-13 16:45:22.258 DBG] [IndustrialHMI] [LIN-PC] [25824] 设备视图创建完成
[2025-08-13 16:45:22.258 DBG] [IndustrialHMI] [LIN-PC] [25824] DeviceModel 初始化完成
[2025-08-13 16:45:22.258 DBG] [IndustrialHMI] [LIN-PC] [25824] 设备模型创建完成
[2025-08-13 16:45:22.259 DBG] [IndustrialHMI] [LIN-PC] [25824] DevicePresenter 初始化完成
[2025-08-13 16:45:22.259 DBG] [IndustrialHMI] [LIN-PC] [25824] 设备表示器创建完成
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] MVP组件创建完成
[2025-08-13 16:45:22.259 DBG] [IndustrialHMI] [LIN-PC] [25824] 系统事件订阅完成
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] 设备监控模块初始化完成
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] 启动设备监控模块
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] 用户请求开始设备监控
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] 开始设备监控
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] 设备监控已启动
[2025-08-13 16:45:22.259 INF] [IndustrialHMI] [LIN-PC] [25824] 设备监控模块启动完成
[2025-08-13 16:45:22.260 INF] [IndustrialHMI] [LIN-PC] [25824] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:45:22.260 DBG] [IndustrialHMI] [LIN-PC] [25824] 模块已加载: 设备监控
[2025-08-13 16:45:22.260 DBG] [IndustrialHMI] [LIN-PC] [25824] 收到模块加载事件: 设备监控
[2025-08-13 16:45:22.260 DBG] [IndustrialHMI] [LIN-PC] [25824] 模块已加载: 设备监控
[2025-08-13 16:45:22.260 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:45:22.261 DBG] [IndustrialHMI] [LIN-PC] [25824] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:45:22.262 INF] [IndustrialHMI] [LIN-PC] [25824] 开始初始化测试框架模块
[2025-08-13 16:45:22.262 DBG] [IndustrialHMI] [LIN-PC] [25824] ConfigurationService未注入（可选）
[2025-08-13 16:45:27.395 DBG] [IndustrialHMI] [LIN-PC] [25824] 初始化TestFrameworkPresenter
[2025-08-13 16:45:27.428 DBG] [IndustrialHMI] [LIN-PC] [25824] TestFrameworkPresenter初始化完成
[2025-08-13 16:45:27.428 DBG] [IndustrialHMI] [LIN-PC] [25824] 测试框架模块事件订阅完成
[2025-08-13 16:45:27.428 INF] [IndustrialHMI] [LIN-PC] [25824] 测试框架模块初始化完成
[2025-08-13 16:45:27.429 INF] [IndustrialHMI] [LIN-PC] [25824] 启动测试框架模块
[2025-08-13 16:45:27.429 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 模型已启动
[2025-08-13 16:45:27.429 DBG] [IndustrialHMI] [LIN-PC] [25824] 加载TestFramework数据
[2025-08-13 16:45:27.430 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据已更新，视图已刷新
[2025-08-13 16:45:27.430 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 数据加载完成
[2025-08-13 16:45:27.431 DBG] [IndustrialHMI] [LIN-PC] [25824] TestFramework数据加载完成
[2025-08-13 16:45:27.431 INF] [IndustrialHMI] [LIN-PC] [25824] 初始化集成测试套件
[2025-08-13 16:45:27.431 INF] [IndustrialHMI] [LIN-PC] [25824] 集成测试套件初始化完成
[2025-08-13 16:45:27.431 INF] [IndustrialHMI] [LIN-PC] [25824] 初始化性能测试套件
[2025-08-13 16:45:27.552 INF] [IndustrialHMI] [LIN-PC] [25824] 性能测试套件初始化完成
[2025-08-13 16:45:27.552 INF] [IndustrialHMI] [LIN-PC] [25824] 初始化内存泄漏测试套件
[2025-08-13 16:45:27.552 INF] [IndustrialHMI] [LIN-PC] [25824] 内存泄漏测试套件初始化完成
[2025-08-13 16:45:27.552 INF] [IndustrialHMI] [LIN-PC] [25824] 测试框架模块启动完成
[2025-08-13 16:45:27.552 INF] [IndustrialHMI] [LIN-PC] [25824] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:45:27.552 INF] [IndustrialHMI] [LIN-PC] [25824] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:45:27.553 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据已更新，视图已刷新
[2025-08-13 16:45:27.553 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:45:27.553 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 发现模块类型: TestModuleMain
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 开始加载模块: TestModuleMain
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:45:27.555 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:45:27.556 INF] [IndustrialHMI] [LIN-PC] [25824] 开始初始化模块: 测试模块
[2025-08-13 16:45:27.556 WRN] [IndustrialHMI] [LIN-PC] [25824] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:45:27.556 DBG] [IndustrialHMI] [LIN-PC] [25824] 依赖注入验证通过
[2025-08-13 16:45:27.556 DBG] [IndustrialHMI] [LIN-PC] [25824] 创建TestModuleModel成功
[2025-08-13 16:45:27.580 DBG] [IndustrialHMI] [LIN-PC] [25824] 创建TestModuleView成功
[2025-08-13 16:45:27.581 DBG] [IndustrialHMI] [LIN-PC] [25824] TestModulePresenter创建完成
[2025-08-13 16:45:27.581 DBG] [IndustrialHMI] [LIN-PC] [25824] 创建TestModulePresenter成功
[2025-08-13 16:45:27.581 DBG] [IndustrialHMI] [LIN-PC] [25824] 事件订阅完成
[2025-08-13 16:45:27.581 INF] [IndustrialHMI] [LIN-PC] [25824] 模块初始化完成: 测试模块
[2025-08-13 16:45:27.582 INF] [IndustrialHMI] [LIN-PC] [25824] 开始启动模块: 测试模块
[2025-08-13 16:45:27.582 INF] [IndustrialHMI] [LIN-PC] [25824] 启动TestModulePresenter
[2025-08-13 16:45:27.594 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 模型启动完成
[2025-08-13 16:45:27.595 DBG] [IndustrialHMI] [LIN-PC] [25824] 系统事件订阅完成
[2025-08-13 16:45:27.595 INF] [IndustrialHMI] [LIN-PC] [25824] TestModulePresenter启动完成
[2025-08-13 16:45:27.595 INF] [IndustrialHMI] [LIN-PC] [25824] 模块启动完成: 测试模块
[2025-08-13 16:45:27.599 DBG] [IndustrialHMI] [LIN-PC] [25824] 处理系统事件: ModuleStarted
[2025-08-13 16:45:27.599 INF] [IndustrialHMI] [LIN-PC] [25824] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:45:27.599 INF] [IndustrialHMI] [LIN-PC] [25824] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:45:27.600 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据已更新，视图已刷新
[2025-08-13 16:45:27.600 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:45:27.602 DBG] [IndustrialHMI] [LIN-PC] [25824] 处理模块加载事件: 测试模块
[2025-08-13 16:45:27.602 INF] [IndustrialHMI] [LIN-PC] [25824] 模块加载完成，共加载 5 个模块
[2025-08-13 16:45:27.602 INF] [IndustrialHMI] [LIN-PC] [25824] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:45:27.603 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:45:27.603 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:45:27.603 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:45:27.604 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:45:27.605 DBG] [IndustrialHMI] [LIN-PC] [25824] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:45:27.605 INF] [IndustrialHMI] [LIN-PC] [25824] 步骤5: 初始化主窗体
[2025-08-13 16:45:27.605 DBG] [IndustrialHMI] [LIN-PC] [25824] 主窗体初始化完成
[2025-08-13 16:45:27.605 INF] [IndustrialHMI] [LIN-PC] [25824] 应用程序初始化完成
[2025-08-13 16:45:27.605 INF] [IndustrialHMI] [LIN-PC] [25824] 应用程序初始化成功，启动主窗体
[2025-08-13 16:45:27.605 INF] [IndustrialHMI] [LIN-PC] [25824] 测试框架模块收到系统启动事件
[2025-08-13 16:45:27.606 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据已更新，视图已刷新
[2025-08-13 16:45:27.606 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:45:27.606 INF] [IndustrialHMI] [LIN-PC] [25824] 模块 测试模块 收到系统启动事件
[2025-08-13 16:45:27.607 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 收到系统启动事件
[2025-08-13 16:45:27.740 DBG] [IndustrialHMI] [LIN-PC] [25824] 主窗体事件订阅完成
[2025-08-13 16:45:27.746 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据变化事件处理完成
[2025-08-13 16:45:27.747 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 16:45:27.750 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 定时状态更新 - 16:45:27
[2025-08-13 16:45:28.614 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 系统启动后初始化完成
[2025-08-13 16:45:32.589 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据变化事件处理完成
[2025-08-13 16:45:32.590 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 数据更新: +1, 当前值: 2
[2025-08-13 16:45:32.591 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 定时状态更新 - 16:45:32
[2025-08-13 16:45:37.608 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据变化事件处理完成
[2025-08-13 16:45:37.609 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 数据更新: +1, 当前值: 3
[2025-08-13 16:45:37.610 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 定时状态更新 - 16:45:37
[2025-08-13 16:45:42.622 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据变化事件处理完成
[2025-08-13 16:45:42.622 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 数据更新: +1, 当前值: 4
[2025-08-13 16:45:42.623 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 定时状态更新 - 16:45:42
[2025-08-13 16:45:47.624 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型数据变化事件处理完成
[2025-08-13 16:45:47.624 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 数据更新: +1, 当前值: 5
[2025-08-13 16:45:47.625 DBG] [IndustrialHMI] [LIN-PC] [25824] 模型状态变化: 定时状态更新 - 16:45:47
﻿[2025-08-13 16:46:14.823 INF] [IndustrialHMI] [LIN-PC] [27908] === 应用程序启动 ===
[2025-08-13 16:46:14.847 INF] [IndustrialHMI] [LIN-PC] [27908] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:14.848 INF] [IndustrialHMI] [LIN-PC] [27908] 启动参数: 
[2025-08-13 16:46:14.848 INF] [IndustrialHMI] [LIN-PC] [27908] 开始初始化应用程序
[2025-08-13 16:46:14.848 INF] [IndustrialHMI] [LIN-PC] [27908] 步骤1: 创建服务容器
[2025-08-13 16:46:14.852 INF] [IndustrialHMI] [LIN-PC] [27908] 开始创建DryIoc容器
[2025-08-13 16:46:14.863 DBG] [IndustrialHMI] [LIN-PC] [27908] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:14.867 DBG] [IndustrialHMI] [LIN-PC] [27908] 注册自定义日志记录器为单例
[2025-08-13 16:46:14.867 DBG] [IndustrialHMI] [LIN-PC] [27908] 注册EventAggregator为单例
[2025-08-13 16:46:14.873 DBG] [IndustrialHMI] [LIN-PC] [27908] 注册ConfigurationService为单例
[2025-08-13 16:46:14.882 DBG] [IndustrialHMI] [LIN-PC] [27908] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:14.913 DBG] [IndustrialHMI] [LIN-PC] [27908] 注册MainForm为单例
[2025-08-13 16:46:14.914 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始验证DryIoc容器配置
[2025-08-13 16:46:14.914 DBG] [IndustrialHMI] [LIN-PC] [27908] DryIoc容器配置验证通过
[2025-08-13 16:46:14.914 INF] [IndustrialHMI] [LIN-PC] [27908] DryIoc容器创建和配置完成
[2025-08-13 16:46:14.914 INF] [IndustrialHMI] [LIN-PC] [27908] 步骤2: 创建主窗体
[2025-08-13 16:46:14.914 INF] [IndustrialHMI] [LIN-PC] [27908] 步骤3: 创建模块加载器
[2025-08-13 16:46:14.914 INF] [IndustrialHMI] [LIN-PC] [27908] 步骤4: 加载模块
[2025-08-13 16:46:14.915 INF] [IndustrialHMI] [LIN-PC] [27908] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:14.916 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:14.924 DBG] [IndustrialHMI] [LIN-PC] [27908] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:14.925 DBG] [IndustrialHMI] [LIN-PC] [27908] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:14.926 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:14.927 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:14.927 INF] [IndustrialHMI] [LIN-PC] [27908] 开始初始化报警管理模块
[2025-08-13 16:46:14.929 DBG] [IndustrialHMI] [LIN-PC] [27908] 报警服务创建完成
[2025-08-13 16:46:14.936 DBG] [IndustrialHMI] [LIN-PC] [27908] 报警视图创建完成
[2025-08-13 16:46:14.938 DBG] [IndustrialHMI] [LIN-PC] [27908] AlarmModel 初始化完成
[2025-08-13 16:46:14.938 DBG] [IndustrialHMI] [LIN-PC] [27908] 报警模型创建完成
[2025-08-13 16:46:14.938 DBG] [IndustrialHMI] [LIN-PC] [27908] AlarmPresenter 初始化完成
[2025-08-13 16:46:14.938 DBG] [IndustrialHMI] [LIN-PC] [27908] 报警表示器创建完成
[2025-08-13 16:46:14.938 INF] [IndustrialHMI] [LIN-PC] [27908] MVP组件创建完成
[2025-08-13 16:46:14.939 DBG] [IndustrialHMI] [LIN-PC] [27908] 系统事件订阅完成
[2025-08-13 16:46:14.939 INF] [IndustrialHMI] [LIN-PC] [27908] 报警管理模块初始化完成
[2025-08-13 16:46:14.939 INF] [IndustrialHMI] [LIN-PC] [27908] 启动报警管理模块
[2025-08-13 16:46:14.939 INF] [IndustrialHMI] [LIN-PC] [27908] 启动报警监控
[2025-08-13 16:46:14.939 INF] [IndustrialHMI] [LIN-PC] [27908] 开始报警监控
[2025-08-13 16:46:14.939 INF] [IndustrialHMI] [LIN-PC] [27908] 报警管理模块启动完成
[2025-08-13 16:46:14.940 INF] [IndustrialHMI] [LIN-PC] [27908] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:14.941 DBG] [IndustrialHMI] [LIN-PC] [27908] 模块已加载: 报警管理
[2025-08-13 16:46:14.941 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:14.943 DBG] [IndustrialHMI] [LIN-PC] [27908] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:14.944 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:14.944 INF] [IndustrialHMI] [LIN-PC] [27908] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:14.945 INF] [IndustrialHMI] [LIN-PC] [27908] 初始化了 8 个测试用例
[2025-08-13 16:46:14.946 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModel 初始化完成
[2025-08-13 16:46:14.950 DBG] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestView 初始化完成
[2025-08-13 16:46:14.953 DBG] [IndustrialHMI] [LIN-PC] [27908] 视图数据初始化完成
[2025-08-13 16:46:14.953 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:14.953 DBG] [IndustrialHMI] [LIN-PC] [27908] MVP组件创建完成
[2025-08-13 16:46:14.953 DBG] [IndustrialHMI] [LIN-PC] [27908] 系统事件订阅完成
[2025-08-13 16:46:14.953 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModule 初始化完成
[2025-08-13 16:46:14.953 INF] [IndustrialHMI] [LIN-PC] [27908] 启动 CommunicationTestModule
[2025-08-13 16:46:14.953 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModule 启动完成
[2025-08-13 16:46:14.954 INF] [IndustrialHMI] [LIN-PC] [27908] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:14.954 DBG] [IndustrialHMI] [LIN-PC] [27908] 模块已加载: 通信测试
[2025-08-13 16:46:14.954 DBG] [IndustrialHMI] [LIN-PC] [27908] 收到模块加载事件: 通信测试
[2025-08-13 16:46:14.954 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:14.955 DBG] [IndustrialHMI] [LIN-PC] [27908] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 发现模块类型: 
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:14.956 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:14.957 DBG] [IndustrialHMI] [LIN-PC] [27908] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:14.958 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:14.959 INF] [IndustrialHMI] [LIN-PC] [27908] 开始初始化设备监控模块
[2025-08-13 16:46:14.959 DBG] [IndustrialHMI] [LIN-PC] [27908] 设备服务创建完成
[2025-08-13 16:46:14.961 DBG] [IndustrialHMI] [LIN-PC] [27908] 设备视图创建完成
[2025-08-13 16:46:14.961 DBG] [IndustrialHMI] [LIN-PC] [27908] DeviceModel 初始化完成
[2025-08-13 16:46:14.962 DBG] [IndustrialHMI] [LIN-PC] [27908] 设备模型创建完成
[2025-08-13 16:46:14.962 DBG] [IndustrialHMI] [LIN-PC] [27908] DevicePresenter 初始化完成
[2025-08-13 16:46:14.962 DBG] [IndustrialHMI] [LIN-PC] [27908] 设备表示器创建完成
[2025-08-13 16:46:14.962 INF] [IndustrialHMI] [LIN-PC] [27908] MVP组件创建完成
[2025-08-13 16:46:14.962 DBG] [IndustrialHMI] [LIN-PC] [27908] 系统事件订阅完成
[2025-08-13 16:46:14.962 INF] [IndustrialHMI] [LIN-PC] [27908] 设备监控模块初始化完成
[2025-08-13 16:46:14.962 INF] [IndustrialHMI] [LIN-PC] [27908] 启动设备监控模块
[2025-08-13 16:46:14.963 INF] [IndustrialHMI] [LIN-PC] [27908] 用户请求开始设备监控
[2025-08-13 16:46:14.963 INF] [IndustrialHMI] [LIN-PC] [27908] 开始设备监控
[2025-08-13 16:46:14.963 INF] [IndustrialHMI] [LIN-PC] [27908] 设备监控已启动
[2025-08-13 16:46:14.963 INF] [IndustrialHMI] [LIN-PC] [27908] 设备监控模块启动完成
[2025-08-13 16:46:14.963 INF] [IndustrialHMI] [LIN-PC] [27908] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:14.963 DBG] [IndustrialHMI] [LIN-PC] [27908] 模块已加载: 设备监控
[2025-08-13 16:46:14.963 DBG] [IndustrialHMI] [LIN-PC] [27908] 收到模块加载事件: 设备监控
[2025-08-13 16:46:14.963 DBG] [IndustrialHMI] [LIN-PC] [27908] 模块已加载: 设备监控
[2025-08-13 16:46:14.963 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:14.965 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:14.966 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:14.966 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:14.966 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:14.966 INF] [IndustrialHMI] [LIN-PC] [27908] 开始初始化测试框架模块
[2025-08-13 16:46:14.966 DBG] [IndustrialHMI] [LIN-PC] [27908] ConfigurationService未注入（可选）
[2025-08-13 16:46:15.716 DBG] [IndustrialHMI] [LIN-PC] [27908] 初始化TestFrameworkPresenter
[2025-08-13 16:46:15.733 DBG] [IndustrialHMI] [LIN-PC] [27908] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:15.733 DBG] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块事件订阅完成
[2025-08-13 16:46:15.733 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块初始化完成
[2025-08-13 16:46:15.733 INF] [IndustrialHMI] [LIN-PC] [27908] 启动测试框架模块
[2025-08-13 16:46:15.734 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 模型已启动
[2025-08-13 16:46:15.734 DBG] [IndustrialHMI] [LIN-PC] [27908] 加载TestFramework数据
[2025-08-13 16:46:15.735 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据已更新，视图已刷新
[2025-08-13 16:46:15.735 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据加载完成
[2025-08-13 16:46:15.735 DBG] [IndustrialHMI] [LIN-PC] [27908] TestFramework数据加载完成
[2025-08-13 16:46:15.735 INF] [IndustrialHMI] [LIN-PC] [27908] 初始化集成测试套件
[2025-08-13 16:46:15.736 INF] [IndustrialHMI] [LIN-PC] [27908] 集成测试套件初始化完成
[2025-08-13 16:46:15.736 INF] [IndustrialHMI] [LIN-PC] [27908] 初始化性能测试套件
[2025-08-13 16:46:15.858 INF] [IndustrialHMI] [LIN-PC] [27908] 性能测试套件初始化完成
[2025-08-13 16:46:15.858 INF] [IndustrialHMI] [LIN-PC] [27908] 初始化内存泄漏测试套件
[2025-08-13 16:46:15.858 INF] [IndustrialHMI] [LIN-PC] [27908] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:15.858 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块启动完成
[2025-08-13 16:46:15.858 INF] [IndustrialHMI] [LIN-PC] [27908] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:15.858 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:15.859 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据已更新，视图已刷新
[2025-08-13 16:46:15.860 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:15.860 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:15.862 DBG] [IndustrialHMI] [LIN-PC] [27908] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 发现模块类型: TestModuleMain
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 开始加载模块: TestModuleMain
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:15.863 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:15.864 INF] [IndustrialHMI] [LIN-PC] [27908] 开始初始化模块: 测试模块
[2025-08-13 16:46:15.864 WRN] [IndustrialHMI] [LIN-PC] [27908] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:15.864 DBG] [IndustrialHMI] [LIN-PC] [27908] 依赖注入验证通过
[2025-08-13 16:46:15.864 DBG] [IndustrialHMI] [LIN-PC] [27908] 创建TestModuleModel成功
[2025-08-13 16:46:15.867 DBG] [IndustrialHMI] [LIN-PC] [27908] 创建TestModuleView成功
[2025-08-13 16:46:15.867 DBG] [IndustrialHMI] [LIN-PC] [27908] TestModulePresenter创建完成
[2025-08-13 16:46:15.867 DBG] [IndustrialHMI] [LIN-PC] [27908] 创建TestModulePresenter成功
[2025-08-13 16:46:15.867 DBG] [IndustrialHMI] [LIN-PC] [27908] 事件订阅完成
[2025-08-13 16:46:15.867 INF] [IndustrialHMI] [LIN-PC] [27908] 模块初始化完成: 测试模块
[2025-08-13 16:46:15.868 INF] [IndustrialHMI] [LIN-PC] [27908] 开始启动模块: 测试模块
[2025-08-13 16:46:15.868 INF] [IndustrialHMI] [LIN-PC] [27908] 启动TestModulePresenter
[2025-08-13 16:46:15.873 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 模型启动完成
[2025-08-13 16:46:15.875 DBG] [IndustrialHMI] [LIN-PC] [27908] 系统事件订阅完成
[2025-08-13 16:46:15.875 INF] [IndustrialHMI] [LIN-PC] [27908] TestModulePresenter启动完成
[2025-08-13 16:46:15.875 INF] [IndustrialHMI] [LIN-PC] [27908] 模块启动完成: 测试模块
[2025-08-13 16:46:15.876 DBG] [IndustrialHMI] [LIN-PC] [27908] 处理系统事件: ModuleStarted
[2025-08-13 16:46:15.877 INF] [IndustrialHMI] [LIN-PC] [27908] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:15.877 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:15.877 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据已更新，视图已刷新
[2025-08-13 16:46:15.877 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:15.878 DBG] [IndustrialHMI] [LIN-PC] [27908] 处理模块加载事件: 测试模块
[2025-08-13 16:46:15.878 INF] [IndustrialHMI] [LIN-PC] [27908] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:15.878 INF] [IndustrialHMI] [LIN-PC] [27908] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:15.879 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:15.879 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:15.880 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:15.881 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:15.881 DBG] [IndustrialHMI] [LIN-PC] [27908] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:15.881 INF] [IndustrialHMI] [LIN-PC] [27908] 步骤5: 初始化主窗体
[2025-08-13 16:46:15.881 DBG] [IndustrialHMI] [LIN-PC] [27908] 主窗体初始化完成
[2025-08-13 16:46:15.881 INF] [IndustrialHMI] [LIN-PC] [27908] 应用程序初始化完成
[2025-08-13 16:46:15.881 INF] [IndustrialHMI] [LIN-PC] [27908] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:15.882 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块收到系统启动事件
[2025-08-13 16:46:15.882 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据已更新，视图已刷新
[2025-08-13 16:46:15.882 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:15.883 INF] [IndustrialHMI] [LIN-PC] [27908] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:15.883 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:15.978 DBG] [IndustrialHMI] [LIN-PC] [27908] 主窗体事件订阅完成
[2025-08-13 16:46:15.985 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:15.987 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 16:46:15.988 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:15
[2025-08-13 16:46:16.896 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 系统启动后初始化完成
[2025-08-13 16:46:20.889 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:20.894 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 2
[2025-08-13 16:46:20.895 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:20
[2025-08-13 16:46:25.905 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:25.906 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 3
[2025-08-13 16:46:25.907 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:25
[2025-08-13 16:46:30.915 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:30.918 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 4
[2025-08-13 16:46:30.921 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:30
[2025-08-13 16:46:35.924 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:35.925 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 5
[2025-08-13 16:46:35.925 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:35
﻿[2025-08-13 16:46:36.425 INF] [IndustrialHMI] [LIN-PC] [28952] === 应用程序启动 ===
[2025-08-13 16:46:36.448 INF] [IndustrialHMI] [LIN-PC] [28952] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:36.448 INF] [IndustrialHMI] [LIN-PC] [28952] 启动参数: 
[2025-08-13 16:46:36.448 INF] [IndustrialHMI] [LIN-PC] [28952] 开始初始化应用程序
[2025-08-13 16:46:36.448 INF] [IndustrialHMI] [LIN-PC] [28952] 步骤1: 创建服务容器
[2025-08-13 16:46:36.452 INF] [IndustrialHMI] [LIN-PC] [28952] 开始创建DryIoc容器
[2025-08-13 16:46:36.463 DBG] [IndustrialHMI] [LIN-PC] [28952] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:36.467 DBG] [IndustrialHMI] [LIN-PC] [28952] 注册自定义日志记录器为单例
[2025-08-13 16:46:36.467 DBG] [IndustrialHMI] [LIN-PC] [28952] 注册EventAggregator为单例
[2025-08-13 16:46:36.472 DBG] [IndustrialHMI] [LIN-PC] [28952] 注册ConfigurationService为单例
[2025-08-13 16:46:36.481 DBG] [IndustrialHMI] [LIN-PC] [28952] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:36.510 DBG] [IndustrialHMI] [LIN-PC] [28952] 注册MainForm为单例
[2025-08-13 16:46:36.510 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始验证DryIoc容器配置
[2025-08-13 16:46:36.511 DBG] [IndustrialHMI] [LIN-PC] [28952] DryIoc容器配置验证通过
[2025-08-13 16:46:36.511 INF] [IndustrialHMI] [LIN-PC] [28952] DryIoc容器创建和配置完成
[2025-08-13 16:46:36.511 INF] [IndustrialHMI] [LIN-PC] [28952] 步骤2: 创建主窗体
[2025-08-13 16:46:36.511 INF] [IndustrialHMI] [LIN-PC] [28952] 步骤3: 创建模块加载器
[2025-08-13 16:46:36.511 INF] [IndustrialHMI] [LIN-PC] [28952] 步骤4: 加载模块
[2025-08-13 16:46:36.511 INF] [IndustrialHMI] [LIN-PC] [28952] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:36.512 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:36.519 DBG] [IndustrialHMI] [LIN-PC] [28952] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.521 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:36.522 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:36.522 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:36.522 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:36.523 INF] [IndustrialHMI] [LIN-PC] [28952] 开始初始化报警管理模块
[2025-08-13 16:46:36.524 DBG] [IndustrialHMI] [LIN-PC] [28952] 报警服务创建完成
[2025-08-13 16:46:36.530 DBG] [IndustrialHMI] [LIN-PC] [28952] 报警视图创建完成
[2025-08-13 16:46:36.532 DBG] [IndustrialHMI] [LIN-PC] [28952] AlarmModel 初始化完成
[2025-08-13 16:46:36.532 DBG] [IndustrialHMI] [LIN-PC] [28952] 报警模型创建完成
[2025-08-13 16:46:36.533 DBG] [IndustrialHMI] [LIN-PC] [28952] AlarmPresenter 初始化完成
[2025-08-13 16:46:36.533 DBG] [IndustrialHMI] [LIN-PC] [28952] 报警表示器创建完成
[2025-08-13 16:46:36.533 INF] [IndustrialHMI] [LIN-PC] [28952] MVP组件创建完成
[2025-08-13 16:46:36.533 DBG] [IndustrialHMI] [LIN-PC] [28952] 系统事件订阅完成
[2025-08-13 16:46:36.533 INF] [IndustrialHMI] [LIN-PC] [28952] 报警管理模块初始化完成
[2025-08-13 16:46:36.533 INF] [IndustrialHMI] [LIN-PC] [28952] 启动报警管理模块
[2025-08-13 16:46:36.534 INF] [IndustrialHMI] [LIN-PC] [28952] 启动报警监控
[2025-08-13 16:46:36.534 INF] [IndustrialHMI] [LIN-PC] [28952] 开始报警监控
[2025-08-13 16:46:36.534 INF] [IndustrialHMI] [LIN-PC] [28952] 报警管理模块启动完成
[2025-08-13 16:46:36.534 INF] [IndustrialHMI] [LIN-PC] [28952] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:36.536 DBG] [IndustrialHMI] [LIN-PC] [28952] 模块已加载: 报警管理
[2025-08-13 16:46:36.536 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:36.537 DBG] [IndustrialHMI] [LIN-PC] [28952] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.538 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.539 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:36.539 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:36.539 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:36.539 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:36.539 INF] [IndustrialHMI] [LIN-PC] [28952] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:36.540 INF] [IndustrialHMI] [LIN-PC] [28952] 初始化了 8 个测试用例
[2025-08-13 16:46:36.540 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModel 初始化完成
[2025-08-13 16:46:36.544 DBG] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestView 初始化完成
[2025-08-13 16:46:36.547 DBG] [IndustrialHMI] [LIN-PC] [28952] 视图数据初始化完成
[2025-08-13 16:46:36.547 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:36.547 DBG] [IndustrialHMI] [LIN-PC] [28952] MVP组件创建完成
[2025-08-13 16:46:36.547 DBG] [IndustrialHMI] [LIN-PC] [28952] 系统事件订阅完成
[2025-08-13 16:46:36.547 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModule 初始化完成
[2025-08-13 16:46:36.547 INF] [IndustrialHMI] [LIN-PC] [28952] 启动 CommunicationTestModule
[2025-08-13 16:46:36.547 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModule 启动完成
[2025-08-13 16:46:36.547 INF] [IndustrialHMI] [LIN-PC] [28952] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:36.547 DBG] [IndustrialHMI] [LIN-PC] [28952] 模块已加载: 通信测试
[2025-08-13 16:46:36.547 DBG] [IndustrialHMI] [LIN-PC] [28952] 收到模块加载事件: 通信测试
[2025-08-13 16:46:36.547 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:36.549 DBG] [IndustrialHMI] [LIN-PC] [28952] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:36.549 DBG] [IndustrialHMI] [LIN-PC] [28952] 发现模块类型: 
[2025-08-13 16:46:36.549 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:36.550 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:36.551 DBG] [IndustrialHMI] [LIN-PC] [28952] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:36.552 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:36.552 INF] [IndustrialHMI] [LIN-PC] [28952] 开始初始化设备监控模块
[2025-08-13 16:46:36.553 DBG] [IndustrialHMI] [LIN-PC] [28952] 设备服务创建完成
[2025-08-13 16:46:36.555 DBG] [IndustrialHMI] [LIN-PC] [28952] 设备视图创建完成
[2025-08-13 16:46:36.555 DBG] [IndustrialHMI] [LIN-PC] [28952] DeviceModel 初始化完成
[2025-08-13 16:46:36.555 DBG] [IndustrialHMI] [LIN-PC] [28952] 设备模型创建完成
[2025-08-13 16:46:36.556 DBG] [IndustrialHMI] [LIN-PC] [28952] DevicePresenter 初始化完成
[2025-08-13 16:46:36.556 DBG] [IndustrialHMI] [LIN-PC] [28952] 设备表示器创建完成
[2025-08-13 16:46:36.556 INF] [IndustrialHMI] [LIN-PC] [28952] MVP组件创建完成
[2025-08-13 16:46:36.556 DBG] [IndustrialHMI] [LIN-PC] [28952] 系统事件订阅完成
[2025-08-13 16:46:36.556 INF] [IndustrialHMI] [LIN-PC] [28952] 设备监控模块初始化完成
[2025-08-13 16:46:36.556 INF] [IndustrialHMI] [LIN-PC] [28952] 启动设备监控模块
[2025-08-13 16:46:36.556 INF] [IndustrialHMI] [LIN-PC] [28952] 用户请求开始设备监控
[2025-08-13 16:46:36.556 INF] [IndustrialHMI] [LIN-PC] [28952] 开始设备监控
[2025-08-13 16:46:36.557 INF] [IndustrialHMI] [LIN-PC] [28952] 设备监控已启动
[2025-08-13 16:46:36.557 INF] [IndustrialHMI] [LIN-PC] [28952] 设备监控模块启动完成
[2025-08-13 16:46:36.557 INF] [IndustrialHMI] [LIN-PC] [28952] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:36.557 DBG] [IndustrialHMI] [LIN-PC] [28952] 模块已加载: 设备监控
[2025-08-13 16:46:36.557 DBG] [IndustrialHMI] [LIN-PC] [28952] 收到模块加载事件: 设备监控
[2025-08-13 16:46:36.557 DBG] [IndustrialHMI] [LIN-PC] [28952] 模块已加载: 设备监控
[2025-08-13 16:46:36.557 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:36.558 DBG] [IndustrialHMI] [LIN-PC] [28952] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:36.559 INF] [IndustrialHMI] [LIN-PC] [28952] 开始初始化测试框架模块
[2025-08-13 16:46:36.559 DBG] [IndustrialHMI] [LIN-PC] [28952] ConfigurationService未注入（可选）
[2025-08-13 16:46:37.260 DBG] [IndustrialHMI] [LIN-PC] [28952] 初始化TestFrameworkPresenter
[2025-08-13 16:46:37.280 DBG] [IndustrialHMI] [LIN-PC] [28952] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:37.281 DBG] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块事件订阅完成
[2025-08-13 16:46:37.281 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块初始化完成
[2025-08-13 16:46:37.281 INF] [IndustrialHMI] [LIN-PC] [28952] 启动测试框架模块
[2025-08-13 16:46:37.283 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 模型已启动
[2025-08-13 16:46:37.283 DBG] [IndustrialHMI] [LIN-PC] [28952] 加载TestFramework数据
[2025-08-13 16:46:37.284 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.284 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 数据加载完成
[2025-08-13 16:46:37.284 DBG] [IndustrialHMI] [LIN-PC] [28952] TestFramework数据加载完成
[2025-08-13 16:46:37.285 INF] [IndustrialHMI] [LIN-PC] [28952] 初始化集成测试套件
[2025-08-13 16:46:37.285 INF] [IndustrialHMI] [LIN-PC] [28952] 集成测试套件初始化完成
[2025-08-13 16:46:37.285 INF] [IndustrialHMI] [LIN-PC] [28952] 初始化性能测试套件
[2025-08-13 16:46:37.418 INF] [IndustrialHMI] [LIN-PC] [28952] 性能测试套件初始化完成
[2025-08-13 16:46:37.418 INF] [IndustrialHMI] [LIN-PC] [28952] 初始化内存泄漏测试套件
[2025-08-13 16:46:37.419 INF] [IndustrialHMI] [LIN-PC] [28952] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:37.419 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块启动完成
[2025-08-13 16:46:37.425 INF] [IndustrialHMI] [LIN-PC] [28952] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:37.426 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:37.426 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.427 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:37.427 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 发现模块类型: TestModuleMain
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 开始加载模块: TestModuleMain
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:37.428 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:37.429 INF] [IndustrialHMI] [LIN-PC] [28952] 开始初始化模块: 测试模块
[2025-08-13 16:46:37.429 WRN] [IndustrialHMI] [LIN-PC] [28952] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:37.429 DBG] [IndustrialHMI] [LIN-PC] [28952] 依赖注入验证通过
[2025-08-13 16:46:37.429 DBG] [IndustrialHMI] [LIN-PC] [28952] 创建TestModuleModel成功
[2025-08-13 16:46:37.431 DBG] [IndustrialHMI] [LIN-PC] [28952] 创建TestModuleView成功
[2025-08-13 16:46:37.431 DBG] [IndustrialHMI] [LIN-PC] [28952] TestModulePresenter创建完成
[2025-08-13 16:46:37.431 DBG] [IndustrialHMI] [LIN-PC] [28952] 创建TestModulePresenter成功
[2025-08-13 16:46:37.432 DBG] [IndustrialHMI] [LIN-PC] [28952] 事件订阅完成
[2025-08-13 16:46:37.432 INF] [IndustrialHMI] [LIN-PC] [28952] 模块初始化完成: 测试模块
[2025-08-13 16:46:37.432 INF] [IndustrialHMI] [LIN-PC] [28952] 开始启动模块: 测试模块
[2025-08-13 16:46:37.432 INF] [IndustrialHMI] [LIN-PC] [28952] 启动TestModulePresenter
[2025-08-13 16:46:37.441 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 模型启动完成
[2025-08-13 16:46:37.442 DBG] [IndustrialHMI] [LIN-PC] [28952] 系统事件订阅完成
[2025-08-13 16:46:37.442 INF] [IndustrialHMI] [LIN-PC] [28952] TestModulePresenter启动完成
[2025-08-13 16:46:37.442 INF] [IndustrialHMI] [LIN-PC] [28952] 模块启动完成: 测试模块
[2025-08-13 16:46:37.443 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理系统事件: ModuleStarted
[2025-08-13 16:46:37.443 INF] [IndustrialHMI] [LIN-PC] [28952] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:37.443 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:37.443 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.444 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:37.445 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理模块加载事件: 测试模块
[2025-08-13 16:46:37.445 INF] [IndustrialHMI] [LIN-PC] [28952] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:37.445 INF] [IndustrialHMI] [LIN-PC] [28952] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:37.446 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:37.446 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:37.446 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:37.447 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:37.448 DBG] [IndustrialHMI] [LIN-PC] [28952] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:37.448 INF] [IndustrialHMI] [LIN-PC] [28952] 步骤5: 初始化主窗体
[2025-08-13 16:46:37.448 DBG] [IndustrialHMI] [LIN-PC] [28952] 主窗体初始化完成
[2025-08-13 16:46:37.448 INF] [IndustrialHMI] [LIN-PC] [28952] 应用程序初始化完成
[2025-08-13 16:46:37.448 INF] [IndustrialHMI] [LIN-PC] [28952] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:37.449 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到系统启动事件
[2025-08-13 16:46:37.450 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.450 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:37.450 INF] [IndustrialHMI] [LIN-PC] [28952] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:37.451 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:37.522 DBG] [IndustrialHMI] [LIN-PC] [28952] 主窗体事件订阅完成
[2025-08-13 16:46:37.523 INF] [IndustrialHMI] [LIN-PC] [28952] 用户请求关闭应用程序
[2025-08-13 16:46:37.523 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:37.524 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.524 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 16:46:37.525 INF] [IndustrialHMI] [LIN-PC] [28952] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:37.526 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到系统关闭事件
[2025-08-13 16:46:37.541 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据变化事件处理完成
[2025-08-13 16:46:37.541 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据变化事件处理完成
[2025-08-13 16:46:37.542 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 系统关闭前清理完成
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载所有模块
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载模块: 报警管理
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 停止报警管理模块
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 停止报警监控
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 停止报警监控
[2025-08-13 16:46:37.543 INF] [IndustrialHMI] [LIN-PC] [28952] 报警管理模块停止完成
[2025-08-13 16:46:37.544 INF] [IndustrialHMI] [LIN-PC] [28952] 开始释放报警管理模块资源
[2025-08-13 16:46:37.544 INF] [IndustrialHMI] [LIN-PC] [28952] 停止报警监控
[2025-08-13 16:46:37.544 INF] [IndustrialHMI] [LIN-PC] [28952] 停止报警监控
[2025-08-13 16:46:37.545 DBG] [IndustrialHMI] [LIN-PC] [28952] AlarmPresenter 资源释放完成
[2025-08-13 16:46:37.545 INF] [IndustrialHMI] [LIN-PC] [28952] 停止报警监控
[2025-08-13 16:46:37.545 DBG] [IndustrialHMI] [LIN-PC] [28952] AlarmModel 资源释放完成
[2025-08-13 16:46:37.565 INF] [IndustrialHMI] [LIN-PC] [28952] 报警管理模块资源释放完成
[2025-08-13 16:46:37.566 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 16:46:37.566 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.567 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:37.568 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理模块卸载事件: 报警管理
[2025-08-13 16:46:37.568 INF] [IndustrialHMI] [LIN-PC] [28952] 模块卸载成功: 报警管理
[2025-08-13 16:46:37.568 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载模块: 通信测试
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] 停止 CommunicationTestModule
[2025-08-13 16:46:37.569 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据变化: EventMonitoring
[2025-08-13 16:46:37.569 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] 测试已停止
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModule 停止完成
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] 停止 CommunicationTestModule
[2025-08-13 16:46:37.569 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据变化: EventMonitoring
[2025-08-13 16:46:37.569 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] 测试已停止
[2025-08-13 16:46:37.569 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModule 停止完成
[2025-08-13 16:46:37.569 DBG] [IndustrialHMI] [LIN-PC] [28952] 系统事件订阅已取消
[2025-08-13 16:46:37.570 DBG] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:46:37.570 DBG] [IndustrialHMI] [LIN-PC] [28952] EventMonitor 资源释放完成
[2025-08-13 16:46:37.570 INF] [IndustrialHMI] [LIN-PC] [28952] 测试已停止
[2025-08-13 16:46:37.570 DBG] [IndustrialHMI] [LIN-PC] [28952] TestCaseManager 资源释放完成
[2025-08-13 16:46:37.571 DBG] [IndustrialHMI] [LIN-PC] [28952] PerformanceMonitor 资源释放完成
[2025-08-13 16:46:37.571 DBG] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModel 资源释放完成
[2025-08-13 16:46:37.571 INF] [IndustrialHMI] [LIN-PC] [28952] CommunicationTestModule 资源释放完成
[2025-08-13 16:46:37.571 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 16:46:37.571 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.571 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:37.572 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理模块卸载事件: 通信测试
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 模块卸载成功: 通信测试
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载模块: 设备监控
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 停止设备监控模块
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 用户请求停止设备监控
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 停止设备监控
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 设备监控已停止
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 设备监控模块停止完成
[2025-08-13 16:46:37.573 INF] [IndustrialHMI] [LIN-PC] [28952] 开始释放设备监控模块资源
[2025-08-13 16:46:37.574 DBG] [IndustrialHMI] [LIN-PC] [28952] DevicePresenter 资源释放完成
[2025-08-13 16:46:37.574 INF] [IndustrialHMI] [LIN-PC] [28952] 停止设备监控
[2025-08-13 16:46:37.574 DBG] [IndustrialHMI] [LIN-PC] [28952] DeviceModel 资源释放完成
[2025-08-13 16:46:37.574 INF] [IndustrialHMI] [LIN-PC] [28952] 设备监控模块资源释放完成
[2025-08-13 16:46:37.574 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 16:46:37.574 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型数据已更新，视图已刷新
[2025-08-13 16:46:37.575 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:37.576 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理模块卸载事件: 设备监控
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 模块卸载成功: 设备监控
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载模块: 测试框架模块
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 停止测试框架模块
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 停止内存泄漏测试套件
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 内存泄漏测试套件已停止
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 停止性能测试套件
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 性能测试套件已停止
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 停止集成测试套件
[2025-08-13 16:46:37.576 INF] [IndustrialHMI] [LIN-PC] [28952] 集成测试套件已停止
[2025-08-13 16:46:37.577 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 模型已停止
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块停止完成
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 开始释放测试框架模块资源
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 停止内存泄漏测试套件
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 内存泄漏测试套件已停止
[2025-08-13 16:46:37.577 DBG] [IndustrialHMI] [LIN-PC] [28952] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 停止性能测试套件
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 性能测试套件已停止
[2025-08-13 16:46:37.577 DBG] [IndustrialHMI] [LIN-PC] [28952] PerformanceTestSuite资源释放完成
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 停止集成测试套件
[2025-08-13 16:46:37.577 INF] [IndustrialHMI] [LIN-PC] [28952] 集成测试套件已停止
[2025-08-13 16:46:37.577 DBG] [IndustrialHMI] [LIN-PC] [28952] IntegrationTestSuite资源释放完成
[2025-08-13 16:46:37.578 DBG] [IndustrialHMI] [LIN-PC] [28952] TestFrameworkPresenter资源释放完成
[2025-08-13 16:46:37.579 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块资源释放完成
[2025-08-13 16:46:37.579 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 16:46:37.579 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理模块卸载事件: 测试框架模块
[2025-08-13 16:46:37.579 INF] [IndustrialHMI] [LIN-PC] [28952] 模块卸载成功: 测试框架模块
[2025-08-13 16:46:37.579 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载模块: 测试模块
[2025-08-13 16:46:37.579 INF] [IndustrialHMI] [LIN-PC] [28952] 开始停止模块: 测试模块
[2025-08-13 16:46:37.579 INF] [IndustrialHMI] [LIN-PC] [28952] 停止TestModulePresenter
[2025-08-13 16:46:37.580 DBG] [IndustrialHMI] [LIN-PC] [28952] 系统事件取消订阅完成
[2025-08-13 16:46:37.580 DBG] [IndustrialHMI] [LIN-PC] [28952] 模型状态变化: 模型停止完成
[2025-08-13 16:46:37.581 INF] [IndustrialHMI] [LIN-PC] [28952] TestModulePresenter停止完成
[2025-08-13 16:46:37.581 DBG] [IndustrialHMI] [LIN-PC] [28952] 事件取消订阅完成
[2025-08-13 16:46:37.581 INF] [IndustrialHMI] [LIN-PC] [28952] 模块停止完成: 测试模块
[2025-08-13 16:46:37.581 DBG] [IndustrialHMI] [LIN-PC] [28952] 处理系统事件: ModuleStopped
[2025-08-13 16:46:37.581 INF] [IndustrialHMI] [LIN-PC] [28952] 开始释放模块资源: 测试模块
[2025-08-13 16:46:37.581 INF] [IndustrialHMI] [LIN-PC] [28952] 释放TestModulePresenter资源
[2025-08-13 16:46:37.582 INF] [IndustrialHMI] [LIN-PC] [28952] TestModulePresenter资源释放完成
[2025-08-13 16:46:37.593 INF] [IndustrialHMI] [LIN-PC] [28952] 模块资源释放完成: 测试模块
[2025-08-13 16:46:37.593 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 16:46:37.597 ERR] [IndustrialHMI] [LIN-PC] [28952] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.set_SelectedText(String value)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 16:46:37.603 INF] [IndustrialHMI] [LIN-PC] [28952] 模块卸载成功: 测试模块
[2025-08-13 16:46:37.603 INF] [IndustrialHMI] [LIN-PC] [28952] 所有模块卸载完成
[2025-08-13 16:46:37.603 INF] [IndustrialHMI] [LIN-PC] [28952] 应用程序关闭流程完成
[2025-08-13 16:46:37.604 INF] [IndustrialHMI] [LIN-PC] [28952] 主窗体已关闭，资源清理完成
[2025-08-13 16:46:37.609 INF] [IndustrialHMI] [LIN-PC] [28952] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:37.609 INF] [IndustrialHMI] [LIN-PC] [28952] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:37.609 INF] [IndustrialHMI] [LIN-PC] [28952] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:37.610 INF] [IndustrialHMI] [LIN-PC] [28952] 开始释放应用程序资源
[2025-08-13 16:46:37.610 INF] [IndustrialHMI] [LIN-PC] [28952] 开始卸载所有模块
[2025-08-13 16:46:37.610 INF] [IndustrialHMI] [LIN-PC] [28952] 所有模块卸载完成
[2025-08-13 16:46:37.610 INF] [IndustrialHMI] [LIN-PC] [28952] 应用程序资源释放完成
[2025-08-13 16:46:37.610 INF] [IndustrialHMI] [LIN-PC] [28952] === 应用程序正常退出 ===
﻿[2025-08-13 16:46:38.734 INF] [IndustrialHMI] [LIN-PC] [7716] === 应用程序启动 ===
[2025-08-13 16:46:38.756 INF] [IndustrialHMI] [LIN-PC] [7716] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:38.756 INF] [IndustrialHMI] [LIN-PC] [7716] 启动参数: 
[2025-08-13 16:46:38.757 INF] [IndustrialHMI] [LIN-PC] [7716] 开始初始化应用程序
[2025-08-13 16:46:38.757 INF] [IndustrialHMI] [LIN-PC] [7716] 步骤1: 创建服务容器
[2025-08-13 16:46:38.761 INF] [IndustrialHMI] [LIN-PC] [7716] 开始创建DryIoc容器
[2025-08-13 16:46:38.771 DBG] [IndustrialHMI] [LIN-PC] [7716] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:38.774 DBG] [IndustrialHMI] [LIN-PC] [7716] 注册自定义日志记录器为单例
[2025-08-13 16:46:38.774 DBG] [IndustrialHMI] [LIN-PC] [7716] 注册EventAggregator为单例
[2025-08-13 16:46:38.780 DBG] [IndustrialHMI] [LIN-PC] [7716] 注册ConfigurationService为单例
[2025-08-13 16:46:38.789 DBG] [IndustrialHMI] [LIN-PC] [7716] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:38.818 DBG] [IndustrialHMI] [LIN-PC] [7716] 注册MainForm为单例
[2025-08-13 16:46:38.818 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始验证DryIoc容器配置
[2025-08-13 16:46:38.818 DBG] [IndustrialHMI] [LIN-PC] [7716] DryIoc容器配置验证通过
[2025-08-13 16:46:38.818 INF] [IndustrialHMI] [LIN-PC] [7716] DryIoc容器创建和配置完成
[2025-08-13 16:46:38.818 INF] [IndustrialHMI] [LIN-PC] [7716] 步骤2: 创建主窗体
[2025-08-13 16:46:38.818 INF] [IndustrialHMI] [LIN-PC] [7716] 步骤3: 创建模块加载器
[2025-08-13 16:46:38.818 INF] [IndustrialHMI] [LIN-PC] [7716] 步骤4: 加载模块
[2025-08-13 16:46:38.819 INF] [IndustrialHMI] [LIN-PC] [7716] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:38.820 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:38.827 DBG] [IndustrialHMI] [LIN-PC] [7716] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:38.828 DBG] [IndustrialHMI] [LIN-PC] [7716] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:38.828 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:38.828 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:38.828 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:38.828 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:38.828 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:38.829 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:38.830 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:38.830 INF] [IndustrialHMI] [LIN-PC] [7716] 开始初始化报警管理模块
[2025-08-13 16:46:38.832 DBG] [IndustrialHMI] [LIN-PC] [7716] 报警服务创建完成
[2025-08-13 16:46:38.840 DBG] [IndustrialHMI] [LIN-PC] [7716] 报警视图创建完成
[2025-08-13 16:46:38.841 DBG] [IndustrialHMI] [LIN-PC] [7716] AlarmModel 初始化完成
[2025-08-13 16:46:38.841 DBG] [IndustrialHMI] [LIN-PC] [7716] 报警模型创建完成
[2025-08-13 16:46:38.842 DBG] [IndustrialHMI] [LIN-PC] [7716] AlarmPresenter 初始化完成
[2025-08-13 16:46:38.842 DBG] [IndustrialHMI] [LIN-PC] [7716] 报警表示器创建完成
[2025-08-13 16:46:38.842 INF] [IndustrialHMI] [LIN-PC] [7716] MVP组件创建完成
[2025-08-13 16:46:38.842 DBG] [IndustrialHMI] [LIN-PC] [7716] 系统事件订阅完成
[2025-08-13 16:46:38.842 INF] [IndustrialHMI] [LIN-PC] [7716] 报警管理模块初始化完成
[2025-08-13 16:46:38.842 INF] [IndustrialHMI] [LIN-PC] [7716] 启动报警管理模块
[2025-08-13 16:46:38.842 INF] [IndustrialHMI] [LIN-PC] [7716] 启动报警监控
[2025-08-13 16:46:38.842 INF] [IndustrialHMI] [LIN-PC] [7716] 开始报警监控
[2025-08-13 16:46:38.843 INF] [IndustrialHMI] [LIN-PC] [7716] 报警管理模块启动完成
[2025-08-13 16:46:38.843 INF] [IndustrialHMI] [LIN-PC] [7716] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:38.844 DBG] [IndustrialHMI] [LIN-PC] [7716] 模块已加载: 报警管理
[2025-08-13 16:46:38.844 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:38.845 DBG] [IndustrialHMI] [LIN-PC] [7716] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:38.846 DBG] [IndustrialHMI] [LIN-PC] [7716] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:38.846 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:38.846 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:38.847 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:38.847 INF] [IndustrialHMI] [LIN-PC] [7716] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:38.848 INF] [IndustrialHMI] [LIN-PC] [7716] 初始化了 8 个测试用例
[2025-08-13 16:46:38.849 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModel 初始化完成
[2025-08-13 16:46:38.853 DBG] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestView 初始化完成
[2025-08-13 16:46:38.855 DBG] [IndustrialHMI] [LIN-PC] [7716] 视图数据初始化完成
[2025-08-13 16:46:38.856 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:38.856 DBG] [IndustrialHMI] [LIN-PC] [7716] MVP组件创建完成
[2025-08-13 16:46:38.856 DBG] [IndustrialHMI] [LIN-PC] [7716] 系统事件订阅完成
[2025-08-13 16:46:38.856 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModule 初始化完成
[2025-08-13 16:46:38.856 INF] [IndustrialHMI] [LIN-PC] [7716] 启动 CommunicationTestModule
[2025-08-13 16:46:38.856 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModule 启动完成
[2025-08-13 16:46:38.856 INF] [IndustrialHMI] [LIN-PC] [7716] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:38.856 DBG] [IndustrialHMI] [LIN-PC] [7716] 模块已加载: 通信测试
[2025-08-13 16:46:38.856 DBG] [IndustrialHMI] [LIN-PC] [7716] 收到模块加载事件: 通信测试
[2025-08-13 16:46:38.856 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:38.857 DBG] [IndustrialHMI] [LIN-PC] [7716] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 发现模块类型: 
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:38.858 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:38.859 DBG] [IndustrialHMI] [LIN-PC] [7716] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:38.860 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:38.860 INF] [IndustrialHMI] [LIN-PC] [7716] 开始初始化设备监控模块
[2025-08-13 16:46:38.861 DBG] [IndustrialHMI] [LIN-PC] [7716] 设备服务创建完成
[2025-08-13 16:46:38.862 DBG] [IndustrialHMI] [LIN-PC] [7716] 设备视图创建完成
[2025-08-13 16:46:38.863 DBG] [IndustrialHMI] [LIN-PC] [7716] DeviceModel 初始化完成
[2025-08-13 16:46:38.863 DBG] [IndustrialHMI] [LIN-PC] [7716] 设备模型创建完成
[2025-08-13 16:46:38.863 DBG] [IndustrialHMI] [LIN-PC] [7716] DevicePresenter 初始化完成
[2025-08-13 16:46:38.863 DBG] [IndustrialHMI] [LIN-PC] [7716] 设备表示器创建完成
[2025-08-13 16:46:38.863 INF] [IndustrialHMI] [LIN-PC] [7716] MVP组件创建完成
[2025-08-13 16:46:38.864 DBG] [IndustrialHMI] [LIN-PC] [7716] 系统事件订阅完成
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 设备监控模块初始化完成
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 启动设备监控模块
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 用户请求开始设备监控
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 开始设备监控
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 设备监控已启动
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 设备监控模块启动完成
[2025-08-13 16:46:38.864 INF] [IndustrialHMI] [LIN-PC] [7716] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:38.864 DBG] [IndustrialHMI] [LIN-PC] [7716] 模块已加载: 设备监控
[2025-08-13 16:46:38.864 DBG] [IndustrialHMI] [LIN-PC] [7716] 收到模块加载事件: 设备监控
[2025-08-13 16:46:38.864 DBG] [IndustrialHMI] [LIN-PC] [7716] 模块已加载: 设备监控
[2025-08-13 16:46:38.864 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:38.866 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:38.867 INF] [IndustrialHMI] [LIN-PC] [7716] 开始初始化测试框架模块
[2025-08-13 16:46:38.867 DBG] [IndustrialHMI] [LIN-PC] [7716] ConfigurationService未注入（可选）
[2025-08-13 16:46:39.512 DBG] [IndustrialHMI] [LIN-PC] [7716] 初始化TestFrameworkPresenter
[2025-08-13 16:46:39.539 DBG] [IndustrialHMI] [LIN-PC] [7716] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:39.539 DBG] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块事件订阅完成
[2025-08-13 16:46:39.539 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块初始化完成
[2025-08-13 16:46:39.540 INF] [IndustrialHMI] [LIN-PC] [7716] 启动测试框架模块
[2025-08-13 16:46:39.540 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 模型已启动
[2025-08-13 16:46:39.540 DBG] [IndustrialHMI] [LIN-PC] [7716] 加载TestFramework数据
[2025-08-13 16:46:39.541 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.541 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 数据加载完成
[2025-08-13 16:46:39.541 DBG] [IndustrialHMI] [LIN-PC] [7716] TestFramework数据加载完成
[2025-08-13 16:46:39.542 INF] [IndustrialHMI] [LIN-PC] [7716] 初始化集成测试套件
[2025-08-13 16:46:39.542 INF] [IndustrialHMI] [LIN-PC] [7716] 集成测试套件初始化完成
[2025-08-13 16:46:39.542 INF] [IndustrialHMI] [LIN-PC] [7716] 初始化性能测试套件
[2025-08-13 16:46:39.666 INF] [IndustrialHMI] [LIN-PC] [7716] 性能测试套件初始化完成
[2025-08-13 16:46:39.666 INF] [IndustrialHMI] [LIN-PC] [7716] 初始化内存泄漏测试套件
[2025-08-13 16:46:39.667 INF] [IndustrialHMI] [LIN-PC] [7716] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:39.667 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块启动完成
[2025-08-13 16:46:39.667 INF] [IndustrialHMI] [LIN-PC] [7716] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:39.667 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:39.669 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.669 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:39.669 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:39.671 DBG] [IndustrialHMI] [LIN-PC] [7716] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 发现模块类型: TestModuleMain
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 开始加载模块: TestModuleMain
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:39.672 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:39.673 INF] [IndustrialHMI] [LIN-PC] [7716] 开始初始化模块: 测试模块
[2025-08-13 16:46:39.673 WRN] [IndustrialHMI] [LIN-PC] [7716] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:39.673 DBG] [IndustrialHMI] [LIN-PC] [7716] 依赖注入验证通过
[2025-08-13 16:46:39.673 DBG] [IndustrialHMI] [LIN-PC] [7716] 创建TestModuleModel成功
[2025-08-13 16:46:39.675 DBG] [IndustrialHMI] [LIN-PC] [7716] 创建TestModuleView成功
[2025-08-13 16:46:39.675 DBG] [IndustrialHMI] [LIN-PC] [7716] TestModulePresenter创建完成
[2025-08-13 16:46:39.675 DBG] [IndustrialHMI] [LIN-PC] [7716] 创建TestModulePresenter成功
[2025-08-13 16:46:39.675 DBG] [IndustrialHMI] [LIN-PC] [7716] 事件订阅完成
[2025-08-13 16:46:39.675 INF] [IndustrialHMI] [LIN-PC] [7716] 模块初始化完成: 测试模块
[2025-08-13 16:46:39.676 INF] [IndustrialHMI] [LIN-PC] [7716] 开始启动模块: 测试模块
[2025-08-13 16:46:39.676 INF] [IndustrialHMI] [LIN-PC] [7716] 启动TestModulePresenter
[2025-08-13 16:46:39.682 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 模型启动完成
[2025-08-13 16:46:39.684 DBG] [IndustrialHMI] [LIN-PC] [7716] 系统事件订阅完成
[2025-08-13 16:46:39.684 INF] [IndustrialHMI] [LIN-PC] [7716] TestModulePresenter启动完成
[2025-08-13 16:46:39.684 INF] [IndustrialHMI] [LIN-PC] [7716] 模块启动完成: 测试模块
[2025-08-13 16:46:39.685 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理系统事件: ModuleStarted
[2025-08-13 16:46:39.685 INF] [IndustrialHMI] [LIN-PC] [7716] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:39.685 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:39.685 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.686 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:39.686 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理模块加载事件: 测试模块
[2025-08-13 16:46:39.686 INF] [IndustrialHMI] [LIN-PC] [7716] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:39.686 INF] [IndustrialHMI] [LIN-PC] [7716] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:39.687 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:39.688 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:39.688 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:39.689 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:39.689 DBG] [IndustrialHMI] [LIN-PC] [7716] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:39.689 INF] [IndustrialHMI] [LIN-PC] [7716] 步骤5: 初始化主窗体
[2025-08-13 16:46:39.689 DBG] [IndustrialHMI] [LIN-PC] [7716] 主窗体初始化完成
[2025-08-13 16:46:39.690 INF] [IndustrialHMI] [LIN-PC] [7716] 应用程序初始化完成
[2025-08-13 16:46:39.690 INF] [IndustrialHMI] [LIN-PC] [7716] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:39.690 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到系统启动事件
[2025-08-13 16:46:39.690 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.691 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:39.691 INF] [IndustrialHMI] [LIN-PC] [7716] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:39.691 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:39.759 DBG] [IndustrialHMI] [LIN-PC] [7716] 主窗体事件订阅完成
[2025-08-13 16:46:39.761 INF] [IndustrialHMI] [LIN-PC] [7716] 用户请求关闭应用程序
[2025-08-13 16:46:39.761 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:39.762 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.762 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 16:46:39.762 INF] [IndustrialHMI] [LIN-PC] [7716] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:39.763 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到系统关闭事件
[2025-08-13 16:46:39.777 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据变化事件处理完成
[2025-08-13 16:46:39.779 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据变化事件处理完成
[2025-08-13 16:46:39.780 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 系统关闭前清理完成
[2025-08-13 16:46:39.780 INF] [IndustrialHMI] [LIN-PC] [7716] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:39.780 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载所有模块
[2025-08-13 16:46:39.780 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载模块: 报警管理
[2025-08-13 16:46:39.781 INF] [IndustrialHMI] [LIN-PC] [7716] 停止报警管理模块
[2025-08-13 16:46:39.781 INF] [IndustrialHMI] [LIN-PC] [7716] 停止报警监控
[2025-08-13 16:46:39.781 INF] [IndustrialHMI] [LIN-PC] [7716] 停止报警监控
[2025-08-13 16:46:39.781 INF] [IndustrialHMI] [LIN-PC] [7716] 报警管理模块停止完成
[2025-08-13 16:46:39.781 INF] [IndustrialHMI] [LIN-PC] [7716] 开始释放报警管理模块资源
[2025-08-13 16:46:39.782 INF] [IndustrialHMI] [LIN-PC] [7716] 停止报警监控
[2025-08-13 16:46:39.782 INF] [IndustrialHMI] [LIN-PC] [7716] 停止报警监控
[2025-08-13 16:46:39.782 DBG] [IndustrialHMI] [LIN-PC] [7716] AlarmPresenter 资源释放完成
[2025-08-13 16:46:39.783 INF] [IndustrialHMI] [LIN-PC] [7716] 停止报警监控
[2025-08-13 16:46:39.783 DBG] [IndustrialHMI] [LIN-PC] [7716] AlarmModel 资源释放完成
[2025-08-13 16:46:39.788 INF] [IndustrialHMI] [LIN-PC] [7716] 报警管理模块资源释放完成
[2025-08-13 16:46:39.789 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 16:46:39.789 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.789 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:39.790 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理模块卸载事件: 报警管理
[2025-08-13 16:46:39.790 INF] [IndustrialHMI] [LIN-PC] [7716] 模块卸载成功: 报警管理
[2025-08-13 16:46:39.790 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载模块: 通信测试
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] 停止 CommunicationTestModule
[2025-08-13 16:46:39.791 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据变化: EventMonitoring
[2025-08-13 16:46:39.791 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] 测试已停止
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModule 停止完成
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] 停止 CommunicationTestModule
[2025-08-13 16:46:39.791 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据变化: EventMonitoring
[2025-08-13 16:46:39.791 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] 测试已停止
[2025-08-13 16:46:39.791 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModule 停止完成
[2025-08-13 16:46:39.791 DBG] [IndustrialHMI] [LIN-PC] [7716] 系统事件订阅已取消
[2025-08-13 16:46:39.792 DBG] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:46:39.792 DBG] [IndustrialHMI] [LIN-PC] [7716] EventMonitor 资源释放完成
[2025-08-13 16:46:39.792 INF] [IndustrialHMI] [LIN-PC] [7716] 测试已停止
[2025-08-13 16:46:39.792 DBG] [IndustrialHMI] [LIN-PC] [7716] TestCaseManager 资源释放完成
[2025-08-13 16:46:39.792 DBG] [IndustrialHMI] [LIN-PC] [7716] PerformanceMonitor 资源释放完成
[2025-08-13 16:46:39.792 DBG] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModel 资源释放完成
[2025-08-13 16:46:39.793 INF] [IndustrialHMI] [LIN-PC] [7716] CommunicationTestModule 资源释放完成
[2025-08-13 16:46:39.793 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 16:46:39.793 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.793 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:39.794 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理模块卸载事件: 通信测试
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 模块卸载成功: 通信测试
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载模块: 设备监控
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 停止设备监控模块
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 用户请求停止设备监控
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 停止设备监控
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 设备监控已停止
[2025-08-13 16:46:39.794 INF] [IndustrialHMI] [LIN-PC] [7716] 设备监控模块停止完成
[2025-08-13 16:46:39.795 INF] [IndustrialHMI] [LIN-PC] [7716] 开始释放设备监控模块资源
[2025-08-13 16:46:39.795 DBG] [IndustrialHMI] [LIN-PC] [7716] DevicePresenter 资源释放完成
[2025-08-13 16:46:39.795 INF] [IndustrialHMI] [LIN-PC] [7716] 停止设备监控
[2025-08-13 16:46:39.795 DBG] [IndustrialHMI] [LIN-PC] [7716] DeviceModel 资源释放完成
[2025-08-13 16:46:39.795 INF] [IndustrialHMI] [LIN-PC] [7716] 设备监控模块资源释放完成
[2025-08-13 16:46:39.795 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 16:46:39.796 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型数据已更新，视图已刷新
[2025-08-13 16:46:39.796 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:39.796 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理模块卸载事件: 设备监控
[2025-08-13 16:46:39.796 INF] [IndustrialHMI] [LIN-PC] [7716] 模块卸载成功: 设备监控
[2025-08-13 16:46:39.796 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载模块: 测试框架模块
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 停止测试框架模块
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 停止内存泄漏测试套件
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 内存泄漏测试套件已停止
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 停止性能测试套件
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 性能测试套件已停止
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 停止集成测试套件
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 集成测试套件已停止
[2025-08-13 16:46:39.797 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 模型已停止
[2025-08-13 16:46:39.797 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块停止完成
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 开始释放测试框架模块资源
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 停止内存泄漏测试套件
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 内存泄漏测试套件已停止
[2025-08-13 16:46:39.798 DBG] [IndustrialHMI] [LIN-PC] [7716] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 停止性能测试套件
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 性能测试套件已停止
[2025-08-13 16:46:39.798 DBG] [IndustrialHMI] [LIN-PC] [7716] PerformanceTestSuite资源释放完成
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 停止集成测试套件
[2025-08-13 16:46:39.798 INF] [IndustrialHMI] [LIN-PC] [7716] 集成测试套件已停止
[2025-08-13 16:46:39.798 DBG] [IndustrialHMI] [LIN-PC] [7716] IntegrationTestSuite资源释放完成
[2025-08-13 16:46:39.798 DBG] [IndustrialHMI] [LIN-PC] [7716] TestFrameworkPresenter资源释放完成
[2025-08-13 16:46:39.800 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块资源释放完成
[2025-08-13 16:46:39.800 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 16:46:39.800 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理模块卸载事件: 测试框架模块
[2025-08-13 16:46:39.800 INF] [IndustrialHMI] [LIN-PC] [7716] 模块卸载成功: 测试框架模块
[2025-08-13 16:46:39.800 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载模块: 测试模块
[2025-08-13 16:46:39.801 INF] [IndustrialHMI] [LIN-PC] [7716] 开始停止模块: 测试模块
[2025-08-13 16:46:39.801 INF] [IndustrialHMI] [LIN-PC] [7716] 停止TestModulePresenter
[2025-08-13 16:46:39.801 DBG] [IndustrialHMI] [LIN-PC] [7716] 系统事件取消订阅完成
[2025-08-13 16:46:39.801 DBG] [IndustrialHMI] [LIN-PC] [7716] 模型状态变化: 模型停止完成
[2025-08-13 16:46:39.801 INF] [IndustrialHMI] [LIN-PC] [7716] TestModulePresenter停止完成
[2025-08-13 16:46:39.802 DBG] [IndustrialHMI] [LIN-PC] [7716] 事件取消订阅完成
[2025-08-13 16:46:39.802 INF] [IndustrialHMI] [LIN-PC] [7716] 模块停止完成: 测试模块
[2025-08-13 16:46:39.802 DBG] [IndustrialHMI] [LIN-PC] [7716] 处理系统事件: ModuleStopped
[2025-08-13 16:46:39.802 INF] [IndustrialHMI] [LIN-PC] [7716] 开始释放模块资源: 测试模块
[2025-08-13 16:46:39.802 INF] [IndustrialHMI] [LIN-PC] [7716] 释放TestModulePresenter资源
[2025-08-13 16:46:39.803 INF] [IndustrialHMI] [LIN-PC] [7716] TestModulePresenter资源释放完成
[2025-08-13 16:46:39.810 INF] [IndustrialHMI] [LIN-PC] [7716] 模块资源释放完成: 测试模块
[2025-08-13 16:46:39.810 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 16:46:39.812 ERR] [IndustrialHMI] [LIN-PC] [7716] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.set_SelectedText(String value)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 16:46:39.817 INF] [IndustrialHMI] [LIN-PC] [7716] 模块卸载成功: 测试模块
[2025-08-13 16:46:39.817 INF] [IndustrialHMI] [LIN-PC] [7716] 所有模块卸载完成
[2025-08-13 16:46:39.817 INF] [IndustrialHMI] [LIN-PC] [7716] 应用程序关闭流程完成
[2025-08-13 16:46:39.817 INF] [IndustrialHMI] [LIN-PC] [7716] 主窗体已关闭，资源清理完成
[2025-08-13 16:46:39.823 INF] [IndustrialHMI] [LIN-PC] [7716] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:39.823 INF] [IndustrialHMI] [LIN-PC] [7716] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:39.823 INF] [IndustrialHMI] [LIN-PC] [7716] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:39.824 INF] [IndustrialHMI] [LIN-PC] [7716] 开始释放应用程序资源
[2025-08-13 16:46:39.824 INF] [IndustrialHMI] [LIN-PC] [7716] 开始卸载所有模块
[2025-08-13 16:46:39.824 INF] [IndustrialHMI] [LIN-PC] [7716] 所有模块卸载完成
[2025-08-13 16:46:39.824 INF] [IndustrialHMI] [LIN-PC] [7716] 应用程序资源释放完成
[2025-08-13 16:46:39.824 INF] [IndustrialHMI] [LIN-PC] [7716] === 应用程序正常退出 ===
[2025-08-13 16:46:40.938 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:40.939 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 6
[2025-08-13 16:46:40.939 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:40
﻿[2025-08-13 16:46:40.936 INF] [IndustrialHMI] [LIN-PC] [31388] === 应用程序启动 ===
[2025-08-13 16:46:40.957 INF] [IndustrialHMI] [LIN-PC] [31388] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:40.958 INF] [IndustrialHMI] [LIN-PC] [31388] 启动参数: 
[2025-08-13 16:46:40.958 INF] [IndustrialHMI] [LIN-PC] [31388] 开始初始化应用程序
[2025-08-13 16:46:40.958 INF] [IndustrialHMI] [LIN-PC] [31388] 步骤1: 创建服务容器
[2025-08-13 16:46:40.962 INF] [IndustrialHMI] [LIN-PC] [31388] 开始创建DryIoc容器
[2025-08-13 16:46:40.973 DBG] [IndustrialHMI] [LIN-PC] [31388] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:40.976 DBG] [IndustrialHMI] [LIN-PC] [31388] 注册自定义日志记录器为单例
[2025-08-13 16:46:40.976 DBG] [IndustrialHMI] [LIN-PC] [31388] 注册EventAggregator为单例
[2025-08-13 16:46:40.981 DBG] [IndustrialHMI] [LIN-PC] [31388] 注册ConfigurationService为单例
[2025-08-13 16:46:40.992 DBG] [IndustrialHMI] [LIN-PC] [31388] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:41.023 DBG] [IndustrialHMI] [LIN-PC] [31388] 注册MainForm为单例
[2025-08-13 16:46:41.023 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始验证DryIoc容器配置
[2025-08-13 16:46:41.023 DBG] [IndustrialHMI] [LIN-PC] [31388] DryIoc容器配置验证通过
[2025-08-13 16:46:41.023 INF] [IndustrialHMI] [LIN-PC] [31388] DryIoc容器创建和配置完成
[2025-08-13 16:46:41.023 INF] [IndustrialHMI] [LIN-PC] [31388] 步骤2: 创建主窗体
[2025-08-13 16:46:41.023 INF] [IndustrialHMI] [LIN-PC] [31388] 步骤3: 创建模块加载器
[2025-08-13 16:46:41.023 INF] [IndustrialHMI] [LIN-PC] [31388] 步骤4: 加载模块
[2025-08-13 16:46:41.024 INF] [IndustrialHMI] [LIN-PC] [31388] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:41.024 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:41.031 DBG] [IndustrialHMI] [LIN-PC] [31388] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.033 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.034 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:41.035 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:41.035 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:41.035 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:41.035 INF] [IndustrialHMI] [LIN-PC] [31388] 开始初始化报警管理模块
[2025-08-13 16:46:41.037 DBG] [IndustrialHMI] [LIN-PC] [31388] 报警服务创建完成
[2025-08-13 16:46:41.044 DBG] [IndustrialHMI] [LIN-PC] [31388] 报警视图创建完成
[2025-08-13 16:46:41.045 DBG] [IndustrialHMI] [LIN-PC] [31388] AlarmModel 初始化完成
[2025-08-13 16:46:41.045 DBG] [IndustrialHMI] [LIN-PC] [31388] 报警模型创建完成
[2025-08-13 16:46:41.046 DBG] [IndustrialHMI] [LIN-PC] [31388] AlarmPresenter 初始化完成
[2025-08-13 16:46:41.046 DBG] [IndustrialHMI] [LIN-PC] [31388] 报警表示器创建完成
[2025-08-13 16:46:41.046 INF] [IndustrialHMI] [LIN-PC] [31388] MVP组件创建完成
[2025-08-13 16:46:41.046 DBG] [IndustrialHMI] [LIN-PC] [31388] 系统事件订阅完成
[2025-08-13 16:46:41.046 INF] [IndustrialHMI] [LIN-PC] [31388] 报警管理模块初始化完成
[2025-08-13 16:46:41.046 INF] [IndustrialHMI] [LIN-PC] [31388] 启动报警管理模块
[2025-08-13 16:46:41.046 INF] [IndustrialHMI] [LIN-PC] [31388] 启动报警监控
[2025-08-13 16:46:41.046 INF] [IndustrialHMI] [LIN-PC] [31388] 开始报警监控
[2025-08-13 16:46:41.047 INF] [IndustrialHMI] [LIN-PC] [31388] 报警管理模块启动完成
[2025-08-13 16:46:41.047 INF] [IndustrialHMI] [LIN-PC] [31388] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:41.048 DBG] [IndustrialHMI] [LIN-PC] [31388] 模块已加载: 报警管理
[2025-08-13 16:46:41.048 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:41.050 DBG] [IndustrialHMI] [LIN-PC] [31388] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:41.051 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:41.052 INF] [IndustrialHMI] [LIN-PC] [31388] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:41.053 INF] [IndustrialHMI] [LIN-PC] [31388] 初始化了 8 个测试用例
[2025-08-13 16:46:41.053 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModel 初始化完成
[2025-08-13 16:46:41.057 DBG] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestView 初始化完成
[2025-08-13 16:46:41.060 DBG] [IndustrialHMI] [LIN-PC] [31388] 视图数据初始化完成
[2025-08-13 16:46:41.060 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:41.060 DBG] [IndustrialHMI] [LIN-PC] [31388] MVP组件创建完成
[2025-08-13 16:46:41.060 DBG] [IndustrialHMI] [LIN-PC] [31388] 系统事件订阅完成
[2025-08-13 16:46:41.060 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModule 初始化完成
[2025-08-13 16:46:41.060 INF] [IndustrialHMI] [LIN-PC] [31388] 启动 CommunicationTestModule
[2025-08-13 16:46:41.060 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModule 启动完成
[2025-08-13 16:46:41.060 INF] [IndustrialHMI] [LIN-PC] [31388] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:41.060 DBG] [IndustrialHMI] [LIN-PC] [31388] 模块已加载: 通信测试
[2025-08-13 16:46:41.060 DBG] [IndustrialHMI] [LIN-PC] [31388] 收到模块加载事件: 通信测试
[2025-08-13 16:46:41.060 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 发现模块类型: 
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:41.062 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.064 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:41.065 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:41.065 INF] [IndustrialHMI] [LIN-PC] [31388] 开始初始化设备监控模块
[2025-08-13 16:46:41.068 DBG] [IndustrialHMI] [LIN-PC] [31388] 设备服务创建完成
[2025-08-13 16:46:41.070 DBG] [IndustrialHMI] [LIN-PC] [31388] 设备视图创建完成
[2025-08-13 16:46:41.070 DBG] [IndustrialHMI] [LIN-PC] [31388] DeviceModel 初始化完成
[2025-08-13 16:46:41.070 DBG] [IndustrialHMI] [LIN-PC] [31388] 设备模型创建完成
[2025-08-13 16:46:41.071 DBG] [IndustrialHMI] [LIN-PC] [31388] DevicePresenter 初始化完成
[2025-08-13 16:46:41.071 DBG] [IndustrialHMI] [LIN-PC] [31388] 设备表示器创建完成
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] MVP组件创建完成
[2025-08-13 16:46:41.071 DBG] [IndustrialHMI] [LIN-PC] [31388] 系统事件订阅完成
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 设备监控模块初始化完成
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 启动设备监控模块
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 用户请求开始设备监控
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 开始设备监控
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 设备监控已启动
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 设备监控模块启动完成
[2025-08-13 16:46:41.071 INF] [IndustrialHMI] [LIN-PC] [31388] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:41.071 DBG] [IndustrialHMI] [LIN-PC] [31388] 模块已加载: 设备监控
[2025-08-13 16:46:41.071 DBG] [IndustrialHMI] [LIN-PC] [31388] 收到模块加载事件: 设备监控
[2025-08-13 16:46:41.072 DBG] [IndustrialHMI] [LIN-PC] [31388] 模块已加载: 设备监控
[2025-08-13 16:46:41.072 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:41.073 DBG] [IndustrialHMI] [LIN-PC] [31388] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:41.074 INF] [IndustrialHMI] [LIN-PC] [31388] 开始初始化测试框架模块
[2025-08-13 16:46:41.074 DBG] [IndustrialHMI] [LIN-PC] [31388] ConfigurationService未注入（可选）
[2025-08-13 16:46:41.718 DBG] [IndustrialHMI] [LIN-PC] [31388] 初始化TestFrameworkPresenter
[2025-08-13 16:46:41.735 DBG] [IndustrialHMI] [LIN-PC] [31388] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:41.736 DBG] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块事件订阅完成
[2025-08-13 16:46:41.736 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块初始化完成
[2025-08-13 16:46:41.736 INF] [IndustrialHMI] [LIN-PC] [31388] 启动测试框架模块
[2025-08-13 16:46:41.736 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 模型已启动
[2025-08-13 16:46:41.737 DBG] [IndustrialHMI] [LIN-PC] [31388] 加载TestFramework数据
[2025-08-13 16:46:41.737 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.738 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 数据加载完成
[2025-08-13 16:46:41.738 DBG] [IndustrialHMI] [LIN-PC] [31388] TestFramework数据加载完成
[2025-08-13 16:46:41.738 INF] [IndustrialHMI] [LIN-PC] [31388] 初始化集成测试套件
[2025-08-13 16:46:41.738 INF] [IndustrialHMI] [LIN-PC] [31388] 集成测试套件初始化完成
[2025-08-13 16:46:41.738 INF] [IndustrialHMI] [LIN-PC] [31388] 初始化性能测试套件
[2025-08-13 16:46:41.862 INF] [IndustrialHMI] [LIN-PC] [31388] 性能测试套件初始化完成
[2025-08-13 16:46:41.862 INF] [IndustrialHMI] [LIN-PC] [31388] 初始化内存泄漏测试套件
[2025-08-13 16:46:41.862 INF] [IndustrialHMI] [LIN-PC] [31388] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:41.862 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块启动完成
[2025-08-13 16:46:41.862 INF] [IndustrialHMI] [LIN-PC] [31388] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:41.862 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:41.863 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.863 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:41.863 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 发现模块类型: TestModuleMain
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 开始加载模块: TestModuleMain
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:41.865 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:41.866 INF] [IndustrialHMI] [LIN-PC] [31388] 开始初始化模块: 测试模块
[2025-08-13 16:46:41.866 WRN] [IndustrialHMI] [LIN-PC] [31388] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:41.866 DBG] [IndustrialHMI] [LIN-PC] [31388] 依赖注入验证通过
[2025-08-13 16:46:41.866 DBG] [IndustrialHMI] [LIN-PC] [31388] 创建TestModuleModel成功
[2025-08-13 16:46:41.868 DBG] [IndustrialHMI] [LIN-PC] [31388] 创建TestModuleView成功
[2025-08-13 16:46:41.869 DBG] [IndustrialHMI] [LIN-PC] [31388] TestModulePresenter创建完成
[2025-08-13 16:46:41.869 DBG] [IndustrialHMI] [LIN-PC] [31388] 创建TestModulePresenter成功
[2025-08-13 16:46:41.869 DBG] [IndustrialHMI] [LIN-PC] [31388] 事件订阅完成
[2025-08-13 16:46:41.869 INF] [IndustrialHMI] [LIN-PC] [31388] 模块初始化完成: 测试模块
[2025-08-13 16:46:41.869 INF] [IndustrialHMI] [LIN-PC] [31388] 开始启动模块: 测试模块
[2025-08-13 16:46:41.869 INF] [IndustrialHMI] [LIN-PC] [31388] 启动TestModulePresenter
[2025-08-13 16:46:41.878 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 模型启动完成
[2025-08-13 16:46:41.878 DBG] [IndustrialHMI] [LIN-PC] [31388] 系统事件订阅完成
[2025-08-13 16:46:41.880 INF] [IndustrialHMI] [LIN-PC] [31388] TestModulePresenter启动完成
[2025-08-13 16:46:41.880 INF] [IndustrialHMI] [LIN-PC] [31388] 模块启动完成: 测试模块
[2025-08-13 16:46:41.881 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理系统事件: ModuleStarted
[2025-08-13 16:46:41.881 INF] [IndustrialHMI] [LIN-PC] [31388] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:41.881 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:41.882 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.882 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:41.884 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理模块加载事件: 测试模块
[2025-08-13 16:46:41.884 INF] [IndustrialHMI] [LIN-PC] [31388] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:41.884 INF] [IndustrialHMI] [LIN-PC] [31388] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:41.885 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:41.885 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:41.885 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:41.886 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:41.887 DBG] [IndustrialHMI] [LIN-PC] [31388] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:41.887 INF] [IndustrialHMI] [LIN-PC] [31388] 步骤5: 初始化主窗体
[2025-08-13 16:46:41.887 DBG] [IndustrialHMI] [LIN-PC] [31388] 主窗体初始化完成
[2025-08-13 16:46:41.887 INF] [IndustrialHMI] [LIN-PC] [31388] 应用程序初始化完成
[2025-08-13 16:46:41.887 INF] [IndustrialHMI] [LIN-PC] [31388] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:41.887 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到系统启动事件
[2025-08-13 16:46:41.888 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.888 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:41.888 INF] [IndustrialHMI] [LIN-PC] [31388] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:41.889 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:41.950 DBG] [IndustrialHMI] [LIN-PC] [31388] 主窗体事件订阅完成
[2025-08-13 16:46:41.953 INF] [IndustrialHMI] [LIN-PC] [31388] 用户请求关闭应用程序
[2025-08-13 16:46:41.953 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:41.954 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.954 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 16:46:41.954 INF] [IndustrialHMI] [LIN-PC] [31388] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:41.955 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到系统关闭事件
[2025-08-13 16:46:41.967 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据变化事件处理完成
[2025-08-13 16:46:41.969 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据变化事件处理完成
[2025-08-13 16:46:41.970 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 系统关闭前清理完成
[2025-08-13 16:46:41.970 INF] [IndustrialHMI] [LIN-PC] [31388] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:41.970 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载所有模块
[2025-08-13 16:46:41.971 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载模块: 报警管理
[2025-08-13 16:46:41.971 INF] [IndustrialHMI] [LIN-PC] [31388] 停止报警管理模块
[2025-08-13 16:46:41.971 INF] [IndustrialHMI] [LIN-PC] [31388] 停止报警监控
[2025-08-13 16:46:41.971 INF] [IndustrialHMI] [LIN-PC] [31388] 停止报警监控
[2025-08-13 16:46:41.971 INF] [IndustrialHMI] [LIN-PC] [31388] 报警管理模块停止完成
[2025-08-13 16:46:41.971 INF] [IndustrialHMI] [LIN-PC] [31388] 开始释放报警管理模块资源
[2025-08-13 16:46:41.972 INF] [IndustrialHMI] [LIN-PC] [31388] 停止报警监控
[2025-08-13 16:46:41.972 INF] [IndustrialHMI] [LIN-PC] [31388] 停止报警监控
[2025-08-13 16:46:41.972 DBG] [IndustrialHMI] [LIN-PC] [31388] AlarmPresenter 资源释放完成
[2025-08-13 16:46:41.972 INF] [IndustrialHMI] [LIN-PC] [31388] 停止报警监控
[2025-08-13 16:46:41.973 DBG] [IndustrialHMI] [LIN-PC] [31388] AlarmModel 资源释放完成
[2025-08-13 16:46:41.980 INF] [IndustrialHMI] [LIN-PC] [31388] 报警管理模块资源释放完成
[2025-08-13 16:46:41.981 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 16:46:41.981 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.982 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:41.983 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理模块卸载事件: 报警管理
[2025-08-13 16:46:41.983 INF] [IndustrialHMI] [LIN-PC] [31388] 模块卸载成功: 报警管理
[2025-08-13 16:46:41.983 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载模块: 通信测试
[2025-08-13 16:46:41.983 INF] [IndustrialHMI] [LIN-PC] [31388] 停止 CommunicationTestModule
[2025-08-13 16:46:41.983 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据变化: EventMonitoring
[2025-08-13 16:46:41.983 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:41.983 INF] [IndustrialHMI] [LIN-PC] [31388] 测试已停止
[2025-08-13 16:46:41.983 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModule 停止完成
[2025-08-13 16:46:41.984 INF] [IndustrialHMI] [LIN-PC] [31388] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:46:41.984 INF] [IndustrialHMI] [LIN-PC] [31388] 停止 CommunicationTestModule
[2025-08-13 16:46:41.984 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据变化: EventMonitoring
[2025-08-13 16:46:41.984 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:41.984 INF] [IndustrialHMI] [LIN-PC] [31388] 测试已停止
[2025-08-13 16:46:41.984 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModule 停止完成
[2025-08-13 16:46:41.984 DBG] [IndustrialHMI] [LIN-PC] [31388] 系统事件订阅已取消
[2025-08-13 16:46:41.984 DBG] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:46:41.985 DBG] [IndustrialHMI] [LIN-PC] [31388] EventMonitor 资源释放完成
[2025-08-13 16:46:41.985 INF] [IndustrialHMI] [LIN-PC] [31388] 测试已停止
[2025-08-13 16:46:41.985 DBG] [IndustrialHMI] [LIN-PC] [31388] TestCaseManager 资源释放完成
[2025-08-13 16:46:41.985 DBG] [IndustrialHMI] [LIN-PC] [31388] PerformanceMonitor 资源释放完成
[2025-08-13 16:46:41.985 DBG] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModel 资源释放完成
[2025-08-13 16:46:41.985 INF] [IndustrialHMI] [LIN-PC] [31388] CommunicationTestModule 资源释放完成
[2025-08-13 16:46:41.985 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 16:46:41.986 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.986 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:41.987 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理模块卸载事件: 通信测试
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 模块卸载成功: 通信测试
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载模块: 设备监控
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 停止设备监控模块
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 用户请求停止设备监控
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 停止设备监控
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 设备监控已停止
[2025-08-13 16:46:41.987 INF] [IndustrialHMI] [LIN-PC] [31388] 设备监控模块停止完成
[2025-08-13 16:46:41.988 INF] [IndustrialHMI] [LIN-PC] [31388] 开始释放设备监控模块资源
[2025-08-13 16:46:41.989 DBG] [IndustrialHMI] [LIN-PC] [31388] DevicePresenter 资源释放完成
[2025-08-13 16:46:41.989 INF] [IndustrialHMI] [LIN-PC] [31388] 停止设备监控
[2025-08-13 16:46:41.989 DBG] [IndustrialHMI] [LIN-PC] [31388] DeviceModel 资源释放完成
[2025-08-13 16:46:41.989 INF] [IndustrialHMI] [LIN-PC] [31388] 设备监控模块资源释放完成
[2025-08-13 16:46:41.989 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 16:46:41.990 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型数据已更新，视图已刷新
[2025-08-13 16:46:41.990 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:41.991 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理模块卸载事件: 设备监控
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 模块卸载成功: 设备监控
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载模块: 测试框架模块
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 停止测试框架模块
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 停止内存泄漏测试套件
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 内存泄漏测试套件已停止
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 停止性能测试套件
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 性能测试套件已停止
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 停止集成测试套件
[2025-08-13 16:46:41.991 INF] [IndustrialHMI] [LIN-PC] [31388] 集成测试套件已停止
[2025-08-13 16:46:41.992 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 模型已停止
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块停止完成
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 开始释放测试框架模块资源
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 停止内存泄漏测试套件
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 内存泄漏测试套件已停止
[2025-08-13 16:46:41.992 DBG] [IndustrialHMI] [LIN-PC] [31388] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 停止性能测试套件
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 性能测试套件已停止
[2025-08-13 16:46:41.992 DBG] [IndustrialHMI] [LIN-PC] [31388] PerformanceTestSuite资源释放完成
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 停止集成测试套件
[2025-08-13 16:46:41.992 INF] [IndustrialHMI] [LIN-PC] [31388] 集成测试套件已停止
[2025-08-13 16:46:41.992 DBG] [IndustrialHMI] [LIN-PC] [31388] IntegrationTestSuite资源释放完成
[2025-08-13 16:46:41.993 DBG] [IndustrialHMI] [LIN-PC] [31388] TestFrameworkPresenter资源释放完成
[2025-08-13 16:46:41.993 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块资源释放完成
[2025-08-13 16:46:41.993 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 16:46:41.994 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理模块卸载事件: 测试框架模块
[2025-08-13 16:46:41.994 INF] [IndustrialHMI] [LIN-PC] [31388] 模块卸载成功: 测试框架模块
[2025-08-13 16:46:41.994 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载模块: 测试模块
[2025-08-13 16:46:41.994 INF] [IndustrialHMI] [LIN-PC] [31388] 开始停止模块: 测试模块
[2025-08-13 16:46:41.994 INF] [IndustrialHMI] [LIN-PC] [31388] 停止TestModulePresenter
[2025-08-13 16:46:41.994 DBG] [IndustrialHMI] [LIN-PC] [31388] 系统事件取消订阅完成
[2025-08-13 16:46:41.995 DBG] [IndustrialHMI] [LIN-PC] [31388] 模型状态变化: 模型停止完成
[2025-08-13 16:46:41.995 INF] [IndustrialHMI] [LIN-PC] [31388] TestModulePresenter停止完成
[2025-08-13 16:46:41.995 DBG] [IndustrialHMI] [LIN-PC] [31388] 事件取消订阅完成
[2025-08-13 16:46:41.995 INF] [IndustrialHMI] [LIN-PC] [31388] 模块停止完成: 测试模块
[2025-08-13 16:46:41.995 DBG] [IndustrialHMI] [LIN-PC] [31388] 处理系统事件: ModuleStopped
[2025-08-13 16:46:41.995 INF] [IndustrialHMI] [LIN-PC] [31388] 开始释放模块资源: 测试模块
[2025-08-13 16:46:41.996 INF] [IndustrialHMI] [LIN-PC] [31388] 释放TestModulePresenter资源
[2025-08-13 16:46:41.996 INF] [IndustrialHMI] [LIN-PC] [31388] TestModulePresenter资源释放完成
[2025-08-13 16:46:42.002 INF] [IndustrialHMI] [LIN-PC] [31388] 模块资源释放完成: 测试模块
[2025-08-13 16:46:42.002 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 16:46:42.005 ERR] [IndustrialHMI] [LIN-PC] [31388] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.set_SelectedText(String value)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 16:46:42.009 INF] [IndustrialHMI] [LIN-PC] [31388] 模块卸载成功: 测试模块
[2025-08-13 16:46:42.009 INF] [IndustrialHMI] [LIN-PC] [31388] 所有模块卸载完成
[2025-08-13 16:46:42.009 INF] [IndustrialHMI] [LIN-PC] [31388] 应用程序关闭流程完成
[2025-08-13 16:46:42.010 INF] [IndustrialHMI] [LIN-PC] [31388] 主窗体已关闭，资源清理完成
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 开始释放应用程序资源
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 开始卸载所有模块
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 所有模块卸载完成
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] 应用程序资源释放完成
[2025-08-13 16:46:42.016 INF] [IndustrialHMI] [LIN-PC] [31388] === 应用程序正常退出 ===
﻿[2025-08-13 16:46:43.137 INF] [IndustrialHMI] [LIN-PC] [24472] === 应用程序启动 ===
[2025-08-13 16:46:43.159 INF] [IndustrialHMI] [LIN-PC] [24472] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:43.159 INF] [IndustrialHMI] [LIN-PC] [24472] 启动参数: 
[2025-08-13 16:46:43.159 INF] [IndustrialHMI] [LIN-PC] [24472] 开始初始化应用程序
[2025-08-13 16:46:43.159 INF] [IndustrialHMI] [LIN-PC] [24472] 步骤1: 创建服务容器
[2025-08-13 16:46:43.164 INF] [IndustrialHMI] [LIN-PC] [24472] 开始创建DryIoc容器
[2025-08-13 16:46:43.177 DBG] [IndustrialHMI] [LIN-PC] [24472] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:43.180 DBG] [IndustrialHMI] [LIN-PC] [24472] 注册自定义日志记录器为单例
[2025-08-13 16:46:43.180 DBG] [IndustrialHMI] [LIN-PC] [24472] 注册EventAggregator为单例
[2025-08-13 16:46:43.186 DBG] [IndustrialHMI] [LIN-PC] [24472] 注册ConfigurationService为单例
[2025-08-13 16:46:43.195 DBG] [IndustrialHMI] [LIN-PC] [24472] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:43.225 DBG] [IndustrialHMI] [LIN-PC] [24472] 注册MainForm为单例
[2025-08-13 16:46:43.226 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始验证DryIoc容器配置
[2025-08-13 16:46:43.226 DBG] [IndustrialHMI] [LIN-PC] [24472] DryIoc容器配置验证通过
[2025-08-13 16:46:43.226 INF] [IndustrialHMI] [LIN-PC] [24472] DryIoc容器创建和配置完成
[2025-08-13 16:46:43.226 INF] [IndustrialHMI] [LIN-PC] [24472] 步骤2: 创建主窗体
[2025-08-13 16:46:43.226 INF] [IndustrialHMI] [LIN-PC] [24472] 步骤3: 创建模块加载器
[2025-08-13 16:46:43.226 INF] [IndustrialHMI] [LIN-PC] [24472] 步骤4: 加载模块
[2025-08-13 16:46:43.226 INF] [IndustrialHMI] [LIN-PC] [24472] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:43.227 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:43.234 DBG] [IndustrialHMI] [LIN-PC] [24472] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:43.235 DBG] [IndustrialHMI] [LIN-PC] [24472] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:43.235 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:43.235 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:43.236 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:43.237 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:43.237 INF] [IndustrialHMI] [LIN-PC] [24472] 开始初始化报警管理模块
[2025-08-13 16:46:43.239 DBG] [IndustrialHMI] [LIN-PC] [24472] 报警服务创建完成
[2025-08-13 16:46:43.245 DBG] [IndustrialHMI] [LIN-PC] [24472] 报警视图创建完成
[2025-08-13 16:46:43.247 DBG] [IndustrialHMI] [LIN-PC] [24472] AlarmModel 初始化完成
[2025-08-13 16:46:43.247 DBG] [IndustrialHMI] [LIN-PC] [24472] 报警模型创建完成
[2025-08-13 16:46:43.247 DBG] [IndustrialHMI] [LIN-PC] [24472] AlarmPresenter 初始化完成
[2025-08-13 16:46:43.248 DBG] [IndustrialHMI] [LIN-PC] [24472] 报警表示器创建完成
[2025-08-13 16:46:43.248 INF] [IndustrialHMI] [LIN-PC] [24472] MVP组件创建完成
[2025-08-13 16:46:43.248 DBG] [IndustrialHMI] [LIN-PC] [24472] 系统事件订阅完成
[2025-08-13 16:46:43.248 INF] [IndustrialHMI] [LIN-PC] [24472] 报警管理模块初始化完成
[2025-08-13 16:46:43.248 INF] [IndustrialHMI] [LIN-PC] [24472] 启动报警管理模块
[2025-08-13 16:46:43.248 INF] [IndustrialHMI] [LIN-PC] [24472] 启动报警监控
[2025-08-13 16:46:43.248 INF] [IndustrialHMI] [LIN-PC] [24472] 开始报警监控
[2025-08-13 16:46:43.249 INF] [IndustrialHMI] [LIN-PC] [24472] 报警管理模块启动完成
[2025-08-13 16:46:43.249 INF] [IndustrialHMI] [LIN-PC] [24472] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:43.250 DBG] [IndustrialHMI] [LIN-PC] [24472] 模块已加载: 报警管理
[2025-08-13 16:46:43.250 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:43.251 DBG] [IndustrialHMI] [LIN-PC] [24472] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:43.252 DBG] [IndustrialHMI] [LIN-PC] [24472] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:43.253 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:43.253 INF] [IndustrialHMI] [LIN-PC] [24472] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:43.254 INF] [IndustrialHMI] [LIN-PC] [24472] 初始化了 8 个测试用例
[2025-08-13 16:46:43.255 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModel 初始化完成
[2025-08-13 16:46:43.259 DBG] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestView 初始化完成
[2025-08-13 16:46:43.261 DBG] [IndustrialHMI] [LIN-PC] [24472] 视图数据初始化完成
[2025-08-13 16:46:43.261 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:43.261 DBG] [IndustrialHMI] [LIN-PC] [24472] MVP组件创建完成
[2025-08-13 16:46:43.262 DBG] [IndustrialHMI] [LIN-PC] [24472] 系统事件订阅完成
[2025-08-13 16:46:43.262 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModule 初始化完成
[2025-08-13 16:46:43.262 INF] [IndustrialHMI] [LIN-PC] [24472] 启动 CommunicationTestModule
[2025-08-13 16:46:43.262 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModule 启动完成
[2025-08-13 16:46:43.262 INF] [IndustrialHMI] [LIN-PC] [24472] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:43.262 DBG] [IndustrialHMI] [LIN-PC] [24472] 模块已加载: 通信测试
[2025-08-13 16:46:43.262 DBG] [IndustrialHMI] [LIN-PC] [24472] 收到模块加载事件: 通信测试
[2025-08-13 16:46:43.262 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:43.263 DBG] [IndustrialHMI] [LIN-PC] [24472] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 发现模块类型: 
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:43.264 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:43.265 DBG] [IndustrialHMI] [LIN-PC] [24472] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:43.266 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:43.266 INF] [IndustrialHMI] [LIN-PC] [24472] 开始初始化设备监控模块
[2025-08-13 16:46:43.267 DBG] [IndustrialHMI] [LIN-PC] [24472] 设备服务创建完成
[2025-08-13 16:46:43.269 DBG] [IndustrialHMI] [LIN-PC] [24472] 设备视图创建完成
[2025-08-13 16:46:43.269 DBG] [IndustrialHMI] [LIN-PC] [24472] DeviceModel 初始化完成
[2025-08-13 16:46:43.269 DBG] [IndustrialHMI] [LIN-PC] [24472] 设备模型创建完成
[2025-08-13 16:46:43.270 DBG] [IndustrialHMI] [LIN-PC] [24472] DevicePresenter 初始化完成
[2025-08-13 16:46:43.270 DBG] [IndustrialHMI] [LIN-PC] [24472] 设备表示器创建完成
[2025-08-13 16:46:43.270 INF] [IndustrialHMI] [LIN-PC] [24472] MVP组件创建完成
[2025-08-13 16:46:43.270 DBG] [IndustrialHMI] [LIN-PC] [24472] 系统事件订阅完成
[2025-08-13 16:46:43.270 INF] [IndustrialHMI] [LIN-PC] [24472] 设备监控模块初始化完成
[2025-08-13 16:46:43.270 INF] [IndustrialHMI] [LIN-PC] [24472] 启动设备监控模块
[2025-08-13 16:46:43.270 INF] [IndustrialHMI] [LIN-PC] [24472] 用户请求开始设备监控
[2025-08-13 16:46:43.270 INF] [IndustrialHMI] [LIN-PC] [24472] 开始设备监控
[2025-08-13 16:46:43.271 INF] [IndustrialHMI] [LIN-PC] [24472] 设备监控已启动
[2025-08-13 16:46:43.271 INF] [IndustrialHMI] [LIN-PC] [24472] 设备监控模块启动完成
[2025-08-13 16:46:43.271 INF] [IndustrialHMI] [LIN-PC] [24472] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:43.271 DBG] [IndustrialHMI] [LIN-PC] [24472] 模块已加载: 设备监控
[2025-08-13 16:46:43.271 DBG] [IndustrialHMI] [LIN-PC] [24472] 收到模块加载事件: 设备监控
[2025-08-13 16:46:43.271 DBG] [IndustrialHMI] [LIN-PC] [24472] 模块已加载: 设备监控
[2025-08-13 16:46:43.271 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:43.272 DBG] [IndustrialHMI] [LIN-PC] [24472] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:43.273 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:43.273 INF] [IndustrialHMI] [LIN-PC] [24472] 开始初始化测试框架模块
[2025-08-13 16:46:43.274 DBG] [IndustrialHMI] [LIN-PC] [24472] ConfigurationService未注入（可选）
[2025-08-13 16:46:43.903 DBG] [IndustrialHMI] [LIN-PC] [24472] 初始化TestFrameworkPresenter
[2025-08-13 16:46:43.920 DBG] [IndustrialHMI] [LIN-PC] [24472] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:43.920 DBG] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块事件订阅完成
[2025-08-13 16:46:43.921 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块初始化完成
[2025-08-13 16:46:43.921 INF] [IndustrialHMI] [LIN-PC] [24472] 启动测试框架模块
[2025-08-13 16:46:43.921 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 模型已启动
[2025-08-13 16:46:43.921 DBG] [IndustrialHMI] [LIN-PC] [24472] 加载TestFramework数据
[2025-08-13 16:46:43.922 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:43.922 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 数据加载完成
[2025-08-13 16:46:43.923 DBG] [IndustrialHMI] [LIN-PC] [24472] TestFramework数据加载完成
[2025-08-13 16:46:43.923 INF] [IndustrialHMI] [LIN-PC] [24472] 初始化集成测试套件
[2025-08-13 16:46:43.923 INF] [IndustrialHMI] [LIN-PC] [24472] 集成测试套件初始化完成
[2025-08-13 16:46:43.923 INF] [IndustrialHMI] [LIN-PC] [24472] 初始化性能测试套件
[2025-08-13 16:46:44.045 INF] [IndustrialHMI] [LIN-PC] [24472] 性能测试套件初始化完成
[2025-08-13 16:46:44.045 INF] [IndustrialHMI] [LIN-PC] [24472] 初始化内存泄漏测试套件
[2025-08-13 16:46:44.045 INF] [IndustrialHMI] [LIN-PC] [24472] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:44.045 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块启动完成
[2025-08-13 16:46:44.046 INF] [IndustrialHMI] [LIN-PC] [24472] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:44.046 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:44.047 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.047 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:44.047 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 发现模块类型: TestModuleMain
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:44.050 DBG] [IndustrialHMI] [LIN-PC] [24472] 开始加载模块: TestModuleMain
[2025-08-13 16:46:44.051 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:44.051 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:44.051 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:44.051 INF] [IndustrialHMI] [LIN-PC] [24472] 开始初始化模块: 测试模块
[2025-08-13 16:46:44.051 WRN] [IndustrialHMI] [LIN-PC] [24472] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:44.051 DBG] [IndustrialHMI] [LIN-PC] [24472] 依赖注入验证通过
[2025-08-13 16:46:44.051 DBG] [IndustrialHMI] [LIN-PC] [24472] 创建TestModuleModel成功
[2025-08-13 16:46:44.053 DBG] [IndustrialHMI] [LIN-PC] [24472] 创建TestModuleView成功
[2025-08-13 16:46:44.054 DBG] [IndustrialHMI] [LIN-PC] [24472] TestModulePresenter创建完成
[2025-08-13 16:46:44.054 DBG] [IndustrialHMI] [LIN-PC] [24472] 创建TestModulePresenter成功
[2025-08-13 16:46:44.054 DBG] [IndustrialHMI] [LIN-PC] [24472] 事件订阅完成
[2025-08-13 16:46:44.054 INF] [IndustrialHMI] [LIN-PC] [24472] 模块初始化完成: 测试模块
[2025-08-13 16:46:44.054 INF] [IndustrialHMI] [LIN-PC] [24472] 开始启动模块: 测试模块
[2025-08-13 16:46:44.055 INF] [IndustrialHMI] [LIN-PC] [24472] 启动TestModulePresenter
[2025-08-13 16:46:44.061 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 模型启动完成
[2025-08-13 16:46:44.062 DBG] [IndustrialHMI] [LIN-PC] [24472] 系统事件订阅完成
[2025-08-13 16:46:44.062 INF] [IndustrialHMI] [LIN-PC] [24472] TestModulePresenter启动完成
[2025-08-13 16:46:44.062 INF] [IndustrialHMI] [LIN-PC] [24472] 模块启动完成: 测试模块
[2025-08-13 16:46:44.063 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理系统事件: ModuleStarted
[2025-08-13 16:46:44.063 INF] [IndustrialHMI] [LIN-PC] [24472] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:44.063 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:44.063 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.064 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:44.065 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理模块加载事件: 测试模块
[2025-08-13 16:46:44.065 INF] [IndustrialHMI] [LIN-PC] [24472] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:44.065 INF] [IndustrialHMI] [LIN-PC] [24472] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:44.066 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:44.066 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:44.066 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:44.068 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:44.068 DBG] [IndustrialHMI] [LIN-PC] [24472] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:44.068 INF] [IndustrialHMI] [LIN-PC] [24472] 步骤5: 初始化主窗体
[2025-08-13 16:46:44.068 DBG] [IndustrialHMI] [LIN-PC] [24472] 主窗体初始化完成
[2025-08-13 16:46:44.068 INF] [IndustrialHMI] [LIN-PC] [24472] 应用程序初始化完成
[2025-08-13 16:46:44.068 INF] [IndustrialHMI] [LIN-PC] [24472] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:44.069 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到系统启动事件
[2025-08-13 16:46:44.069 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.069 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:44.069 INF] [IndustrialHMI] [LIN-PC] [24472] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:44.070 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:44.127 DBG] [IndustrialHMI] [LIN-PC] [24472] 主窗体事件订阅完成
[2025-08-13 16:46:44.129 INF] [IndustrialHMI] [LIN-PC] [24472] 用户请求关闭应用程序
[2025-08-13 16:46:44.129 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:44.130 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.131 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 16:46:44.131 INF] [IndustrialHMI] [LIN-PC] [24472] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:44.132 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到系统关闭事件
[2025-08-13 16:46:44.146 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据变化事件处理完成
[2025-08-13 16:46:44.146 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据变化事件处理完成
[2025-08-13 16:46:44.148 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 系统关闭前清理完成
[2025-08-13 16:46:44.148 INF] [IndustrialHMI] [LIN-PC] [24472] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:44.149 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载所有模块
[2025-08-13 16:46:44.149 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载模块: 报警管理
[2025-08-13 16:46:44.149 INF] [IndustrialHMI] [LIN-PC] [24472] 停止报警管理模块
[2025-08-13 16:46:44.149 INF] [IndustrialHMI] [LIN-PC] [24472] 停止报警监控
[2025-08-13 16:46:44.149 INF] [IndustrialHMI] [LIN-PC] [24472] 停止报警监控
[2025-08-13 16:46:44.149 INF] [IndustrialHMI] [LIN-PC] [24472] 报警管理模块停止完成
[2025-08-13 16:46:44.150 INF] [IndustrialHMI] [LIN-PC] [24472] 开始释放报警管理模块资源
[2025-08-13 16:46:44.150 INF] [IndustrialHMI] [LIN-PC] [24472] 停止报警监控
[2025-08-13 16:46:44.150 INF] [IndustrialHMI] [LIN-PC] [24472] 停止报警监控
[2025-08-13 16:46:44.151 DBG] [IndustrialHMI] [LIN-PC] [24472] AlarmPresenter 资源释放完成
[2025-08-13 16:46:44.151 INF] [IndustrialHMI] [LIN-PC] [24472] 停止报警监控
[2025-08-13 16:46:44.151 DBG] [IndustrialHMI] [LIN-PC] [24472] AlarmModel 资源释放完成
[2025-08-13 16:46:44.161 INF] [IndustrialHMI] [LIN-PC] [24472] 报警管理模块资源释放完成
[2025-08-13 16:46:44.162 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 16:46:44.162 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.163 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:44.164 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理模块卸载事件: 报警管理
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 模块卸载成功: 报警管理
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载模块: 通信测试
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 停止 CommunicationTestModule
[2025-08-13 16:46:44.165 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据变化: EventMonitoring
[2025-08-13 16:46:44.165 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 测试已停止
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModule 停止完成
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 停止 CommunicationTestModule
[2025-08-13 16:46:44.165 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据变化: EventMonitoring
[2025-08-13 16:46:44.165 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] 测试已停止
[2025-08-13 16:46:44.165 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModule 停止完成
[2025-08-13 16:46:44.166 DBG] [IndustrialHMI] [LIN-PC] [24472] 系统事件订阅已取消
[2025-08-13 16:46:44.166 DBG] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:46:44.167 DBG] [IndustrialHMI] [LIN-PC] [24472] EventMonitor 资源释放完成
[2025-08-13 16:46:44.167 INF] [IndustrialHMI] [LIN-PC] [24472] 测试已停止
[2025-08-13 16:46:44.167 DBG] [IndustrialHMI] [LIN-PC] [24472] TestCaseManager 资源释放完成
[2025-08-13 16:46:44.167 DBG] [IndustrialHMI] [LIN-PC] [24472] PerformanceMonitor 资源释放完成
[2025-08-13 16:46:44.167 DBG] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModel 资源释放完成
[2025-08-13 16:46:44.167 INF] [IndustrialHMI] [LIN-PC] [24472] CommunicationTestModule 资源释放完成
[2025-08-13 16:46:44.167 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 16:46:44.168 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.169 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:44.170 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理模块卸载事件: 通信测试
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 模块卸载成功: 通信测试
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载模块: 设备监控
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 停止设备监控模块
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 用户请求停止设备监控
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 停止设备监控
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 设备监控已停止
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 设备监控模块停止完成
[2025-08-13 16:46:44.170 INF] [IndustrialHMI] [LIN-PC] [24472] 开始释放设备监控模块资源
[2025-08-13 16:46:44.171 DBG] [IndustrialHMI] [LIN-PC] [24472] DevicePresenter 资源释放完成
[2025-08-13 16:46:44.171 INF] [IndustrialHMI] [LIN-PC] [24472] 停止设备监控
[2025-08-13 16:46:44.171 DBG] [IndustrialHMI] [LIN-PC] [24472] DeviceModel 资源释放完成
[2025-08-13 16:46:44.171 INF] [IndustrialHMI] [LIN-PC] [24472] 设备监控模块资源释放完成
[2025-08-13 16:46:44.171 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 16:46:44.173 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:44.173 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:44.174 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理模块卸载事件: 设备监控
[2025-08-13 16:46:44.174 INF] [IndustrialHMI] [LIN-PC] [24472] 模块卸载成功: 设备监控
[2025-08-13 16:46:44.174 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载模块: 测试框架模块
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 停止测试框架模块
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 停止内存泄漏测试套件
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 内存泄漏测试套件已停止
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 停止性能测试套件
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 性能测试套件已停止
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 停止集成测试套件
[2025-08-13 16:46:44.175 INF] [IndustrialHMI] [LIN-PC] [24472] 集成测试套件已停止
[2025-08-13 16:46:44.176 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 模型已停止
[2025-08-13 16:46:44.176 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块停止完成
[2025-08-13 16:46:44.176 INF] [IndustrialHMI] [LIN-PC] [24472] 开始释放测试框架模块资源
[2025-08-13 16:46:44.177 INF] [IndustrialHMI] [LIN-PC] [24472] 停止内存泄漏测试套件
[2025-08-13 16:46:44.177 INF] [IndustrialHMI] [LIN-PC] [24472] 内存泄漏测试套件已停止
[2025-08-13 16:46:44.177 DBG] [IndustrialHMI] [LIN-PC] [24472] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:46:44.177 INF] [IndustrialHMI] [LIN-PC] [24472] 停止性能测试套件
[2025-08-13 16:46:44.177 INF] [IndustrialHMI] [LIN-PC] [24472] 性能测试套件已停止
[2025-08-13 16:46:44.177 DBG] [IndustrialHMI] [LIN-PC] [24472] PerformanceTestSuite资源释放完成
[2025-08-13 16:46:44.177 INF] [IndustrialHMI] [LIN-PC] [24472] 停止集成测试套件
[2025-08-13 16:46:44.177 INF] [IndustrialHMI] [LIN-PC] [24472] 集成测试套件已停止
[2025-08-13 16:46:44.177 DBG] [IndustrialHMI] [LIN-PC] [24472] IntegrationTestSuite资源释放完成
[2025-08-13 16:46:44.177 DBG] [IndustrialHMI] [LIN-PC] [24472] TestFrameworkPresenter资源释放完成
[2025-08-13 16:46:44.178 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块资源释放完成
[2025-08-13 16:46:44.178 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 16:46:44.179 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理模块卸载事件: 测试框架模块
[2025-08-13 16:46:44.179 INF] [IndustrialHMI] [LIN-PC] [24472] 模块卸载成功: 测试框架模块
[2025-08-13 16:46:44.179 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载模块: 测试模块
[2025-08-13 16:46:44.179 INF] [IndustrialHMI] [LIN-PC] [24472] 开始停止模块: 测试模块
[2025-08-13 16:46:44.179 INF] [IndustrialHMI] [LIN-PC] [24472] 停止TestModulePresenter
[2025-08-13 16:46:44.179 DBG] [IndustrialHMI] [LIN-PC] [24472] 系统事件取消订阅完成
[2025-08-13 16:46:44.180 DBG] [IndustrialHMI] [LIN-PC] [24472] 模型状态变化: 模型停止完成
[2025-08-13 16:46:44.181 INF] [IndustrialHMI] [LIN-PC] [24472] TestModulePresenter停止完成
[2025-08-13 16:46:44.181 DBG] [IndustrialHMI] [LIN-PC] [24472] 事件取消订阅完成
[2025-08-13 16:46:44.181 INF] [IndustrialHMI] [LIN-PC] [24472] 模块停止完成: 测试模块
[2025-08-13 16:46:44.181 DBG] [IndustrialHMI] [LIN-PC] [24472] 处理系统事件: ModuleStopped
[2025-08-13 16:46:44.182 INF] [IndustrialHMI] [LIN-PC] [24472] 开始释放模块资源: 测试模块
[2025-08-13 16:46:44.182 INF] [IndustrialHMI] [LIN-PC] [24472] 释放TestModulePresenter资源
[2025-08-13 16:46:44.182 INF] [IndustrialHMI] [LIN-PC] [24472] TestModulePresenter资源释放完成
[2025-08-13 16:46:44.190 INF] [IndustrialHMI] [LIN-PC] [24472] 模块资源释放完成: 测试模块
[2025-08-13 16:46:44.190 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 16:46:44.193 ERR] [IndustrialHMI] [LIN-PC] [24472] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.set_SelectedText(String value)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 16:46:44.197 INF] [IndustrialHMI] [LIN-PC] [24472] 模块卸载成功: 测试模块
[2025-08-13 16:46:44.197 INF] [IndustrialHMI] [LIN-PC] [24472] 所有模块卸载完成
[2025-08-13 16:46:44.197 INF] [IndustrialHMI] [LIN-PC] [24472] 应用程序关闭流程完成
[2025-08-13 16:46:44.197 INF] [IndustrialHMI] [LIN-PC] [24472] 主窗体已关闭，资源清理完成
[2025-08-13 16:46:44.203 INF] [IndustrialHMI] [LIN-PC] [24472] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:44.203 INF] [IndustrialHMI] [LIN-PC] [24472] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:44.203 INF] [IndustrialHMI] [LIN-PC] [24472] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:44.204 INF] [IndustrialHMI] [LIN-PC] [24472] 开始释放应用程序资源
[2025-08-13 16:46:44.204 INF] [IndustrialHMI] [LIN-PC] [24472] 开始卸载所有模块
[2025-08-13 16:46:44.204 INF] [IndustrialHMI] [LIN-PC] [24472] 所有模块卸载完成
[2025-08-13 16:46:44.204 INF] [IndustrialHMI] [LIN-PC] [24472] 应用程序资源释放完成
[2025-08-13 16:46:44.204 INF] [IndustrialHMI] [LIN-PC] [24472] === 应用程序正常退出 ===
﻿[2025-08-13 16:46:45.309 INF] [IndustrialHMI] [LIN-PC] [31472] === 应用程序启动 ===
[2025-08-13 16:46:45.331 INF] [IndustrialHMI] [LIN-PC] [31472] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:45.331 INF] [IndustrialHMI] [LIN-PC] [31472] 启动参数: 
[2025-08-13 16:46:45.332 INF] [IndustrialHMI] [LIN-PC] [31472] 开始初始化应用程序
[2025-08-13 16:46:45.332 INF] [IndustrialHMI] [LIN-PC] [31472] 步骤1: 创建服务容器
[2025-08-13 16:46:45.336 INF] [IndustrialHMI] [LIN-PC] [31472] 开始创建DryIoc容器
[2025-08-13 16:46:45.348 DBG] [IndustrialHMI] [LIN-PC] [31472] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:45.351 DBG] [IndustrialHMI] [LIN-PC] [31472] 注册自定义日志记录器为单例
[2025-08-13 16:46:45.351 DBG] [IndustrialHMI] [LIN-PC] [31472] 注册EventAggregator为单例
[2025-08-13 16:46:45.356 DBG] [IndustrialHMI] [LIN-PC] [31472] 注册ConfigurationService为单例
[2025-08-13 16:46:45.366 DBG] [IndustrialHMI] [LIN-PC] [31472] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:45.396 DBG] [IndustrialHMI] [LIN-PC] [31472] 注册MainForm为单例
[2025-08-13 16:46:45.396 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始验证DryIoc容器配置
[2025-08-13 16:46:45.397 DBG] [IndustrialHMI] [LIN-PC] [31472] DryIoc容器配置验证通过
[2025-08-13 16:46:45.397 INF] [IndustrialHMI] [LIN-PC] [31472] DryIoc容器创建和配置完成
[2025-08-13 16:46:45.397 INF] [IndustrialHMI] [LIN-PC] [31472] 步骤2: 创建主窗体
[2025-08-13 16:46:45.397 INF] [IndustrialHMI] [LIN-PC] [31472] 步骤3: 创建模块加载器
[2025-08-13 16:46:45.397 INF] [IndustrialHMI] [LIN-PC] [31472] 步骤4: 加载模块
[2025-08-13 16:46:45.397 INF] [IndustrialHMI] [LIN-PC] [31472] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:45.398 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:45.405 DBG] [IndustrialHMI] [LIN-PC] [31472] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.406 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.407 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:45.407 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:45.407 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:45.407 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:45.408 INF] [IndustrialHMI] [LIN-PC] [31472] 开始初始化报警管理模块
[2025-08-13 16:46:45.409 DBG] [IndustrialHMI] [LIN-PC] [31472] 报警服务创建完成
[2025-08-13 16:46:45.416 DBG] [IndustrialHMI] [LIN-PC] [31472] 报警视图创建完成
[2025-08-13 16:46:45.418 DBG] [IndustrialHMI] [LIN-PC] [31472] AlarmModel 初始化完成
[2025-08-13 16:46:45.418 DBG] [IndustrialHMI] [LIN-PC] [31472] 报警模型创建完成
[2025-08-13 16:46:45.418 DBG] [IndustrialHMI] [LIN-PC] [31472] AlarmPresenter 初始化完成
[2025-08-13 16:46:45.418 DBG] [IndustrialHMI] [LIN-PC] [31472] 报警表示器创建完成
[2025-08-13 16:46:45.418 INF] [IndustrialHMI] [LIN-PC] [31472] MVP组件创建完成
[2025-08-13 16:46:45.419 DBG] [IndustrialHMI] [LIN-PC] [31472] 系统事件订阅完成
[2025-08-13 16:46:45.419 INF] [IndustrialHMI] [LIN-PC] [31472] 报警管理模块初始化完成
[2025-08-13 16:46:45.419 INF] [IndustrialHMI] [LIN-PC] [31472] 启动报警管理模块
[2025-08-13 16:46:45.419 INF] [IndustrialHMI] [LIN-PC] [31472] 启动报警监控
[2025-08-13 16:46:45.419 INF] [IndustrialHMI] [LIN-PC] [31472] 开始报警监控
[2025-08-13 16:46:45.419 INF] [IndustrialHMI] [LIN-PC] [31472] 报警管理模块启动完成
[2025-08-13 16:46:45.420 INF] [IndustrialHMI] [LIN-PC] [31472] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:45.421 DBG] [IndustrialHMI] [LIN-PC] [31472] 模块已加载: 报警管理
[2025-08-13 16:46:45.421 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:45.423 DBG] [IndustrialHMI] [LIN-PC] [31472] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:45.424 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:45.425 INF] [IndustrialHMI] [LIN-PC] [31472] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:45.426 INF] [IndustrialHMI] [LIN-PC] [31472] 初始化了 8 个测试用例
[2025-08-13 16:46:45.426 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModel 初始化完成
[2025-08-13 16:46:45.431 DBG] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestView 初始化完成
[2025-08-13 16:46:45.433 DBG] [IndustrialHMI] [LIN-PC] [31472] 视图数据初始化完成
[2025-08-13 16:46:45.433 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:45.433 DBG] [IndustrialHMI] [LIN-PC] [31472] MVP组件创建完成
[2025-08-13 16:46:45.433 DBG] [IndustrialHMI] [LIN-PC] [31472] 系统事件订阅完成
[2025-08-13 16:46:45.433 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModule 初始化完成
[2025-08-13 16:46:45.433 INF] [IndustrialHMI] [LIN-PC] [31472] 启动 CommunicationTestModule
[2025-08-13 16:46:45.433 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModule 启动完成
[2025-08-13 16:46:45.433 INF] [IndustrialHMI] [LIN-PC] [31472] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:45.433 DBG] [IndustrialHMI] [LIN-PC] [31472] 模块已加载: 通信测试
[2025-08-13 16:46:45.434 DBG] [IndustrialHMI] [LIN-PC] [31472] 收到模块加载事件: 通信测试
[2025-08-13 16:46:45.434 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:45.435 DBG] [IndustrialHMI] [LIN-PC] [31472] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:45.435 DBG] [IndustrialHMI] [LIN-PC] [31472] 发现模块类型: 
[2025-08-13 16:46:45.435 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.435 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.435 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.436 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.436 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.436 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.436 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:45.436 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:45.437 DBG] [IndustrialHMI] [LIN-PC] [31472] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:45.438 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:45.438 INF] [IndustrialHMI] [LIN-PC] [31472] 开始初始化设备监控模块
[2025-08-13 16:46:45.439 DBG] [IndustrialHMI] [LIN-PC] [31472] 设备服务创建完成
[2025-08-13 16:46:45.441 DBG] [IndustrialHMI] [LIN-PC] [31472] 设备视图创建完成
[2025-08-13 16:46:45.441 DBG] [IndustrialHMI] [LIN-PC] [31472] DeviceModel 初始化完成
[2025-08-13 16:46:45.441 DBG] [IndustrialHMI] [LIN-PC] [31472] 设备模型创建完成
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] DevicePresenter 初始化完成
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] 设备表示器创建完成
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] MVP组件创建完成
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] 系统事件订阅完成
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 设备监控模块初始化完成
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 启动设备监控模块
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 用户请求开始设备监控
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 开始设备监控
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 设备监控已启动
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 设备监控模块启动完成
[2025-08-13 16:46:45.442 INF] [IndustrialHMI] [LIN-PC] [31472] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] 模块已加载: 设备监控
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] 收到模块加载事件: 设备监控
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] 模块已加载: 设备监控
[2025-08-13 16:46:45.442 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:45.444 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:45.445 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:45.445 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:45.445 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:45.445 INF] [IndustrialHMI] [LIN-PC] [31472] 开始初始化测试框架模块
[2025-08-13 16:46:45.445 DBG] [IndustrialHMI] [LIN-PC] [31472] ConfigurationService未注入（可选）
[2025-08-13 16:46:45.954 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:45.954 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 7
[2025-08-13 16:46:45.954 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:45
[2025-08-13 16:46:46.090 DBG] [IndustrialHMI] [LIN-PC] [31472] 初始化TestFrameworkPresenter
[2025-08-13 16:46:46.109 DBG] [IndustrialHMI] [LIN-PC] [31472] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:46.109 DBG] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块事件订阅完成
[2025-08-13 16:46:46.109 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块初始化完成
[2025-08-13 16:46:46.110 INF] [IndustrialHMI] [LIN-PC] [31472] 启动测试框架模块
[2025-08-13 16:46:46.110 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 模型已启动
[2025-08-13 16:46:46.110 DBG] [IndustrialHMI] [LIN-PC] [31472] 加载TestFramework数据
[2025-08-13 16:46:46.111 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.111 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 数据加载完成
[2025-08-13 16:46:46.111 DBG] [IndustrialHMI] [LIN-PC] [31472] TestFramework数据加载完成
[2025-08-13 16:46:46.111 INF] [IndustrialHMI] [LIN-PC] [31472] 初始化集成测试套件
[2025-08-13 16:46:46.111 INF] [IndustrialHMI] [LIN-PC] [31472] 集成测试套件初始化完成
[2025-08-13 16:46:46.112 INF] [IndustrialHMI] [LIN-PC] [31472] 初始化性能测试套件
[2025-08-13 16:46:46.233 INF] [IndustrialHMI] [LIN-PC] [31472] 性能测试套件初始化完成
[2025-08-13 16:46:46.233 INF] [IndustrialHMI] [LIN-PC] [31472] 初始化内存泄漏测试套件
[2025-08-13 16:46:46.233 INF] [IndustrialHMI] [LIN-PC] [31472] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:46.233 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块启动完成
[2025-08-13 16:46:46.233 INF] [IndustrialHMI] [LIN-PC] [31472] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:46.234 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:46.234 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.235 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:46.235 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:46.236 DBG] [IndustrialHMI] [LIN-PC] [31472] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 发现模块类型: TestModuleMain
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 开始加载模块: TestModuleMain
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:46.237 INF] [IndustrialHMI] [LIN-PC] [31472] 开始初始化模块: 测试模块
[2025-08-13 16:46:46.237 WRN] [IndustrialHMI] [LIN-PC] [31472] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 依赖注入验证通过
[2025-08-13 16:46:46.237 DBG] [IndustrialHMI] [LIN-PC] [31472] 创建TestModuleModel成功
[2025-08-13 16:46:46.240 DBG] [IndustrialHMI] [LIN-PC] [31472] 创建TestModuleView成功
[2025-08-13 16:46:46.240 DBG] [IndustrialHMI] [LIN-PC] [31472] TestModulePresenter创建完成
[2025-08-13 16:46:46.240 DBG] [IndustrialHMI] [LIN-PC] [31472] 创建TestModulePresenter成功
[2025-08-13 16:46:46.240 DBG] [IndustrialHMI] [LIN-PC] [31472] 事件订阅完成
[2025-08-13 16:46:46.240 INF] [IndustrialHMI] [LIN-PC] [31472] 模块初始化完成: 测试模块
[2025-08-13 16:46:46.241 INF] [IndustrialHMI] [LIN-PC] [31472] 开始启动模块: 测试模块
[2025-08-13 16:46:46.241 INF] [IndustrialHMI] [LIN-PC] [31472] 启动TestModulePresenter
[2025-08-13 16:46:46.246 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 模型启动完成
[2025-08-13 16:46:46.247 DBG] [IndustrialHMI] [LIN-PC] [31472] 系统事件订阅完成
[2025-08-13 16:46:46.248 INF] [IndustrialHMI] [LIN-PC] [31472] TestModulePresenter启动完成
[2025-08-13 16:46:46.248 INF] [IndustrialHMI] [LIN-PC] [31472] 模块启动完成: 测试模块
[2025-08-13 16:46:46.249 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理系统事件: ModuleStarted
[2025-08-13 16:46:46.250 INF] [IndustrialHMI] [LIN-PC] [31472] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:46.250 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:46.250 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.251 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:46.252 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理模块加载事件: 测试模块
[2025-08-13 16:46:46.252 INF] [IndustrialHMI] [LIN-PC] [31472] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:46.252 INF] [IndustrialHMI] [LIN-PC] [31472] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:46.253 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:46.253 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:46.253 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:46.254 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:46.254 DBG] [IndustrialHMI] [LIN-PC] [31472] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:46.254 INF] [IndustrialHMI] [LIN-PC] [31472] 步骤5: 初始化主窗体
[2025-08-13 16:46:46.255 DBG] [IndustrialHMI] [LIN-PC] [31472] 主窗体初始化完成
[2025-08-13 16:46:46.255 INF] [IndustrialHMI] [LIN-PC] [31472] 应用程序初始化完成
[2025-08-13 16:46:46.255 INF] [IndustrialHMI] [LIN-PC] [31472] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:46.255 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到系统启动事件
[2025-08-13 16:46:46.255 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.256 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:46.256 INF] [IndustrialHMI] [LIN-PC] [31472] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:46.257 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:46.333 DBG] [IndustrialHMI] [LIN-PC] [31472] 主窗体事件订阅完成
[2025-08-13 16:46:46.334 INF] [IndustrialHMI] [LIN-PC] [31472] 用户请求关闭应用程序
[2025-08-13 16:46:46.335 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:46.336 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.336 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 16:46:46.336 INF] [IndustrialHMI] [LIN-PC] [31472] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:46.337 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到系统关闭事件
[2025-08-13 16:46:46.360 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据变化事件处理完成
[2025-08-13 16:46:46.361 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据变化事件处理完成
[2025-08-13 16:46:46.362 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 系统关闭前清理完成
[2025-08-13 16:46:46.362 INF] [IndustrialHMI] [LIN-PC] [31472] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载所有模块
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载模块: 报警管理
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 停止报警管理模块
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 停止报警监控
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 停止报警监控
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 报警管理模块停止完成
[2025-08-13 16:46:46.363 INF] [IndustrialHMI] [LIN-PC] [31472] 开始释放报警管理模块资源
[2025-08-13 16:46:46.364 INF] [IndustrialHMI] [LIN-PC] [31472] 停止报警监控
[2025-08-13 16:46:46.364 INF] [IndustrialHMI] [LIN-PC] [31472] 停止报警监控
[2025-08-13 16:46:46.365 DBG] [IndustrialHMI] [LIN-PC] [31472] AlarmPresenter 资源释放完成
[2025-08-13 16:46:46.365 INF] [IndustrialHMI] [LIN-PC] [31472] 停止报警监控
[2025-08-13 16:46:46.365 DBG] [IndustrialHMI] [LIN-PC] [31472] AlarmModel 资源释放完成
[2025-08-13 16:46:46.372 INF] [IndustrialHMI] [LIN-PC] [31472] 报警管理模块资源释放完成
[2025-08-13 16:46:46.372 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 16:46:46.373 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.373 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:46.374 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理模块卸载事件: 报警管理
[2025-08-13 16:46:46.374 INF] [IndustrialHMI] [LIN-PC] [31472] 模块卸载成功: 报警管理
[2025-08-13 16:46:46.374 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载模块: 通信测试
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] 停止 CommunicationTestModule
[2025-08-13 16:46:46.375 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据变化: EventMonitoring
[2025-08-13 16:46:46.375 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] 测试已停止
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModule 停止完成
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] 停止 CommunicationTestModule
[2025-08-13 16:46:46.375 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据变化: EventMonitoring
[2025-08-13 16:46:46.375 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] 测试已停止
[2025-08-13 16:46:46.375 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModule 停止完成
[2025-08-13 16:46:46.375 DBG] [IndustrialHMI] [LIN-PC] [31472] 系统事件订阅已取消
[2025-08-13 16:46:46.376 DBG] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:46:46.376 DBG] [IndustrialHMI] [LIN-PC] [31472] EventMonitor 资源释放完成
[2025-08-13 16:46:46.376 INF] [IndustrialHMI] [LIN-PC] [31472] 测试已停止
[2025-08-13 16:46:46.376 DBG] [IndustrialHMI] [LIN-PC] [31472] TestCaseManager 资源释放完成
[2025-08-13 16:46:46.377 DBG] [IndustrialHMI] [LIN-PC] [31472] PerformanceMonitor 资源释放完成
[2025-08-13 16:46:46.377 DBG] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModel 资源释放完成
[2025-08-13 16:46:46.377 INF] [IndustrialHMI] [LIN-PC] [31472] CommunicationTestModule 资源释放完成
[2025-08-13 16:46:46.377 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 16:46:46.377 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.377 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:46.379 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理模块卸载事件: 通信测试
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 模块卸载成功: 通信测试
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载模块: 设备监控
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 停止设备监控模块
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 用户请求停止设备监控
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 停止设备监控
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 设备监控已停止
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 设备监控模块停止完成
[2025-08-13 16:46:46.379 INF] [IndustrialHMI] [LIN-PC] [31472] 开始释放设备监控模块资源
[2025-08-13 16:46:46.380 DBG] [IndustrialHMI] [LIN-PC] [31472] DevicePresenter 资源释放完成
[2025-08-13 16:46:46.380 INF] [IndustrialHMI] [LIN-PC] [31472] 停止设备监控
[2025-08-13 16:46:46.380 DBG] [IndustrialHMI] [LIN-PC] [31472] DeviceModel 资源释放完成
[2025-08-13 16:46:46.380 INF] [IndustrialHMI] [LIN-PC] [31472] 设备监控模块资源释放完成
[2025-08-13 16:46:46.380 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 16:46:46.381 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型数据已更新，视图已刷新
[2025-08-13 16:46:46.381 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:46.382 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理模块卸载事件: 设备监控
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 模块卸载成功: 设备监控
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载模块: 测试框架模块
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 停止测试框架模块
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 停止内存泄漏测试套件
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 内存泄漏测试套件已停止
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 停止性能测试套件
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 性能测试套件已停止
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 停止集成测试套件
[2025-08-13 16:46:46.382 INF] [IndustrialHMI] [LIN-PC] [31472] 集成测试套件已停止
[2025-08-13 16:46:46.383 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 模型已停止
[2025-08-13 16:46:46.383 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块停止完成
[2025-08-13 16:46:46.383 INF] [IndustrialHMI] [LIN-PC] [31472] 开始释放测试框架模块资源
[2025-08-13 16:46:46.383 INF] [IndustrialHMI] [LIN-PC] [31472] 停止内存泄漏测试套件
[2025-08-13 16:46:46.383 INF] [IndustrialHMI] [LIN-PC] [31472] 内存泄漏测试套件已停止
[2025-08-13 16:46:46.383 DBG] [IndustrialHMI] [LIN-PC] [31472] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:46:46.383 INF] [IndustrialHMI] [LIN-PC] [31472] 停止性能测试套件
[2025-08-13 16:46:46.383 INF] [IndustrialHMI] [LIN-PC] [31472] 性能测试套件已停止
[2025-08-13 16:46:46.384 DBG] [IndustrialHMI] [LIN-PC] [31472] PerformanceTestSuite资源释放完成
[2025-08-13 16:46:46.384 INF] [IndustrialHMI] [LIN-PC] [31472] 停止集成测试套件
[2025-08-13 16:46:46.384 INF] [IndustrialHMI] [LIN-PC] [31472] 集成测试套件已停止
[2025-08-13 16:46:46.384 DBG] [IndustrialHMI] [LIN-PC] [31472] IntegrationTestSuite资源释放完成
[2025-08-13 16:46:46.384 DBG] [IndustrialHMI] [LIN-PC] [31472] TestFrameworkPresenter资源释放完成
[2025-08-13 16:46:46.385 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块资源释放完成
[2025-08-13 16:46:46.385 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 16:46:46.386 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理模块卸载事件: 测试框架模块
[2025-08-13 16:46:46.386 INF] [IndustrialHMI] [LIN-PC] [31472] 模块卸载成功: 测试框架模块
[2025-08-13 16:46:46.386 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载模块: 测试模块
[2025-08-13 16:46:46.386 INF] [IndustrialHMI] [LIN-PC] [31472] 开始停止模块: 测试模块
[2025-08-13 16:46:46.386 INF] [IndustrialHMI] [LIN-PC] [31472] 停止TestModulePresenter
[2025-08-13 16:46:46.386 DBG] [IndustrialHMI] [LIN-PC] [31472] 系统事件取消订阅完成
[2025-08-13 16:46:46.387 DBG] [IndustrialHMI] [LIN-PC] [31472] 模型状态变化: 模型停止完成
[2025-08-13 16:46:46.387 INF] [IndustrialHMI] [LIN-PC] [31472] TestModulePresenter停止完成
[2025-08-13 16:46:46.387 DBG] [IndustrialHMI] [LIN-PC] [31472] 事件取消订阅完成
[2025-08-13 16:46:46.387 INF] [IndustrialHMI] [LIN-PC] [31472] 模块停止完成: 测试模块
[2025-08-13 16:46:46.388 DBG] [IndustrialHMI] [LIN-PC] [31472] 处理系统事件: ModuleStopped
[2025-08-13 16:46:46.388 INF] [IndustrialHMI] [LIN-PC] [31472] 开始释放模块资源: 测试模块
[2025-08-13 16:46:46.388 INF] [IndustrialHMI] [LIN-PC] [31472] 释放TestModulePresenter资源
[2025-08-13 16:46:46.388 INF] [IndustrialHMI] [LIN-PC] [31472] TestModulePresenter资源释放完成
[2025-08-13 16:46:46.395 INF] [IndustrialHMI] [LIN-PC] [31472] 模块资源释放完成: 测试模块
[2025-08-13 16:46:46.395 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 16:46:46.399 ERR] [IndustrialHMI] [LIN-PC] [31472] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.set_SelectedText(String value)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 16:46:46.404 INF] [IndustrialHMI] [LIN-PC] [31472] 模块卸载成功: 测试模块
[2025-08-13 16:46:46.404 INF] [IndustrialHMI] [LIN-PC] [31472] 所有模块卸载完成
[2025-08-13 16:46:46.404 INF] [IndustrialHMI] [LIN-PC] [31472] 应用程序关闭流程完成
[2025-08-13 16:46:46.404 INF] [IndustrialHMI] [LIN-PC] [31472] 主窗体已关闭，资源清理完成
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 开始释放应用程序资源
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 开始卸载所有模块
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 所有模块卸载完成
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] 应用程序资源释放完成
[2025-08-13 16:46:46.413 INF] [IndustrialHMI] [LIN-PC] [31472] === 应用程序正常退出 ===
﻿[2025-08-13 16:46:47.513 INF] [IndustrialHMI] [LIN-PC] [29776] === 应用程序启动 ===
[2025-08-13 16:46:47.537 INF] [IndustrialHMI] [LIN-PC] [29776] 应用程序版本: 1.0.0.0
[2025-08-13 16:46:47.537 INF] [IndustrialHMI] [LIN-PC] [29776] 启动参数: 
[2025-08-13 16:46:47.537 INF] [IndustrialHMI] [LIN-PC] [29776] 开始初始化应用程序
[2025-08-13 16:46:47.537 INF] [IndustrialHMI] [LIN-PC] [29776] 步骤1: 创建服务容器
[2025-08-13 16:46:47.541 INF] [IndustrialHMI] [LIN-PC] [29776] 开始创建DryIoc容器
[2025-08-13 16:46:47.552 DBG] [IndustrialHMI] [LIN-PC] [29776] DryIoc容器创建成功，开始注册服务
[2025-08-13 16:46:47.555 DBG] [IndustrialHMI] [LIN-PC] [29776] 注册自定义日志记录器为单例
[2025-08-13 16:46:47.555 DBG] [IndustrialHMI] [LIN-PC] [29776] 注册EventAggregator为单例
[2025-08-13 16:46:47.561 DBG] [IndustrialHMI] [LIN-PC] [29776] 注册ConfigurationService为单例
[2025-08-13 16:46:47.570 DBG] [IndustrialHMI] [LIN-PC] [29776] 注册ModuleLoader为单例（支持DryIoc依赖注入）
[2025-08-13 16:46:47.599 DBG] [IndustrialHMI] [LIN-PC] [29776] 注册MainForm为单例
[2025-08-13 16:46:47.599 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始验证DryIoc容器配置
[2025-08-13 16:46:47.599 DBG] [IndustrialHMI] [LIN-PC] [29776] DryIoc容器配置验证通过
[2025-08-13 16:46:47.599 INF] [IndustrialHMI] [LIN-PC] [29776] DryIoc容器创建和配置完成
[2025-08-13 16:46:47.599 INF] [IndustrialHMI] [LIN-PC] [29776] 步骤2: 创建主窗体
[2025-08-13 16:46:47.599 INF] [IndustrialHMI] [LIN-PC] [29776] 步骤3: 创建模块加载器
[2025-08-13 16:46:47.599 INF] [IndustrialHMI] [LIN-PC] [29776] 步骤4: 加载模块
[2025-08-13 16:46:47.600 INF] [IndustrialHMI] [LIN-PC] [29776] 开始从目录加载模块: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules
[2025-08-13 16:46:47.601 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\AlarmModule.dll
[2025-08-13 16:46:47.608 DBG] [IndustrialHMI] [LIN-PC] [29776] 程序集加载成功: AlarmModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 发现模块类型: AlarmModuleMain
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 MockAlarmService 实现的接口: Contracts.Services.IAlarmService
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmModel 实现的接口: System.IDisposable
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnAcknowledgeAlarmRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnAcknowledgeAllAlarmsRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnClearAcknowledgedAlarmsRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnClearAlarmRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.609 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnRefreshRequested>d__11 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.610 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载模块: AlarmModuleMain
[2025-08-13 16:46:47.610 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 AlarmModuleMain 注入EventAggregator
[2025-08-13 16:46:47.610 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 AlarmModuleMain 注入Logger
[2025-08-13 16:46:47.611 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 AlarmModuleMain 完成依赖注入
[2025-08-13 16:46:47.611 INF] [IndustrialHMI] [LIN-PC] [29776] 开始初始化报警管理模块
[2025-08-13 16:46:47.612 DBG] [IndustrialHMI] [LIN-PC] [29776] 报警服务创建完成
[2025-08-13 16:46:47.620 DBG] [IndustrialHMI] [LIN-PC] [29776] 报警视图创建完成
[2025-08-13 16:46:47.621 DBG] [IndustrialHMI] [LIN-PC] [29776] AlarmModel 初始化完成
[2025-08-13 16:46:47.621 DBG] [IndustrialHMI] [LIN-PC] [29776] 报警模型创建完成
[2025-08-13 16:46:47.623 DBG] [IndustrialHMI] [LIN-PC] [29776] AlarmPresenter 初始化完成
[2025-08-13 16:46:47.623 DBG] [IndustrialHMI] [LIN-PC] [29776] 报警表示器创建完成
[2025-08-13 16:46:47.623 INF] [IndustrialHMI] [LIN-PC] [29776] MVP组件创建完成
[2025-08-13 16:46:47.623 DBG] [IndustrialHMI] [LIN-PC] [29776] 系统事件订阅完成
[2025-08-13 16:46:47.623 INF] [IndustrialHMI] [LIN-PC] [29776] 报警管理模块初始化完成
[2025-08-13 16:46:47.623 INF] [IndustrialHMI] [LIN-PC] [29776] 启动报警管理模块
[2025-08-13 16:46:47.624 INF] [IndustrialHMI] [LIN-PC] [29776] 启动报警监控
[2025-08-13 16:46:47.624 INF] [IndustrialHMI] [LIN-PC] [29776] 开始报警监控
[2025-08-13 16:46:47.624 INF] [IndustrialHMI] [LIN-PC] [29776] 报警管理模块启动完成
[2025-08-13 16:46:47.624 INF] [IndustrialHMI] [LIN-PC] [29776] 模块加载成功: 报警管理 - 实时接收和管理系统报警，提供报警确认、清除和历史记录功能
[2025-08-13 16:46:47.625 DBG] [IndustrialHMI] [LIN-PC] [29776] 模块已加载: 报警管理
[2025-08-13 16:46:47.625 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\CommunicationTestModule.dll
[2025-08-13 16:46:47.627 DBG] [IndustrialHMI] [LIN-PC] [29776] 程序集加载成功: CommunicationTestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 发现模块类型: CommunicationTestModuleMain
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 CommunicationTestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 EventMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestCaseManager 实现的接口: System.IDisposable
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 PerformanceMonitor 实现的接口: System.IDisposable
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 CommunicationTestModel 实现的接口: System.IDisposable
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 CommunicationTestPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 CommunicationTestView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunAllTestsAsync>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunSingleTestAsync>d__21 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunTestsAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunTestsByCategoryAsync>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestAlarmEvent>d__25 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestConcurrentEvents>d__28 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestDeviceConnectionEvent>d__24 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestDeviceDataUpdateEvent>d__23 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestDeviceOfflineAlarm>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestEventStress>d__29 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestExceptionIsolation>d__30 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <TestTemperatureAlarm>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunPerformanceTestAsync>d__19 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunAllTests>d__26 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <RunTestsByCategory>d__27 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnEventMonitorActionRequested>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnPerformanceMonitorActionRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnResultActionRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnTestExecutionActionRequested>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <<TestConcurrentEvents>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载模块: CommunicationTestModuleMain
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 CommunicationTestModuleMain 注入EventAggregator
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 CommunicationTestModuleMain 注入Logger
[2025-08-13 16:46:47.628 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 CommunicationTestModuleMain 完成依赖注入
[2025-08-13 16:46:47.628 INF] [IndustrialHMI] [LIN-PC] [29776] 开始初始化 CommunicationTestModule
[2025-08-13 16:46:47.629 INF] [IndustrialHMI] [LIN-PC] [29776] 初始化了 8 个测试用例
[2025-08-13 16:46:47.630 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModel 初始化完成
[2025-08-13 16:46:47.635 DBG] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestView 初始化完成
[2025-08-13 16:46:47.637 DBG] [IndustrialHMI] [LIN-PC] [29776] 视图数据初始化完成
[2025-08-13 16:46:47.638 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestPresenter 初始化完成
[2025-08-13 16:46:47.638 DBG] [IndustrialHMI] [LIN-PC] [29776] MVP组件创建完成
[2025-08-13 16:46:47.638 DBG] [IndustrialHMI] [LIN-PC] [29776] 系统事件订阅完成
[2025-08-13 16:46:47.638 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModule 初始化完成
[2025-08-13 16:46:47.638 INF] [IndustrialHMI] [LIN-PC] [29776] 启动 CommunicationTestModule
[2025-08-13 16:46:47.638 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModule 启动完成
[2025-08-13 16:46:47.638 INF] [IndustrialHMI] [LIN-PC] [29776] 模块加载成功: 通信测试 - 模块间通信验证模块，测试事件通信的稳定性和性能，提供完整的测试报告
[2025-08-13 16:46:47.638 DBG] [IndustrialHMI] [LIN-PC] [29776] 模块已加载: 通信测试
[2025-08-13 16:46:47.638 DBG] [IndustrialHMI] [LIN-PC] [29776] 收到模块加载事件: 通信测试
[2025-08-13 16:46:47.638 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\Contracts.dll
[2025-08-13 16:46:47.639 DBG] [IndustrialHMI] [LIN-PC] [29776] 程序集加载成功: Contracts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 发现模块类型: 
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmRuleType 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 ComparisonOperator 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 ThreadOption 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 ShutdownReason 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 DataQuality 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmLevel 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 AlarmStatus 实现的接口: System.IComparable, System.IFormattable, System.IConvertible
[2025-08-13 16:46:47.640 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\DeviceModule.dll
[2025-08-13 16:46:47.641 DBG] [IndustrialHMI] [LIN-PC] [29776] 程序集加载成功: DeviceModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 发现模块类型: DeviceModuleMain
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 DeviceModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 MockDeviceService 实现的接口: Contracts.Services.IDeviceService
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 DeviceModel 实现的接口: System.IDisposable
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 DeviceViewModel 实现的接口: System.ComponentModel.INotifyPropertyChanged
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 DevicePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 DeviceView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnConnectAllRequested>d__13 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnDeviceConnectRequested>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnDeviceDisconnectRequested>d__18 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnDisconnectAllRequested>d__14 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnRefreshRequested>d__12 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <<ConnectDevice>b__0>d 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载模块: DeviceModuleMain
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 DeviceModuleMain 注入EventAggregator
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 DeviceModuleMain 注入Logger
[2025-08-13 16:46:47.642 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 DeviceModuleMain 完成依赖注入
[2025-08-13 16:46:47.642 INF] [IndustrialHMI] [LIN-PC] [29776] 开始初始化设备监控模块
[2025-08-13 16:46:47.643 DBG] [IndustrialHMI] [LIN-PC] [29776] 设备服务创建完成
[2025-08-13 16:46:47.645 DBG] [IndustrialHMI] [LIN-PC] [29776] 设备视图创建完成
[2025-08-13 16:46:47.645 DBG] [IndustrialHMI] [LIN-PC] [29776] DeviceModel 初始化完成
[2025-08-13 16:46:47.645 DBG] [IndustrialHMI] [LIN-PC] [29776] 设备模型创建完成
[2025-08-13 16:46:47.646 DBG] [IndustrialHMI] [LIN-PC] [29776] DevicePresenter 初始化完成
[2025-08-13 16:46:47.646 DBG] [IndustrialHMI] [LIN-PC] [29776] 设备表示器创建完成
[2025-08-13 16:46:47.646 INF] [IndustrialHMI] [LIN-PC] [29776] MVP组件创建完成
[2025-08-13 16:46:47.646 DBG] [IndustrialHMI] [LIN-PC] [29776] 系统事件订阅完成
[2025-08-13 16:46:47.646 INF] [IndustrialHMI] [LIN-PC] [29776] 设备监控模块初始化完成
[2025-08-13 16:46:47.646 INF] [IndustrialHMI] [LIN-PC] [29776] 启动设备监控模块
[2025-08-13 16:46:47.646 INF] [IndustrialHMI] [LIN-PC] [29776] 用户请求开始设备监控
[2025-08-13 16:46:47.646 INF] [IndustrialHMI] [LIN-PC] [29776] 开始设备监控
[2025-08-13 16:46:47.647 INF] [IndustrialHMI] [LIN-PC] [29776] 设备监控已启动
[2025-08-13 16:46:47.647 INF] [IndustrialHMI] [LIN-PC] [29776] 设备监控模块启动完成
[2025-08-13 16:46:47.647 INF] [IndustrialHMI] [LIN-PC] [29776] 模块加载成功: 设备监控 - 实时监控设备连接状态和运行参数，提供设备管理和控制功能
[2025-08-13 16:46:47.647 DBG] [IndustrialHMI] [LIN-PC] [29776] 模块已加载: 设备监控
[2025-08-13 16:46:47.647 DBG] [IndustrialHMI] [LIN-PC] [29776] 收到模块加载事件: 设备监控
[2025-08-13 16:46:47.647 DBG] [IndustrialHMI] [LIN-PC] [29776] 模块已加载: 设备监控
[2025-08-13 16:46:47.647 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestFrameworkModule.dll
[2025-08-13 16:46:47.648 DBG] [IndustrialHMI] [LIN-PC] [29776] 程序集加载成功: TestFrameworkModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 发现模块类型: TestFrameworkModuleMain
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestFrameworkModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 IntegrationTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 PerformanceTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 MemoryLeakTestSuite 实现的接口: System.IDisposable
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestFrameworkModel 实现的接口: System.IDisposable
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestFrameworkPresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestFrameworkView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnRunIntegrationTestsClicked>d__15 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnRunMemoryLeakTestsClicked>d__17 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 <OnRunPerformanceTestsClicked>d__16 实现的接口: System.Runtime.CompilerServices.IAsyncStateMachine
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载模块: TestFrameworkModuleMain
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 TestFrameworkModuleMain 注入EventAggregator
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 TestFrameworkModuleMain 注入Logger
[2025-08-13 16:46:47.649 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 TestFrameworkModuleMain 完成依赖注入
[2025-08-13 16:46:47.650 INF] [IndustrialHMI] [LIN-PC] [29776] 开始初始化测试框架模块
[2025-08-13 16:46:47.650 DBG] [IndustrialHMI] [LIN-PC] [29776] ConfigurationService未注入（可选）
[2025-08-13 16:46:48.300 DBG] [IndustrialHMI] [LIN-PC] [29776] 初始化TestFrameworkPresenter
[2025-08-13 16:46:48.323 DBG] [IndustrialHMI] [LIN-PC] [29776] TestFrameworkPresenter初始化完成
[2025-08-13 16:46:48.323 DBG] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块事件订阅完成
[2025-08-13 16:46:48.324 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块初始化完成
[2025-08-13 16:46:48.324 INF] [IndustrialHMI] [LIN-PC] [29776] 启动测试框架模块
[2025-08-13 16:46:48.324 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 模型已启动
[2025-08-13 16:46:48.324 DBG] [IndustrialHMI] [LIN-PC] [29776] 加载TestFramework数据
[2025-08-13 16:46:48.325 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:48.325 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 数据加载完成
[2025-08-13 16:46:48.325 DBG] [IndustrialHMI] [LIN-PC] [29776] TestFramework数据加载完成
[2025-08-13 16:46:48.326 INF] [IndustrialHMI] [LIN-PC] [29776] 初始化集成测试套件
[2025-08-13 16:46:48.326 INF] [IndustrialHMI] [LIN-PC] [29776] 集成测试套件初始化完成
[2025-08-13 16:46:48.326 INF] [IndustrialHMI] [LIN-PC] [29776] 初始化性能测试套件
[2025-08-13 16:46:48.448 INF] [IndustrialHMI] [LIN-PC] [29776] 性能测试套件初始化完成
[2025-08-13 16:46:48.449 INF] [IndustrialHMI] [LIN-PC] [29776] 初始化内存泄漏测试套件
[2025-08-13 16:46:48.449 INF] [IndustrialHMI] [LIN-PC] [29776] 内存泄漏测试套件初始化完成
[2025-08-13 16:46:48.449 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块启动完成
[2025-08-13 16:46:48.450 INF] [IndustrialHMI] [LIN-PC] [29776] 模块加载成功: 测试框架模块 - 提供系统集成测试、性能测试和内存泄漏检测功能的测试框架模块
[2025-08-13 16:46:48.451 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块加载事件: 测试框架模块
[2025-08-13 16:46:48.453 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:48.453 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:48.453 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载程序集: F:\Project\C#_project\winform\winfoms\bin\Debug\Modules\TestModule.dll
[2025-08-13 16:46:48.455 DBG] [IndustrialHMI] [LIN-PC] [29776] 程序集加载成功: TestModule, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 发现模块类型: TestModuleMain
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestModuleMain 实现的接口: Contracts.IModule, System.IDisposable
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestModuleModel 实现的接口: System.IDisposable
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestModulePresenter 实现的接口: Contracts.IPresenter, System.IDisposable
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 类型 TestModuleView 实现的接口: System.ComponentModel.IComponent, System.IDisposable, System.Windows.Forms.UnsafeNativeMethods+IOleControl, System.Windows.Forms.UnsafeNativeMethods+IOleObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceObject, System.Windows.Forms.UnsafeNativeMethods+IOleInPlaceActiveObject, System.Windows.Forms.UnsafeNativeMethods+IOleWindow, System.Windows.Forms.UnsafeNativeMethods+IViewObject, System.Windows.Forms.UnsafeNativeMethods+IViewObject2, System.Windows.Forms.UnsafeNativeMethods+IPersist, System.Windows.Forms.UnsafeNativeMethods+IPersistStreamInit, System.Windows.Forms.UnsafeNativeMethods+IPersistPropertyBag, System.Windows.Forms.UnsafeNativeMethods+IPersistStorage, System.Windows.Forms.UnsafeNativeMethods+IQuickActivate, System.Windows.Forms.ISupportOleDropSource, System.Windows.Forms.IDropTarget, System.ComponentModel.ISynchronizeInvoke, System.Windows.Forms.IWin32Window, System.Windows.Forms.Layout.IArrangedElement, System.Windows.Forms.IBindableComponent, System.Windows.Forms.IKeyboardToolTip, System.Windows.Forms.IContainerControl, Contracts.IView
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 开始加载模块: TestModuleMain
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 TestModuleMain 注入EventAggregator
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 TestModuleMain 注入Logger
[2025-08-13 16:46:48.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 TestModuleMain 完成依赖注入
[2025-08-13 16:46:48.456 INF] [IndustrialHMI] [LIN-PC] [29776] 开始初始化模块: 测试模块
[2025-08-13 16:46:48.457 WRN] [IndustrialHMI] [LIN-PC] [29776] ConfigurationService未注入（可能容器中未注册）
[2025-08-13 16:46:48.457 DBG] [IndustrialHMI] [LIN-PC] [29776] 依赖注入验证通过
[2025-08-13 16:46:48.457 DBG] [IndustrialHMI] [LIN-PC] [29776] 创建TestModuleModel成功
[2025-08-13 16:46:48.459 DBG] [IndustrialHMI] [LIN-PC] [29776] 创建TestModuleView成功
[2025-08-13 16:46:48.459 DBG] [IndustrialHMI] [LIN-PC] [29776] TestModulePresenter创建完成
[2025-08-13 16:46:48.459 DBG] [IndustrialHMI] [LIN-PC] [29776] 创建TestModulePresenter成功
[2025-08-13 16:46:48.459 DBG] [IndustrialHMI] [LIN-PC] [29776] 事件订阅完成
[2025-08-13 16:46:48.459 INF] [IndustrialHMI] [LIN-PC] [29776] 模块初始化完成: 测试模块
[2025-08-13 16:46:48.460 INF] [IndustrialHMI] [LIN-PC] [29776] 开始启动模块: 测试模块
[2025-08-13 16:46:48.460 INF] [IndustrialHMI] [LIN-PC] [29776] 启动TestModulePresenter
[2025-08-13 16:46:48.467 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 模型启动完成
[2025-08-13 16:46:48.469 DBG] [IndustrialHMI] [LIN-PC] [29776] 系统事件订阅完成
[2025-08-13 16:46:48.469 INF] [IndustrialHMI] [LIN-PC] [29776] TestModulePresenter启动完成
[2025-08-13 16:46:48.469 INF] [IndustrialHMI] [LIN-PC] [29776] 模块启动完成: 测试模块
[2025-08-13 16:46:48.470 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理系统事件: ModuleStarted
[2025-08-13 16:46:48.470 INF] [IndustrialHMI] [LIN-PC] [29776] 模块加载成功: 测试模块 - 用于验证模块加载器功能的测试模块，包含完整的MVP架构
[2025-08-13 16:46:48.470 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块加载事件: 测试模块
[2025-08-13 16:46:48.471 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:48.471 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到模块事件: ModuleLoaded
[2025-08-13 16:46:48.472 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理模块加载事件: 测试模块
[2025-08-13 16:46:48.473 INF] [IndustrialHMI] [LIN-PC] [29776] 模块加载完成，共加载 5 个模块
[2025-08-13 16:46:48.473 INF] [IndustrialHMI] [LIN-PC] [29776] 从目录 F:\Project\C#_project\winform\winfoms\bin\Debug\Modules 加载了 5 个模块
[2025-08-13 16:46:48.474 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 报警管理 添加了UI标签页
[2025-08-13 16:46:48.474 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 通信测试 添加了UI标签页
[2025-08-13 16:46:48.474 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 设备监控 添加了UI标签页
[2025-08-13 16:46:48.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 测试框架模块 添加了UI标签页
[2025-08-13 16:46:48.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 为模块 测试模块 添加了UI标签页
[2025-08-13 16:46:48.475 INF] [IndustrialHMI] [LIN-PC] [29776] 步骤5: 初始化主窗体
[2025-08-13 16:46:48.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 主窗体初始化完成
[2025-08-13 16:46:48.476 INF] [IndustrialHMI] [LIN-PC] [29776] 应用程序初始化完成
[2025-08-13 16:46:48.476 INF] [IndustrialHMI] [LIN-PC] [29776] 应用程序初始化成功，启动主窗体
[2025-08-13 16:46:48.476 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到系统启动事件
[2025-08-13 16:46:48.476 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:48.477 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到系统事件: SystemStartup
[2025-08-13 16:46:48.477 INF] [IndustrialHMI] [LIN-PC] [29776] 模块 测试模块 收到系统启动事件
[2025-08-13 16:46:48.478 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到系统启动事件
[2025-08-13 16:46:48.553 DBG] [IndustrialHMI] [LIN-PC] [29776] 主窗体事件订阅完成
[2025-08-13 16:46:48.562 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据变化事件处理完成
[2025-08-13 16:46:48.563 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 数据更新: +1, 当前值: 1
[2025-08-13 16:46:48.564 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 定时状态更新 - 16:46:48
[2025-08-13 16:46:49.455 INF] [IndustrialHMI] [LIN-PC] [29776] 用户请求关闭应用程序
[2025-08-13 16:46:49.455 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:49.456 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:49.457 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到系统事件: SystemShutdown
[2025-08-13 16:46:49.457 INF] [IndustrialHMI] [LIN-PC] [29776] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:49.458 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到系统关闭事件
[2025-08-13 16:46:49.459 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据变化事件处理完成
[2025-08-13 16:46:49.461 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 系统关闭前清理完成
[2025-08-13 16:46:49.461 INF] [IndustrialHMI] [LIN-PC] [29776] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:49.462 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载所有模块
[2025-08-13 16:46:49.462 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载模块: 报警管理
[2025-08-13 16:46:49.462 INF] [IndustrialHMI] [LIN-PC] [29776] 停止报警管理模块
[2025-08-13 16:46:49.462 INF] [IndustrialHMI] [LIN-PC] [29776] 停止报警监控
[2025-08-13 16:46:49.462 INF] [IndustrialHMI] [LIN-PC] [29776] 停止报警监控
[2025-08-13 16:46:49.462 INF] [IndustrialHMI] [LIN-PC] [29776] 报警管理模块停止完成
[2025-08-13 16:46:49.463 INF] [IndustrialHMI] [LIN-PC] [29776] 开始释放报警管理模块资源
[2025-08-13 16:46:49.463 INF] [IndustrialHMI] [LIN-PC] [29776] 停止报警监控
[2025-08-13 16:46:49.463 INF] [IndustrialHMI] [LIN-PC] [29776] 停止报警监控
[2025-08-13 16:46:49.465 DBG] [IndustrialHMI] [LIN-PC] [29776] AlarmPresenter 资源释放完成
[2025-08-13 16:46:49.466 INF] [IndustrialHMI] [LIN-PC] [29776] 停止报警监控
[2025-08-13 16:46:49.466 DBG] [IndustrialHMI] [LIN-PC] [29776] AlarmModel 资源释放完成
[2025-08-13 16:46:49.472 INF] [IndustrialHMI] [LIN-PC] [29776] 报警管理模块资源释放完成
[2025-08-13 16:46:49.472 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块卸载事件: 报警管理
[2025-08-13 16:46:49.473 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:49.473 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:49.474 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理模块卸载事件: 报警管理
[2025-08-13 16:46:49.474 INF] [IndustrialHMI] [LIN-PC] [29776] 模块卸载成功: 报警管理
[2025-08-13 16:46:49.474 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载模块: 通信测试
[2025-08-13 16:46:49.474 INF] [IndustrialHMI] [LIN-PC] [29776] 停止 CommunicationTestModule
[2025-08-13 16:46:49.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据变化: EventMonitoring
[2025-08-13 16:46:49.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:49.475 INF] [IndustrialHMI] [LIN-PC] [29776] 测试已停止
[2025-08-13 16:46:49.475 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModule 停止完成
[2025-08-13 16:46:49.475 INF] [IndustrialHMI] [LIN-PC] [29776] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:46:49.475 INF] [IndustrialHMI] [LIN-PC] [29776] 停止 CommunicationTestModule
[2025-08-13 16:46:49.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据变化: EventMonitoring
[2025-08-13 16:46:49.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:46:49.475 INF] [IndustrialHMI] [LIN-PC] [29776] 测试已停止
[2025-08-13 16:46:49.475 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModule 停止完成
[2025-08-13 16:46:49.475 DBG] [IndustrialHMI] [LIN-PC] [29776] 系统事件订阅已取消
[2025-08-13 16:46:49.476 DBG] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:46:49.476 DBG] [IndustrialHMI] [LIN-PC] [29776] EventMonitor 资源释放完成
[2025-08-13 16:46:49.476 INF] [IndustrialHMI] [LIN-PC] [29776] 测试已停止
[2025-08-13 16:46:49.476 DBG] [IndustrialHMI] [LIN-PC] [29776] TestCaseManager 资源释放完成
[2025-08-13 16:46:49.476 DBG] [IndustrialHMI] [LIN-PC] [29776] PerformanceMonitor 资源释放完成
[2025-08-13 16:46:49.476 DBG] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModel 资源释放完成
[2025-08-13 16:46:49.477 INF] [IndustrialHMI] [LIN-PC] [29776] CommunicationTestModule 资源释放完成
[2025-08-13 16:46:49.477 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块卸载事件: 通信测试
[2025-08-13 16:46:49.477 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:49.477 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:49.478 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理模块卸载事件: 通信测试
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 模块卸载成功: 通信测试
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载模块: 设备监控
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 停止设备监控模块
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 用户请求停止设备监控
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 停止设备监控
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 设备监控已停止
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 设备监控模块停止完成
[2025-08-13 16:46:49.478 INF] [IndustrialHMI] [LIN-PC] [29776] 开始释放设备监控模块资源
[2025-08-13 16:46:49.479 DBG] [IndustrialHMI] [LIN-PC] [29776] DevicePresenter 资源释放完成
[2025-08-13 16:46:49.479 INF] [IndustrialHMI] [LIN-PC] [29776] 停止设备监控
[2025-08-13 16:46:49.479 DBG] [IndustrialHMI] [LIN-PC] [29776] DeviceModel 资源释放完成
[2025-08-13 16:46:49.479 INF] [IndustrialHMI] [LIN-PC] [29776] 设备监控模块资源释放完成
[2025-08-13 16:46:49.479 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块卸载事件: 设备监控
[2025-08-13 16:46:49.479 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型数据已更新，视图已刷新
[2025-08-13 16:46:49.480 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 收到模块事件: ModuleUnloaded
[2025-08-13 16:46:49.480 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理模块卸载事件: 设备监控
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 模块卸载成功: 设备监控
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载模块: 测试框架模块
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 停止测试框架模块
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 停止内存泄漏测试套件
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 内存泄漏测试套件已停止
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 停止性能测试套件
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 性能测试套件已停止
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 停止集成测试套件
[2025-08-13 16:46:49.480 INF] [IndustrialHMI] [LIN-PC] [29776] 集成测试套件已停止
[2025-08-13 16:46:49.481 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 模型已停止
[2025-08-13 16:46:49.481 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块停止完成
[2025-08-13 16:46:49.481 INF] [IndustrialHMI] [LIN-PC] [29776] 开始释放测试框架模块资源
[2025-08-13 16:46:49.481 INF] [IndustrialHMI] [LIN-PC] [29776] 停止内存泄漏测试套件
[2025-08-13 16:46:49.481 INF] [IndustrialHMI] [LIN-PC] [29776] 内存泄漏测试套件已停止
[2025-08-13 16:46:49.481 DBG] [IndustrialHMI] [LIN-PC] [29776] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:46:49.482 INF] [IndustrialHMI] [LIN-PC] [29776] 停止性能测试套件
[2025-08-13 16:46:49.482 INF] [IndustrialHMI] [LIN-PC] [29776] 性能测试套件已停止
[2025-08-13 16:46:49.482 DBG] [IndustrialHMI] [LIN-PC] [29776] PerformanceTestSuite资源释放完成
[2025-08-13 16:46:49.482 INF] [IndustrialHMI] [LIN-PC] [29776] 停止集成测试套件
[2025-08-13 16:46:49.482 INF] [IndustrialHMI] [LIN-PC] [29776] 集成测试套件已停止
[2025-08-13 16:46:49.482 DBG] [IndustrialHMI] [LIN-PC] [29776] IntegrationTestSuite资源释放完成
[2025-08-13 16:46:49.482 DBG] [IndustrialHMI] [LIN-PC] [29776] TestFrameworkPresenter资源释放完成
[2025-08-13 16:46:49.483 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块资源释放完成
[2025-08-13 16:46:49.483 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块卸载事件: 测试框架模块
[2025-08-13 16:46:49.483 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理模块卸载事件: 测试框架模块
[2025-08-13 16:46:49.483 INF] [IndustrialHMI] [LIN-PC] [29776] 模块卸载成功: 测试框架模块
[2025-08-13 16:46:49.483 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载模块: 测试模块
[2025-08-13 16:46:49.483 INF] [IndustrialHMI] [LIN-PC] [29776] 开始停止模块: 测试模块
[2025-08-13 16:46:49.484 INF] [IndustrialHMI] [LIN-PC] [29776] 停止TestModulePresenter
[2025-08-13 16:46:49.484 DBG] [IndustrialHMI] [LIN-PC] [29776] 系统事件取消订阅完成
[2025-08-13 16:46:49.484 DBG] [IndustrialHMI] [LIN-PC] [29776] 模型状态变化: 模型停止完成
[2025-08-13 16:46:49.484 INF] [IndustrialHMI] [LIN-PC] [29776] TestModulePresenter停止完成
[2025-08-13 16:46:49.485 DBG] [IndustrialHMI] [LIN-PC] [29776] 事件取消订阅完成
[2025-08-13 16:46:49.485 INF] [IndustrialHMI] [LIN-PC] [29776] 模块停止完成: 测试模块
[2025-08-13 16:46:49.485 DBG] [IndustrialHMI] [LIN-PC] [29776] 处理系统事件: ModuleStopped
[2025-08-13 16:46:49.485 INF] [IndustrialHMI] [LIN-PC] [29776] 开始释放模块资源: 测试模块
[2025-08-13 16:46:49.485 INF] [IndustrialHMI] [LIN-PC] [29776] 释放TestModulePresenter资源
[2025-08-13 16:46:49.486 INF] [IndustrialHMI] [LIN-PC] [29776] TestModulePresenter资源释放完成
[2025-08-13 16:46:49.491 INF] [IndustrialHMI] [LIN-PC] [29776] 模块资源释放完成: 测试模块
[2025-08-13 16:46:49.492 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到模块卸载事件: 测试模块
[2025-08-13 16:46:49.494 ERR] [IndustrialHMI] [LIN-PC] [29776] 处理模块卸载事件失败
System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“TextBox”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.CreateHandle()
   在 System.Windows.Forms.TextBoxBase.SetSelectedTextInternal(String text, Boolean clearUndo)
   在 System.Windows.Forms.TextBoxBase.set_SelectedText(String value)
   在 System.Windows.Forms.TextBoxBase.AppendText(String text)
   在 TestModule.Views.TestModuleView.<>c__DisplayClass7_0.<AddLog>b__0() 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 229
   在 TestModule.Views.TestModuleView.SafeUpdateUI(Action action) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 331
   在 TestModule.Views.TestModuleView.AddLog(String message) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Views\TestModuleView.cs:行号 226
   在 TestModule.Presenters.TestModulePresenter.OnModuleUnloaded(ModuleUnloadedEvent moduleEvent) 位置 F:\Project\C#_project\winform\winfoms\Modules.Sources\TestModule\Presenters\TestModulePresenter.cs:行号 453
[2025-08-13 16:46:49.498 INF] [IndustrialHMI] [LIN-PC] [29776] 模块卸载成功: 测试模块
[2025-08-13 16:46:49.498 INF] [IndustrialHMI] [LIN-PC] [29776] 所有模块卸载完成
[2025-08-13 16:46:49.498 INF] [IndustrialHMI] [LIN-PC] [29776] 应用程序关闭流程完成
[2025-08-13 16:46:49.499 INF] [IndustrialHMI] [LIN-PC] [29776] 主窗体已关闭，资源清理完成
[2025-08-13 16:46:49.505 INF] [IndustrialHMI] [LIN-PC] [29776] 测试框架模块收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:49.505 INF] [IndustrialHMI] [LIN-PC] [29776] 模块 测试模块 收到系统关闭事件: UserRequest
[2025-08-13 16:46:49.505 INF] [IndustrialHMI] [LIN-PC] [29776] 收到系统关闭事件，原因: UserRequest
[2025-08-13 16:46:49.506 INF] [IndustrialHMI] [LIN-PC] [29776] 开始释放应用程序资源
[2025-08-13 16:46:49.506 INF] [IndustrialHMI] [LIN-PC] [29776] 开始卸载所有模块
[2025-08-13 16:46:49.506 INF] [IndustrialHMI] [LIN-PC] [29776] 所有模块卸载完成
[2025-08-13 16:46:49.506 INF] [IndustrialHMI] [LIN-PC] [29776] 应用程序资源释放完成
[2025-08-13 16:46:49.506 INF] [IndustrialHMI] [LIN-PC] [29776] === 应用程序正常退出 ===
[2025-08-13 16:46:50.967 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:50.969 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 8
[2025-08-13 16:46:50.971 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:50
[2025-08-13 16:46:55.977 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:46:55.978 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 9
[2025-08-13 16:46:55.978 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:46:55
[2025-08-13 16:47:00.998 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:00.999 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 10
[2025-08-13 16:47:01.000 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:00
[2025-08-13 16:47:06.000 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:06.001 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 11
[2025-08-13 16:47:06.002 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:06
[2025-08-13 16:47:11.020 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:11.021 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 12
[2025-08-13 16:47:11.021 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:11
[2025-08-13 16:47:16.039 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:16.040 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 13
[2025-08-13 16:47:16.040 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:16
[2025-08-13 16:47:21.043 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:21.043 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 14
[2025-08-13 16:47:21.043 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:21
[2025-08-13 16:47:26.048 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:26.049 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 15
[2025-08-13 16:47:26.049 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:26
[2025-08-13 16:47:31.061 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:31.061 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 16
[2025-08-13 16:47:31.062 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:31
[2025-08-13 16:47:36.074 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:36.075 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 17
[2025-08-13 16:47:36.075 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:36
[2025-08-13 16:47:41.085 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:41.087 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 18
[2025-08-13 16:47:41.089 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:41
[2025-08-13 16:47:46.084 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:46.086 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 19
[2025-08-13 16:47:46.088 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:46
[2025-08-13 16:47:51.102 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:51.102 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 20
[2025-08-13 16:47:51.102 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:51
[2025-08-13 16:47:56.113 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:47:56.113 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 21
[2025-08-13 16:47:56.114 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:47:56
[2025-08-13 16:48:01.126 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:01.127 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 22
[2025-08-13 16:48:01.128 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:01
[2025-08-13 16:48:06.146 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:06.147 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 23
[2025-08-13 16:48:06.148 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:06
[2025-08-13 16:48:11.152 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:11.153 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 24
[2025-08-13 16:48:11.153 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:11
[2025-08-13 16:48:16.155 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:16.156 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 25
[2025-08-13 16:48:16.156 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:16
[2025-08-13 16:48:21.160 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:21.161 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 26
[2025-08-13 16:48:21.161 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:21
[2025-08-13 16:48:26.170 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:26.172 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 27
[2025-08-13 16:48:26.174 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:26
[2025-08-13 16:48:31.182 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:31.184 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 28
[2025-08-13 16:48:31.185 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:31
[2025-08-13 16:48:36.192 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:36.193 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 29
[2025-08-13 16:48:36.193 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:36
[2025-08-13 16:48:41.205 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:41.206 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 30
[2025-08-13 16:48:41.207 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:41
[2025-08-13 16:48:46.209 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:46.210 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 31
[2025-08-13 16:48:46.211 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:46
[2025-08-13 16:48:51.212 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:51.213 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 32
[2025-08-13 16:48:51.213 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:51
[2025-08-13 16:48:56.227 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:48:56.227 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 33
[2025-08-13 16:48:56.228 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:48:56
[2025-08-13 16:49:01.229 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:01.230 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 34
[2025-08-13 16:49:01.230 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:01
[2025-08-13 16:49:06.235 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:06.236 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 35
[2025-08-13 16:49:06.240 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:06
[2025-08-13 16:49:11.242 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:11.243 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 36
[2025-08-13 16:49:11.244 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:11
[2025-08-13 16:49:16.251 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:16.252 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 37
[2025-08-13 16:49:16.252 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:16
[2025-08-13 16:49:21.261 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:21.262 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 38
[2025-08-13 16:49:21.263 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:21
[2025-08-13 16:49:26.273 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:26.274 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 39
[2025-08-13 16:49:26.275 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:26
[2025-08-13 16:49:31.272 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:31.273 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 40
[2025-08-13 16:49:31.274 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:31
[2025-08-13 16:49:36.284 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:36.285 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 41
[2025-08-13 16:49:36.285 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:36
[2025-08-13 16:49:41.292 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:41.293 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 42
[2025-08-13 16:49:41.294 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:41
[2025-08-13 16:49:46.301 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:46.301 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 43
[2025-08-13 16:49:46.302 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:46
[2025-08-13 16:49:51.314 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:51.314 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 44
[2025-08-13 16:49:51.315 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:51
[2025-08-13 16:49:56.322 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:49:56.325 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 45
[2025-08-13 16:49:56.328 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:49:56
[2025-08-13 16:50:01.321 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:01.322 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 46
[2025-08-13 16:50:01.322 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:01
[2025-08-13 16:50:06.328 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:06.331 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 47
[2025-08-13 16:50:06.332 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:06
[2025-08-13 16:50:11.338 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:11.339 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 48
[2025-08-13 16:50:11.339 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:11
[2025-08-13 16:50:16.349 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:16.350 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 49
[2025-08-13 16:50:16.351 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:16
[2025-08-13 16:50:21.364 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:21.365 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 50
[2025-08-13 16:50:21.366 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:21
[2025-08-13 16:50:26.374 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:26.375 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 51
[2025-08-13 16:50:26.375 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:26
[2025-08-13 16:50:31.383 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:31.383 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 52
[2025-08-13 16:50:31.383 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:31
[2025-08-13 16:50:36.392 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:36.401 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 53
[2025-08-13 16:50:36.401 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:36
[2025-08-13 16:50:41.400 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:41.401 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 54
[2025-08-13 16:50:41.403 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:41
[2025-08-13 16:50:46.406 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:46.406 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 55
[2025-08-13 16:50:46.406 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:46
[2025-08-13 16:50:51.412 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:51.412 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 56
[2025-08-13 16:50:51.413 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:51
[2025-08-13 16:50:56.418 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:50:56.427 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 57
[2025-08-13 16:50:56.428 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:50:56
[2025-08-13 16:51:01.417 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:51:01.420 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 58
[2025-08-13 16:51:01.421 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:51:01
[2025-08-13 16:51:06.426 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:51:06.427 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 59
[2025-08-13 16:51:06.428 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:51:06
[2025-08-13 16:51:11.432 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化事件处理完成
[2025-08-13 16:51:11.433 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 数据更新: +1, 当前值: 60
[2025-08-13 16:51:11.433 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 定时状态更新 - 16:51:11
[2025-08-13 16:51:16.327 INF] [IndustrialHMI] [LIN-PC] [27908] 用户请求关闭应用程序
[2025-08-13 16:51:16.328 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载所有模块
[2025-08-13 16:51:16.329 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载模块: 报警管理
[2025-08-13 16:51:16.329 INF] [IndustrialHMI] [LIN-PC] [27908] 停止报警管理模块
[2025-08-13 16:51:16.329 INF] [IndustrialHMI] [LIN-PC] [27908] 停止报警监控
[2025-08-13 16:51:16.329 INF] [IndustrialHMI] [LIN-PC] [27908] 停止报警监控
[2025-08-13 16:51:16.329 INF] [IndustrialHMI] [LIN-PC] [27908] 报警管理模块停止完成
[2025-08-13 16:51:16.330 INF] [IndustrialHMI] [LIN-PC] [27908] 开始释放报警管理模块资源
[2025-08-13 16:51:16.331 INF] [IndustrialHMI] [LIN-PC] [27908] 停止报警监控
[2025-08-13 16:51:16.331 INF] [IndustrialHMI] [LIN-PC] [27908] 停止报警监控
[2025-08-13 16:51:16.332 DBG] [IndustrialHMI] [LIN-PC] [27908] AlarmPresenter 资源释放完成
[2025-08-13 16:51:16.332 INF] [IndustrialHMI] [LIN-PC] [27908] 停止报警监控
[2025-08-13 16:51:16.332 DBG] [IndustrialHMI] [LIN-PC] [27908] AlarmModel 资源释放完成
[2025-08-13 16:51:16.347 INF] [IndustrialHMI] [LIN-PC] [27908] 报警管理模块资源释放完成
[2025-08-13 16:51:16.348 INF] [IndustrialHMI] [LIN-PC] [27908] 模块卸载成功: 报警管理
[2025-08-13 16:51:16.348 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载模块: 通信测试
[2025-08-13 16:51:16.348 INF] [IndustrialHMI] [LIN-PC] [27908] 停止 CommunicationTestModule
[2025-08-13 16:51:16.349 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化: EventMonitoring
[2025-08-13 16:51:16.349 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:51:16.349 INF] [IndustrialHMI] [LIN-PC] [27908] 测试已停止
[2025-08-13 16:51:16.349 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModule 停止完成
[2025-08-13 16:51:16.349 INF] [IndustrialHMI] [LIN-PC] [27908] 开始释放 CommunicationTestModule 资源
[2025-08-13 16:51:16.349 INF] [IndustrialHMI] [LIN-PC] [27908] 停止 CommunicationTestModule
[2025-08-13 16:51:16.349 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化: EventMonitoring
[2025-08-13 16:51:16.350 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型数据变化: PerformanceMonitoring
[2025-08-13 16:51:16.350 INF] [IndustrialHMI] [LIN-PC] [27908] 测试已停止
[2025-08-13 16:51:16.350 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModule 停止完成
[2025-08-13 16:51:16.350 DBG] [IndustrialHMI] [LIN-PC] [27908] 系统事件订阅已取消
[2025-08-13 16:51:16.351 DBG] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestPresenter 资源释放完成
[2025-08-13 16:51:16.352 DBG] [IndustrialHMI] [LIN-PC] [27908] EventMonitor 资源释放完成
[2025-08-13 16:51:16.352 INF] [IndustrialHMI] [LIN-PC] [27908] 测试已停止
[2025-08-13 16:51:16.352 DBG] [IndustrialHMI] [LIN-PC] [27908] TestCaseManager 资源释放完成
[2025-08-13 16:51:16.352 DBG] [IndustrialHMI] [LIN-PC] [27908] PerformanceMonitor 资源释放完成
[2025-08-13 16:51:16.352 DBG] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModel 资源释放完成
[2025-08-13 16:51:16.353 INF] [IndustrialHMI] [LIN-PC] [27908] CommunicationTestModule 资源释放完成
[2025-08-13 16:51:16.353 INF] [IndustrialHMI] [LIN-PC] [27908] 模块卸载成功: 通信测试
[2025-08-13 16:51:16.353 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载模块: 设备监控
[2025-08-13 16:51:16.353 INF] [IndustrialHMI] [LIN-PC] [27908] 停止设备监控模块
[2025-08-13 16:51:16.353 INF] [IndustrialHMI] [LIN-PC] [27908] 用户请求停止设备监控
[2025-08-13 16:51:16.353 INF] [IndustrialHMI] [LIN-PC] [27908] 停止设备监控
[2025-08-13 16:51:16.354 INF] [IndustrialHMI] [LIN-PC] [27908] 设备监控已停止
[2025-08-13 16:51:16.354 INF] [IndustrialHMI] [LIN-PC] [27908] 设备监控模块停止完成
[2025-08-13 16:51:16.354 INF] [IndustrialHMI] [LIN-PC] [27908] 开始释放设备监控模块资源
[2025-08-13 16:51:16.354 DBG] [IndustrialHMI] [LIN-PC] [27908] DevicePresenter 资源释放完成
[2025-08-13 16:51:16.354 INF] [IndustrialHMI] [LIN-PC] [27908] 停止设备监控
[2025-08-13 16:51:16.354 DBG] [IndustrialHMI] [LIN-PC] [27908] DeviceModel 资源释放完成
[2025-08-13 16:51:16.354 INF] [IndustrialHMI] [LIN-PC] [27908] 设备监控模块资源释放完成
[2025-08-13 16:51:16.354 INF] [IndustrialHMI] [LIN-PC] [27908] 模块卸载成功: 设备监控
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载模块: 测试框架模块
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 停止测试框架模块
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 停止内存泄漏测试套件
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 内存泄漏测试套件已停止
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 停止性能测试套件
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 性能测试套件已停止
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 停止集成测试套件
[2025-08-13 16:51:16.355 INF] [IndustrialHMI] [LIN-PC] [27908] 集成测试套件已停止
[2025-08-13 16:51:16.360 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 模型已停止
[2025-08-13 16:51:16.360 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块停止完成
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 开始释放测试框架模块资源
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 停止内存泄漏测试套件
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 内存泄漏测试套件已停止
[2025-08-13 16:51:16.361 DBG] [IndustrialHMI] [LIN-PC] [27908] MemoryLeakTestSuite资源释放完成
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 停止性能测试套件
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 性能测试套件已停止
[2025-08-13 16:51:16.361 DBG] [IndustrialHMI] [LIN-PC] [27908] PerformanceTestSuite资源释放完成
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 停止集成测试套件
[2025-08-13 16:51:16.361 INF] [IndustrialHMI] [LIN-PC] [27908] 集成测试套件已停止
[2025-08-13 16:51:16.361 DBG] [IndustrialHMI] [LIN-PC] [27908] IntegrationTestSuite资源释放完成
[2025-08-13 16:51:16.362 DBG] [IndustrialHMI] [LIN-PC] [27908] TestFrameworkPresenter资源释放完成
[2025-08-13 16:51:16.363 INF] [IndustrialHMI] [LIN-PC] [27908] 测试框架模块资源释放完成
[2025-08-13 16:51:16.363 INF] [IndustrialHMI] [LIN-PC] [27908] 模块卸载成功: 测试框架模块
[2025-08-13 16:51:16.363 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载模块: 测试模块
[2025-08-13 16:51:16.363 INF] [IndustrialHMI] [LIN-PC] [27908] 开始停止模块: 测试模块
[2025-08-13 16:51:16.363 INF] [IndustrialHMI] [LIN-PC] [27908] 停止TestModulePresenter
[2025-08-13 16:51:16.363 DBG] [IndustrialHMI] [LIN-PC] [27908] 系统事件取消订阅完成
[2025-08-13 16:51:16.364 DBG] [IndustrialHMI] [LIN-PC] [27908] 模型状态变化: 模型停止完成
[2025-08-13 16:51:16.365 INF] [IndustrialHMI] [LIN-PC] [27908] TestModulePresenter停止完成
[2025-08-13 16:51:16.365 DBG] [IndustrialHMI] [LIN-PC] [27908] 事件取消订阅完成
[2025-08-13 16:51:16.365 INF] [IndustrialHMI] [LIN-PC] [27908] 模块停止完成: 测试模块
[2025-08-13 16:51:16.366 INF] [IndustrialHMI] [LIN-PC] [27908] 开始释放模块资源: 测试模块
[2025-08-13 16:51:16.366 INF] [IndustrialHMI] [LIN-PC] [27908] 释放TestModulePresenter资源
[2025-08-13 16:51:16.366 INF] [IndustrialHMI] [LIN-PC] [27908] TestModulePresenter资源释放完成
[2025-08-13 16:51:16.376 INF] [IndustrialHMI] [LIN-PC] [27908] 模块资源释放完成: 测试模块
[2025-08-13 16:51:16.377 INF] [IndustrialHMI] [LIN-PC] [27908] 模块卸载成功: 测试模块
[2025-08-13 16:51:16.377 INF] [IndustrialHMI] [LIN-PC] [27908] 所有模块卸载完成
[2025-08-13 16:51:16.377 INF] [IndustrialHMI] [LIN-PC] [27908] 应用程序关闭流程完成
[2025-08-13 16:51:16.378 INF] [IndustrialHMI] [LIN-PC] [27908] 主窗体已关闭，资源清理完成
[2025-08-13 16:51:16.413 INF] [IndustrialHMI] [LIN-PC] [27908] 开始释放应用程序资源
[2025-08-13 16:51:16.413 INF] [IndustrialHMI] [LIN-PC] [27908] 开始卸载所有模块
[2025-08-13 16:51:16.413 INF] [IndustrialHMI] [LIN-PC] [27908] 所有模块卸载完成
[2025-08-13 16:51:16.413 INF] [IndustrialHMI] [LIN-PC] [27908] 应用程序资源释放完成
[2025-08-13 16:51:16.413 INF] [IndustrialHMI] [LIN-PC] [27908] === 应用程序正常退出 ===
