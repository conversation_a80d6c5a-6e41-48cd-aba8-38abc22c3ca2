using System;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using Services;
using Contracts;
using Contracts.Events;

namespace SimpleTestApp
{
    static class Program
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool AllocConsole();
        [STAThread]
        static void Main()
        {
            try
            {
                // 分配控制台窗口
                AllocConsole();
                Console.WriteLine("=== 简化测试应用程序 ===");

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // 创建简单的日志记录器
                var logger = new SimpleLogger();
                logger.Info("应用程序启动");
                
                // 创建简单的事件聚合器
                var eventAggregator = new SimpleEventAggregator();
                logger.Info("事件聚合器创建成功");
                
                // 创建模块加载器
                var moduleLoader = new ModuleLoader(eventAggregator, logger, null);
                logger.Info("模块加载器创建成功");
                
                // 加载模块
                var modulesPath = @"bin\Debug\Modules";
                logger.Info($"开始从目录加载模块: {modulesPath}");
                
                var loadedCount = moduleLoader.LoadModulesFromDirectory(modulesPath);
                logger.Info($"加载了 {loadedCount} 个模块");
                
                // 获取已加载的模块
                var modules = moduleLoader.LoadedModules;
                logger.Info($"获取到 {modules.Count} 个已加载模块");
                
                foreach (var module in modules)
                {
                    logger.Info($"模块: {module.Name} - {module.Description}");
                }
                
                // 创建简单的主窗体
                var mainForm = new SimpleMainForm(modules);
                logger.Info("主窗体创建成功");
                
                // 运行应用程序
                Application.Run(mainForm);
                
                logger.Info("应用程序正常退出");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序异常: {ex}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Console.WriteLine($"异常: {ex}");
            }
        }
    }
    
    public class SimpleMainForm : Form
    {
        private readonly System.Collections.Generic.IReadOnlyList<IModule> _modules;
        private TabControl _tabControl;
        
        public SimpleMainForm(System.Collections.Generic.IReadOnlyList<IModule> modules)
        {
            _modules = modules;
            InitializeComponent();
            LoadModules();
        }
        
        private void InitializeComponent()
        {
            Text = "简化测试应用程序";
            Size = new System.Drawing.Size(800, 600);
            StartPosition = FormStartPosition.CenterScreen;
            
            _tabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };
            
            Controls.Add(_tabControl);
        }
        
        private void LoadModules()
        {
            foreach (var module in _modules)
            {
                try
                {
                    var userControl = module.GetUserControl();
                    if (userControl != null)
                    {
                        var tabPage = new TabPage(module.Name)
                        {
                            Tag = module
                        };
                        
                        userControl.Dock = DockStyle.Fill;
                        tabPage.Controls.Add(userControl);
                        _tabControl.TabPages.Add(tabPage);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载模块 {module.Name} 的UI时出错: {ex.Message}", "错误");
                }
            }
        }
    }
    
    public class SimpleLogger : ILogger
    {
        public void Debug(string message) => Console.WriteLine($"[DEBUG] {DateTime.Now:HH:mm:ss} {message}");
        public void Debug(string message, Exception exception) => Console.WriteLine($"[DEBUG] {DateTime.Now:HH:mm:ss} {message}: {exception}");
        public void Info(string message) => Console.WriteLine($"[INFO] {DateTime.Now:HH:mm:ss} {message}");
        public void Info(string message, Exception exception) => Console.WriteLine($"[INFO] {DateTime.Now:HH:mm:ss} {message}: {exception}");
        public void Warning(string message) => Console.WriteLine($"[WARNING] {DateTime.Now:HH:mm:ss} {message}");
        public void Warning(string message, Exception exception) => Console.WriteLine($"[WARNING] {DateTime.Now:HH:mm:ss} {message}: {exception}");
        public void Error(string message) => Console.WriteLine($"[ERROR] {DateTime.Now:HH:mm:ss} {message}");
        public void Error(string message, Exception exception) => Console.WriteLine($"[ERROR] {DateTime.Now:HH:mm:ss} {message}: {exception}");
        public void Fatal(string message) => Console.WriteLine($"[FATAL] {DateTime.Now:HH:mm:ss} {message}");
        public void Fatal(string message, Exception exception) => Console.WriteLine($"[FATAL] {DateTime.Now:HH:mm:ss} {message}: {exception}");
    }
    
    public class SimpleEventAggregator : IEventAggregator
    {
        public IEvent<T> GetEvent<T>() where T : class
        {
            return new SimpleEvent<T>();
        }
    }

    public class SimpleEvent<T> : IEvent<T> where T : class
    {
        public void Publish(T eventData)
        {
            Console.WriteLine($"[EVENT] 发布事件: {typeof(T).Name}");
        }
        
        public void Subscribe(Action<T> handler, ThreadOption threadOption = ThreadOption.UIThread, bool keepSubscriberReferenceAlive = false)
        {
            Console.WriteLine($"[EVENT] 订阅事件: {typeof(T).Name}");
        }
        
        public void Unsubscribe(Action<T> handler)
        {
            Console.WriteLine($"[EVENT] 取消订阅事件: {typeof(T).Name}");
        }
    }
}
