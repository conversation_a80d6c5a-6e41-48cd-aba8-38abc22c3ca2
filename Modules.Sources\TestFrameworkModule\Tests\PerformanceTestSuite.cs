using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using Contracts;
using Contracts.Events;

namespace TestFrameworkModule.Tests
{
    /// <summary>
    /// 性能测试套件
    /// </summary>
    /// <remarks>
    /// 提供系统性能测试和监控功能
    /// </remarks>
    public class PerformanceTestSuite : IDisposable
    {
        #region 私有字段

        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private readonly List<PerformanceTestResult> _testResults;
        private readonly PerformanceCounter _cpuCounter;
        private readonly PerformanceCounter _memoryCounter;
        private bool _disposed = false;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        public PerformanceTestSuite(IEventAggregator eventAggregator, ILogger logger)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _testResults = new List<PerformanceTestResult>();

            try
            {
                // 初始化性能计数器
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            catch (Exception ex)
            {
                _logger.Warning($"性能计数器初始化失败: {ex.Message}");
                // 继续运行，但性能计数器功能将不可用
            }
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 测试结果
        /// </summary>
        public IReadOnlyList<PerformanceTestResult> TestResults => _testResults;

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化测试套件
        /// </summary>
        public void Initialize()
        {
            if (_disposed) return;

            try
            {
                _logger.Info("初始化性能测试套件");
                _testResults.Clear();

                // 预热性能计数器
                if (_cpuCounter != null)
                {
                    _cpuCounter.NextValue();
                    Thread.Sleep(100);
                }

                _logger.Info("性能测试套件初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error("性能测试套件初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止测试套件
        /// </summary>
        public void Stop()
        {
            if (_disposed) return;

            try
            {
                _logger.Info("停止性能测试套件");
                _logger.Info("性能测试套件已停止");
            }
            catch (Exception ex)
            {
                _logger.Error("停止性能测试套件失败", ex);
            }
        }

        /// <summary>
        /// 运行所有性能测试
        /// </summary>
        /// <returns>测试结果</returns>
        public PerformanceTestSuiteResult RunAllTests()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PerformanceTestSuite));

            try
            {
                _logger.Info("开始运行性能测试");
                _testResults.Clear();

                var stopwatch = Stopwatch.StartNew();

                // 运行各项性能测试
                RunEventPerformanceTest();
                RunMemoryUsageTest();
                RunCpuUsageTest();
                RunUIResponseTest();
                RunModuleLoadingPerformanceTest();

                stopwatch.Stop();

                var result = new PerformanceTestSuiteResult
                {
                    SuiteName = "性能测试套件",
                    TotalTests = _testResults.Count,
                    ExecutionTime = stopwatch.Elapsed,
                    TestResults = new List<PerformanceTestResult>(_testResults),
                    SystemMetrics = GetCurrentSystemMetrics()
                };

                _logger.Info($"性能测试完成: {result.TotalTests} 个测试, 耗时: {result.ExecutionTime.TotalSeconds:F2}秒");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error("运行性能测试失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 运行事件性能测试
        /// </summary>
        public void RunEventPerformanceTest()
        {
            var testName = "事件处理性能测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                const int eventCount = 1000;
                int eventsReceived = 0;

                // 订阅测试事件
                _eventAggregator.GetEvent<SystemEvent>()
                    .Subscribe(evt =>
                    {
                        if (evt.EventType == "PerformanceTest")
                        {
                            Interlocked.Increment(ref eventsReceived);
                        }
                    }, keepSubscriberReferenceAlive: false);

                var eventStopwatch = Stopwatch.StartNew();

                // 发布大量事件
                for (int i = 0; i < eventCount; i++)
                {
                    var testEvent = new SystemEvent("PerformanceTest", $"Event_{i}");
                    _eventAggregator.GetEvent<SystemEvent>().Publish(testEvent);
                }

                // 等待所有事件处理完成
                var timeout = DateTime.Now.AddSeconds(5);
                while (eventsReceived < eventCount && DateTime.Now < timeout)
                {
                    Thread.Sleep(10);
                }

                eventStopwatch.Stop();
                stopwatch.Stop();

                var eventsPerSecond = eventCount / eventStopwatch.Elapsed.TotalSeconds;

                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    EventProcessingTime = eventStopwatch.Elapsed,
                    EventsProcessed = eventsReceived,
                    EventsPerSecond = eventsPerSecond,
                    MemoryUsage = GC.GetTotalMemory(false) / (1024 * 1024), // MB
                    Passed = eventsReceived >= eventCount * 0.95, // 95%成功率
                    Message = $"处理了 {eventsReceived}/{eventCount} 个事件, {eventsPerSecond:F0} 事件/秒"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行内存使用测试
        /// </summary>
        public void RunMemoryUsageTest()
        {
            var testName = "内存使用测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                // 记录初始内存
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var initialMemory = GC.GetTotalMemory(false);

                // 创建一些对象来测试内存分配
                var testObjects = new List<byte[]>();
                for (int i = 0; i < 100; i++)
                {
                    testObjects.Add(new byte[1024 * 1024]); // 1MB each
                    Thread.Sleep(10);
                }

                var peakMemory = GC.GetTotalMemory(false);

                // 清理对象
                testObjects.Clear();
                testObjects = null;

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var finalMemory = GC.GetTotalMemory(false);

                stopwatch.Stop();

                var memoryAllocated = (peakMemory - initialMemory) / (1024 * 1024); // MB
                var memoryReleased = (peakMemory - finalMemory) / (1024 * 1024); // MB
                var memoryLeak = (finalMemory - initialMemory) / (1024 * 1024); // MB

                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    MemoryUsage = finalMemory / (1024 * 1024),
                    MemoryAllocated = memoryAllocated,
                    MemoryReleased = memoryReleased,
                    MemoryLeak = memoryLeak,
                    Passed = memoryLeak < 10, // 内存泄漏小于10MB认为通过
                    Message = $"分配: {memoryAllocated:F1}MB, 释放: {memoryReleased:F1}MB, 泄漏: {memoryLeak:F1}MB"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行CPU使用率测试
        /// </summary>
        public void RunCpuUsageTest()
        {
            var testName = "CPU使用率测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                var cpuUsages = new List<float>();

                // 监控CPU使用率
                for (int i = 0; i < 10; i++)
                {
                    if (_cpuCounter != null)
                    {
                        var cpuUsage = _cpuCounter.NextValue();
                        cpuUsages.Add(cpuUsage);
                    }

                    // 执行一些CPU密集型操作
                    var sum = 0.0;
                    for (int j = 0; j < 100000; j++)
                    {
                        sum += Math.Sqrt(j);
                    }

                    Thread.Sleep(100);
                }

                stopwatch.Stop();

                var averageCpuUsage = cpuUsages.Count > 0 ? cpuUsages.Average() : 0;
                var maxCpuUsage = cpuUsages.Count > 0 ? cpuUsages.Max() : 0;

                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    CpuUsage = averageCpuUsage,
                    MaxCpuUsage = maxCpuUsage,
                    Passed = averageCpuUsage < 80, // 平均CPU使用率小于80%认为通过
                    Message = $"平均CPU: {averageCpuUsage:F1}%, 峰值CPU: {maxCpuUsage:F1}%"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行UI响应测试
        /// </summary>
        public void RunUIResponseTest()
        {
            var testName = "UI响应性能测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                var responseTimes = new List<TimeSpan>();

                // 模拟UI操作
                for (int i = 0; i < 50; i++)
                {
                    var operationStopwatch = Stopwatch.StartNew();

                    // 模拟UI操作（在实际环境中这里会是真实的UI操作）
                    Thread.Sleep(new Random().Next(1, 20)); // 1-20ms的随机延迟

                    operationStopwatch.Stop();
                    responseTimes.Add(operationStopwatch.Elapsed);
                }

                stopwatch.Stop();

                var averageResponseTime = TimeSpan.FromTicks((long)responseTimes.Select(t => t.Ticks).Average());
                var maxResponseTime = responseTimes.Max();

                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    AverageResponseTime = averageResponseTime,
                    MaxResponseTime = maxResponseTime,
                    Passed = averageResponseTime.TotalMilliseconds < 100, // 平均响应时间小于100ms
                    Message = $"平均响应: {averageResponseTime.TotalMilliseconds:F1}ms, 最大响应: {maxResponseTime.TotalMilliseconds:F1}ms"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        /// <summary>
        /// 运行模块加载性能测试
        /// </summary>
        public void RunModuleLoadingPerformanceTest()
        {
            var testName = "模块加载性能测试";
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Debug($"开始执行: {testName}");

                var loadingTimes = new List<TimeSpan>();

                // 模拟模块加载
                for (int i = 0; i < 10; i++)
                {
                    var loadStopwatch = Stopwatch.StartNew();

                    // 模拟模块加载过程
                    Thread.Sleep(new Random().Next(50, 200)); // 50-200ms的随机加载时间

                    // 发布模块加载事件
                    var moduleEvent = new ModuleLoadedEvent($"TestModule_{i}", $"测试模块{i}");
                    _eventAggregator.GetEvent<ModuleLoadedEvent>().Publish(moduleEvent);

                    loadStopwatch.Stop();
                    loadingTimes.Add(loadStopwatch.Elapsed);
                }

                stopwatch.Stop();

                var averageLoadingTime = TimeSpan.FromTicks((long)loadingTimes.Select(t => t.Ticks).Average());
                var maxLoadingTime = loadingTimes.Max();
                var totalLoadingTime = TimeSpan.FromTicks(loadingTimes.Sum(t => t.Ticks));

                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    AverageLoadingTime = averageLoadingTime,
                    MaxLoadingTime = maxLoadingTime,
                    TotalLoadingTime = totalLoadingTime,
                    ModulesLoaded = loadingTimes.Count,
                    Passed = averageLoadingTime.TotalSeconds < 2, // 平均加载时间小于2秒
                    Message = $"加载 {loadingTimes.Count} 个模块, 平均: {averageLoadingTime.TotalMilliseconds:F0}ms, 总计: {totalLoadingTime.TotalSeconds:F1}s"
                };

                _testResults.Add(result);
                _logger.Debug($"{testName} - {(result.Passed ? "通过" : "失败")}: {result.Message}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var result = new PerformanceTestResult
                {
                    TestName = testName,
                    ExecutionTime = stopwatch.Elapsed,
                    Passed = false,
                    Message = $"测试异常: {ex.Message}",
                    Details = ex.ToString()
                };

                _testResults.Add(result);
                _logger.Error($"{testName}失败", ex);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取当前系统指标
        /// </summary>
        /// <returns>系统指标</returns>
        private SystemMetrics GetCurrentSystemMetrics()
        {
            try
            {
                return new SystemMetrics
                {
                    CpuUsage = _cpuCounter?.NextValue() ?? 0,
                    AvailableMemoryMB = _memoryCounter?.NextValue() ?? 0,
                    TotalManagedMemoryMB = GC.GetTotalMemory(false) / (1024 * 1024),
                    ProcessorCount = Environment.ProcessorCount,
                    WorkingSetMB = Environment.WorkingSet / (1024 * 1024),
                    Timestamp = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger.Warning($"获取系统指标失败: {ex.Message}");
                return new SystemMetrics { Timestamp = DateTime.Now };
            }
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Stop();
                _testResults.Clear();
                _cpuCounter?.Dispose();
                _memoryCounter?.Dispose();
                _disposed = true;
                _logger?.Debug("PerformanceTestSuite资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("PerformanceTestSuite资源释放失败", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// 性能测试结果
    /// </summary>
    public class PerformanceTestResult
    {
        public string TestName { get; set; }
        public bool Passed { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public string Message { get; set; }
        public string Details { get; set; }

        // 性能指标
        public double MemoryUsage { get; set; }
        public double MemoryAllocated { get; set; }
        public double MemoryReleased { get; set; }
        public double MemoryLeak { get; set; }
        public double CpuUsage { get; set; }
        public double MaxCpuUsage { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public TimeSpan MaxResponseTime { get; set; }
        public TimeSpan AverageLoadingTime { get; set; }
        public TimeSpan MaxLoadingTime { get; set; }
        public TimeSpan TotalLoadingTime { get; set; }
        public TimeSpan EventProcessingTime { get; set; }
        public int EventsProcessed { get; set; }
        public double EventsPerSecond { get; set; }
        public int ModulesLoaded { get; set; }
    }

    /// <summary>
    /// 性能测试套件结果
    /// </summary>
    public class PerformanceTestSuiteResult
    {
        public string SuiteName { get; set; }
        public int TotalTests { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public List<PerformanceTestResult> TestResults { get; set; }
        public SystemMetrics SystemMetrics { get; set; }
    }

    /// <summary>
    /// 系统指标
    /// </summary>
    public class SystemMetrics
    {
        public double CpuUsage { get; set; }
        public double AvailableMemoryMB { get; set; }
        public double TotalManagedMemoryMB { get; set; }
        public int ProcessorCount { get; set; }
        public double WorkingSetMB { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
