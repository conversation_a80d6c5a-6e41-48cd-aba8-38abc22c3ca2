<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    
    <appSettings>
        <!-- 应用程序配置 -->
        <add key="ApplicationName" value="工业HMI系统" />
        <add key="ApplicationVersion" value="1.0.0.0" />
        
        <!-- 模块配置 -->
        <add key="ModulesDirectory" value="Modules" />
        <add key="AutoLoadModules" value="true" />
        
        <!-- 日志配置 -->
        <add key="LogLevel" value="Debug" />
        <add key="LogDirectory" value="logs" />
        <add key="LogFileMaxSize" value="10485760" />
        <add key="LogFileRetentionDays" value="30" />
        
        <!-- UI配置 -->
        <add key="MainFormTitle" value="工业HMI系统" />
        <add key="MainFormWidth" value="1200" />
        <add key="MainFormHeight" value="800" />
        <add key="MainFormMaximized" value="true" />
        
        <!-- 系统配置 -->
        <add key="EnableGlobalExceptionHandling" value="true" />
        <add key="ShowDebugInfo" value="false" />
    </appSettings>
    
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="DryIoc" publicKeyToken="dfbf2bd50fcf7768" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-5.4.3.0" newVersion="5.4.3.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
</configuration>
