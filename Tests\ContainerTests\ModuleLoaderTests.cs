using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Shell.Bootstrapper;
using Contracts;
using Contracts.Events;
using Services;

namespace ContainerTests
{
    /// <summary>
    /// ModuleLoader测试类
    /// </summary>
    [TestClass]
    public class ModuleLoaderTests
    {
        private SimpleContainer _container;
        private ModuleLoader _moduleLoader;
        private IEventAggregator _eventAggregator;
        private ILogger _logger;

        /// <summary>
        /// 测试初始化
        /// </summary>
        [TestInitialize]
        public void TestInitialize()
        {
            _container = ContainerBootstrapper.CreateContainer();
            _eventAggregator = _container.Resolve<IEventAggregator>();
            _logger = _container.Resolve<ILogger>();
            _moduleLoader = _container.Resolve<ModuleLoader>();
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            _moduleLoader?.Dispose();
            _container?.Dispose();
        }

        /// <summary>
        /// 测试ModuleLoader创建
        /// </summary>
        [TestMethod]
        public void ModuleLoader_ShouldBeCreated()
        {
            // Assert
            Assert.IsNotNull(_moduleLoader);
            Assert.IsNotNull(_moduleLoader.LoadedModules);
            Assert.AreEqual(0, _moduleLoader.LoadedModules.Count);
        }

        /// <summary>
        /// 测试从不存在的目录加载模块
        /// </summary>
        [TestMethod]
        public void LoadModulesFromDirectory_NonExistentDirectory_ShouldReturnZero()
        {
            // Arrange
            var nonExistentDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

            // Act
            var loadedCount = _moduleLoader.LoadModulesFromDirectory(nonExistentDirectory);

            // Assert
            Assert.AreEqual(0, loadedCount);
        }

        /// <summary>
        /// 测试从空目录加载模块
        /// </summary>
        [TestMethod]
        public void LoadModulesFromDirectory_EmptyDirectory_ShouldReturnZero()
        {
            // Arrange
            var emptyDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(emptyDirectory);

            try
            {
                // Act
                var loadedCount = _moduleLoader.LoadModulesFromDirectory(emptyDirectory);

                // Assert
                Assert.AreEqual(0, loadedCount);
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(emptyDirectory))
                {
                    Directory.Delete(emptyDirectory, true);
                }
            }
        }

        /// <summary>
        /// 测试从无效程序集加载模块
        /// </summary>
        [TestMethod]
        public void LoadModulesFromAssembly_InvalidAssembly_ShouldReturnZero()
        {
            // Arrange
            var invalidAssemblyPath = "NonExistent.dll";

            // Act
            var loadedCount = _moduleLoader.LoadModulesFromAssembly(invalidAssemblyPath);

            // Assert
            Assert.AreEqual(0, loadedCount);
        }

        /// <summary>
        /// 测试卸载所有模块
        /// </summary>
        [TestMethod]
        public void UnloadAllModules_ShouldCompleteSuccessfully()
        {
            // Act & Assert - 应该不抛出异常
            _moduleLoader.UnloadAllModules();
            Assert.AreEqual(0, _moduleLoader.LoadedModules.Count);
        }

        /// <summary>
        /// 测试ModuleLoader的Dispose
        /// </summary>
        [TestMethod]
        public void Dispose_ShouldCompleteSuccessfully()
        {
            // Act & Assert - 应该不抛出异常
            _moduleLoader.Dispose();
            Assert.IsTrue(true);
        }
    }

    /// <summary>
    /// 测试模块类
    /// </summary>
    public class TestModule : IModule
    {
        public string Name => "TestModule";
        public string Description => "用于测试的模块";
        public IEventAggregator EventAggregator { get; set; }
        public ILogger Logger { get; set; }

        public void Initialize()
        {
            // 测试初始化
        }

        public UserControl GetUserControl()
        {
            return new UserControl();
        }

        public void Start()
        {
            // 测试启动
        }

        public void Stop()
        {
            // 测试停止
        }

        public void Dispose()
        {
            // 测试释放
        }
    }
}
