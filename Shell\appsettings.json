{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "DryIoc": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/application-.log", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] [{Application}] {Message:lj} {Properties:j}{NewLine}{Exception}", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "shared": true, "flushToDiskInterval": "00:00:01"}}], "Enrich": ["WithProperty"], "Properties": {"Application": "IndustrialHMI", "Environment": "Development"}}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "System": "Information"}}, "Application": {"Name": "工业HMI系统", "Version": "1.0.0", "Description": "基于模块化架构的工业上位机软件", "Company": "Industrial Solutions", "Copyright": "Copyright © 2025 Industrial Solutions. All rights reserved."}, "ModuleSettings": {"ModulesDirectory": "<PERSON><PERSON><PERSON>", "AutoLoadModules": true, "ModuleLoadTimeout": 30000, "EnableModuleHotReload": false}, "UISettings": {"Theme": "<PERSON><PERSON><PERSON>", "Language": "zh-CN", "StartMaximized": true, "EnableFullScreen": true, "StatusBarUpdateInterval": 5000, "MemoryMonitoringEnabled": true}, "PerformanceSettings": {"GCCollectionInterval": 300000, "MaxMemoryUsageMB": 1024, "EnablePerformanceCounters": true, "LogPerformanceMetrics": true}}