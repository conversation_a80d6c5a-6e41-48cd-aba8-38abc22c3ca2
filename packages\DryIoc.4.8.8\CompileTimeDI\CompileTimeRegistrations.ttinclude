<#
// TODO:
// 1. Fill-in method `GetContainerWithRegistrations` below with creation of DryIoc `Container` and registrations.
// 2. Specify the resolution roots via `SpecifyResolutionRoots` method, see example below.
// 3. Save the "Container.Generated.tt" file. Confirm the Visual Studio prompt if any.
// 4. Check the "Container.Generated.cs" for the generated results and issues.
//
// Note: 
// - When specifying assembly path, you may use $(SolutionDir), $(ProjectDir), $(Configuration) parameters.
//
#>
<#@ import Namespace="DryIoc" #>
<#@ import Namespace="ImTools" #>
<#// TODO: Insert the assemblies and namespaces of your services to be registered in container #>
<#@ import Namespace="Example" #>
<#+
    // TODO: Specify the container and registrations ...
    IContainer GetContainerWithRegistrations()
    {
        var container = new Container();

        // NOTE: `RegisterDelegate`, `RegisterInstance` and `Use` methods are not supported because of runtime state usage. 
        // Instead you can use `RegisterPlaceholder` to put a hole in the generated object graph, then you fill it in with the runtime registration.

        // TODO: Add the registrations to generate factories for them in compile-time...
        // for example:
        container.Register<IService, MyService>();
        container.Register<IDependencyA, DependencyA>();
        container.Register(typeof(DependencyB<>));
        container.RegisterPlaceholder<RuntimeDependencyC>();
        
        // or from the assembly:
        // container.RegisterMany(new[] { MyAssembly });

        return container;
    }

    // TODO: For each passed registration specify what resolution roots it provides, null if none
    ServiceInfo[] SpecifyResolutionRoots(ServiceRegistrationInfo reg)
    {
        return reg.AsResolutionRoot ? reg.ToServiceInfo().One() : null;
    }

    // TODO: Explicitly specified roots to generate ...
    ServiceInfo[] CustomResolutionRoots =
    {
        // for example:
        ServiceInfo.Of<Example.IService>(), 
    };

    // TODO: Specify the namespace to go into `using` instead of qualify the type all the times, 
    // You may generate the Container.Generated.cs first, then look what you want to move to using
    string[] NamespaceUsings =
    {
        "Example",
        //"Foo.Bar.Buzz",
    };
#>