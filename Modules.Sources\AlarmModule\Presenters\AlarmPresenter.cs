using System;
using System.Threading.Tasks;
using Contracts;
using AlarmModule.Models;
using AlarmModule.Views;

namespace AlarmModule.Presenters
{
    /// <summary>
    /// 报警管理表示器
    /// </summary>
    /// <remarks>
    /// 协调报警视图和模型之间的交互，处理用户操作和业务逻辑
    /// </remarks>
    public class AlarmPresenter : IPresenter, IDisposable
    {
        private readonly AlarmView _view;
        private readonly AlarmModel _model;
        private readonly ILogger _logger;
        private bool _disposed = false;

        /// <summary>
        /// 关联的视图
        /// </summary>
        public IView View { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="view">报警视图</param>
        /// <param name="model">报警模型</param>
        /// <param name="logger">日志记录器</param>
        public AlarmPresenter(AlarmView view, AlarmModel model, ILogger logger)
        {
            _view = view ?? throw new ArgumentNullException(nameof(view));
            _model = model ?? throw new ArgumentNullException(nameof(model));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            View = _view; // 设置IPresenter.View属性

            // 绑定视图事件
            BindViewEvents();

            // 绑定模型事件
            BindModelEvents();

            _logger.Debug("AlarmPresenter 初始化完成");
        }

        /// <summary>
        /// 绑定视图事件
        /// </summary>
        private void BindViewEvents()
        {
            _view.RefreshRequested += OnRefreshRequested;
            _view.AcknowledgeAlarmRequested += OnAcknowledgeAlarmRequested;
            _view.AcknowledgeAllAlarmsRequested += OnAcknowledgeAllAlarmsRequested;
            _view.ClearAlarmRequested += OnClearAlarmRequested;
            _view.ClearAcknowledgedAlarmsRequested += OnClearAcknowledgedAlarmsRequested;
        }

        /// <summary>
        /// 绑定模型事件
        /// </summary>
        private void BindModelEvents()
        {
            _model.ActiveAlarmsChanged += OnActiveAlarmsChanged;
            _model.AlarmHistoryChanged += OnAlarmHistoryChanged;
            _model.StatisticsChanged += OnStatisticsChanged;
        }

        /// <summary>
        /// 刷新请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnRefreshRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求刷新报警数据");
                _view.SetLoadingState(true, "正在刷新报警数据...");

                await Task.Run(() =>
                {
                    _model.LoadAlarmData();
                });

                _view.SetLoadingState(false);
                _view.UpdateStatus("报警数据刷新完成");
                _logger.Info("报警数据刷新完成");
            }
            catch (Exception ex)
            {
                _view.SetLoadingState(false);
                _view.ShowError($"刷新报警数据失败: {ex.Message}");
                _logger.Error("刷新报警数据时发生错误", ex);
            }
        }

        /// <summary>
        /// 确认报警请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnAcknowledgeAlarmRequested(object sender, AlarmActionEventArgs e)
        {
            try
            {
                _logger.Info($"用户请求确认报警: {e.AlarmId}");
                _view.UpdateStatus($"正在确认报警: {e.AlarmId}");

                await Task.Run(() =>
                {
                    _model.AcknowledgeAlarm(e.AlarmId, "User");
                });

                _view.UpdateStatus($"报警确认成功: {e.AlarmId}");
                _logger.Info($"报警确认成功: {e.AlarmId}");
            }
            catch (Exception ex)
            {
                _view.ShowError($"确认报警失败: {ex.Message}");
                _logger.Error($"确认报警时发生错误: {e.AlarmId}", ex);
            }
        }

        /// <summary>
        /// 确认所有报警请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnAcknowledgeAllAlarmsRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求确认所有报警");
                _view.UpdateStatus("正在确认所有报警...");

                await Task.Run(() =>
                {
                    _model.AcknowledgeAllAlarms("User");
                });

                _view.UpdateStatus("所有报警确认完成");
                _logger.Info("所有报警确认完成");
            }
            catch (Exception ex)
            {
                _view.ShowError($"确认所有报警失败: {ex.Message}");
                _logger.Error("确认所有报警时发生错误", ex);
            }
        }

        /// <summary>
        /// 清除报警请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnClearAlarmRequested(object sender, AlarmActionEventArgs e)
        {
            try
            {
                _logger.Info($"用户请求清除报警: {e.AlarmId}");
                _view.UpdateStatus($"正在清除报警: {e.AlarmId}");

                await Task.Run(() =>
                {
                    _model.ClearAlarm(e.AlarmId);
                });

                _view.UpdateStatus($"报警清除成功: {e.AlarmId}");
                _logger.Info($"报警清除成功: {e.AlarmId}");
            }
            catch (Exception ex)
            {
                _view.ShowError($"清除报警失败: {ex.Message}");
                _logger.Error($"清除报警时发生错误: {e.AlarmId}", ex);
            }
        }

        /// <summary>
        /// 清除已确认报警请求事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnClearAcknowledgedAlarmsRequested(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("用户请求清除已确认的报警");
                _view.UpdateStatus("正在清除已确认的报警...");

                await Task.Run(() =>
                {
                    _model.ClearAcknowledgedAlarms();
                });

                _view.UpdateStatus("已确认报警清除完成");
                _logger.Info("已确认报警清除完成");
            }
            catch (Exception ex)
            {
                _view.ShowError($"清除已确认报警失败: {ex.Message}");
                _logger.Error("清除已确认报警时发生错误", ex);
            }
        }

        /// <summary>
        /// 活动报警列表变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnActiveAlarmsChanged(object sender, AlarmListChangedEventArgs e)
        {
            try
            {
                _logger.Debug($"活动报警列表已更新，共 {e.Alarms.Count} 个报警");
                _view.ShowActiveAlarms(e.Alarms);
            }
            catch (Exception ex)
            {
                _view.ShowError($"更新活动报警列表显示失败: {ex.Message}");
                _logger.Error("更新活动报警列表显示时发生错误", ex);
            }
        }

        /// <summary>
        /// 历史报警列表变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnAlarmHistoryChanged(object sender, AlarmListChangedEventArgs e)
        {
            try
            {
                _logger.Debug($"历史报警列表已更新，共 {e.Alarms.Count} 个报警");
                _view.ShowAlarmHistory(e.Alarms);
            }
            catch (Exception ex)
            {
                _view.ShowError($"更新历史报警列表显示失败: {ex.Message}");
                _logger.Error("更新历史报警列表显示时发生错误", ex);
            }
        }

        /// <summary>
        /// 统计信息变化事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnStatisticsChanged(object sender, AlarmStatisticsChangedEventArgs e)
        {
            try
            {
                _logger.Debug("报警统计信息已更新");
                _view.UpdateStatistics(e.Statistics);
            }
            catch (Exception ex)
            {
                _view.ShowError($"更新统计信息显示失败: {ex.Message}");
                _logger.Error("更新统计信息显示时发生错误", ex);
            }
        }

        /// <summary>
        /// 初始化表示器
        /// </summary>
        public void Initialize()
        {
            try
            {
                _logger.Info("初始化报警表示器");
                
                // 加载初始报警数据
                LoadData();
                
                _view.UpdateStatus("报警管理模块已就绪");
                _logger.Info("报警表示器初始化完成");
            }
            catch (Exception ex)
            {
                _view.ShowError($"初始化报警表示器失败: {ex.Message}");
                _logger.Error("初始化报警表示器时发生错误", ex);
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        public void LoadData()
        {
            try
            {
                _logger.Debug("加载报警数据");
                _model.LoadAlarmData();
            }
            catch (Exception ex)
            {
                _view.ShowError($"加载报警数据失败: {ex.Message}");
                _logger.Error("加载报警数据时发生错误", ex);
            }
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <returns>保存是否成功</returns>
        public bool SaveData()
        {
            // 报警管理模块通常不需要保存数据
            return true;
        }

        /// <summary>
        /// 验证数据
        /// </summary>
        /// <returns>验证是否通过</returns>
        public bool ValidateData()
        {
            // 报警管理模块通常不需要验证数据
            return true;
        }

        /// <summary>
        /// 启动监控
        /// </summary>
        public void StartMonitoring()
        {
            try
            {
                _logger.Info("启动报警监控");
                _model.StartMonitoring();
                _view.UpdateStatus("报警监控已启动");
            }
            catch (Exception ex)
            {
                _view.ShowError($"启动报警监控失败: {ex.Message}");
                _logger.Error("启动报警监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            try
            {
                _logger.Info("停止报警监控");
                _model.StopMonitoring();
                _view.UpdateStatus("报警监控已停止");
            }
            catch (Exception ex)
            {
                _view.ShowError($"停止报警监控失败: {ex.Message}");
                _logger.Error("停止报警监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                // 停止监控
                StopMonitoring();

                // 取消视图事件绑定
                if (_view != null)
                {
                    _view.RefreshRequested -= OnRefreshRequested;
                    _view.AcknowledgeAlarmRequested -= OnAcknowledgeAlarmRequested;
                    _view.AcknowledgeAllAlarmsRequested -= OnAcknowledgeAllAlarmsRequested;
                    _view.ClearAlarmRequested -= OnClearAlarmRequested;
                    _view.ClearAcknowledgedAlarmsRequested -= OnClearAcknowledgedAlarmsRequested;
                }

                // 取消模型事件绑定
                if (_model != null)
                {
                    _model.ActiveAlarmsChanged -= OnActiveAlarmsChanged;
                    _model.AlarmHistoryChanged -= OnAlarmHistoryChanged;
                    _model.StatisticsChanged -= OnStatisticsChanged;
                }

                _logger?.Debug("AlarmPresenter 资源释放完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("释放 AlarmPresenter 资源时发生错误", ex);
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
