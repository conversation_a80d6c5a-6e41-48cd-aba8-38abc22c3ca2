using System;
using System.Threading;
using System.Threading.Tasks;

namespace TestModule.Models
{
    /// <summary>
    /// 测试模块模型
    /// </summary>
    /// <remarks>
    /// 负责测试模块的业务逻辑和数据管理
    /// </remarks>
    public class TestModuleModel : IDisposable
    {
        private bool _isStarted = false;
        private bool _isDisposed = false;
        private Timer _statusTimer;
        private int _dataCounter = 0;
        private readonly object _lock = new object();

        /// <summary>
        /// 数据变化事件
        /// </summary>
        public event EventHandler DataChanged;

        /// <summary>
        /// 状态变化事件
        /// </summary>
        public event EventHandler<string> StatusChanged;

        /// <summary>
        /// 当前数据计数
        /// </summary>
        public int DataCounter
        {
            get
            {
                lock (_lock)
                {
                    return _dataCounter;
                }
            }
            private set
            {
                lock (_lock)
                {
                    if (_dataCounter != value)
                    {
                        _dataCounter = value;
                        OnDataChanged();
                    }
                }
            }
        }

        /// <summary>
        /// 是否已启动
        /// </summary>
        public bool IsStarted
        {
            get
            {
                lock (_lock)
                {
                    return _isStarted;
                }
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public TestModuleModel()
        {
            // 初始化模型
        }

        /// <summary>
        /// 启动模型
        /// </summary>
        public void Start()
        {
            lock (_lock)
            {
                if (_isStarted)
                {
                    return;
                }

                try
                {
                    // 启动状态定时器，每5秒更新一次状态
                    _statusTimer = new Timer(OnStatusTimerTick, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));

                    _isStarted = true;
                    OnStatusChanged("模型启动完成");
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"模型启动失败: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 停止模型
        /// </summary>
        public void Stop()
        {
            lock (_lock)
            {
                if (!_isStarted)
                {
                    return;
                }

                try
                {
                    // 停止定时器
                    _statusTimer?.Dispose();
                    _statusTimer = null;

                    _isStarted = false;
                    OnStatusChanged("模型停止完成");
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"模型停止失败: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 处理系统启动事件
        /// </summary>
        public void OnSystemStartup()
        {
            try
            {
                OnStatusChanged("收到系统启动事件");
                
                // 模拟一些启动后的初始化工作
                Task.Run(() =>
                {
                    Thread.Sleep(1000); // 模拟耗时操作
                    DataCounter = 1;
                    OnStatusChanged("系统启动后初始化完成");
                });
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理系统启动事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理系统关闭事件
        /// </summary>
        public void OnSystemShutdown()
        {
            try
            {
                OnStatusChanged("收到系统关闭事件");
                
                // 模拟一些关闭前的清理工作
                DataCounter = 0;
                OnStatusChanged("系统关闭前清理完成");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理系统关闭事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="increment">增量</param>
        public void UpdateData(int increment = 1)
        {
            try
            {
                DataCounter += increment;
                OnStatusChanged($"数据更新: +{increment}, 当前值: {DataCounter}");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"数据更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置数据
        /// </summary>
        public void ResetData()
        {
            try
            {
                DataCounter = 0;
                OnStatusChanged("数据已重置");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"数据重置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 状态定时器回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private void OnStatusTimerTick(object state)
        {
            try
            {
                if (_isStarted)
                {
                    // 模拟定期数据更新
                    UpdateData();
                    
                    var currentTime = DateTime.Now.ToString("HH:mm:ss");
                    OnStatusChanged($"定时状态更新 - {currentTime}");
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"定时状态更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发数据变化事件
        /// </summary>
        protected virtual void OnDataChanged()
        {
            try
            {
                DataChanged?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                OnStatusChanged($"触发数据变化事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发状态变化事件
        /// </summary>
        /// <param name="status">状态信息</param>
        protected virtual void OnStatusChanged(string status)
        {
            try
            {
                StatusChanged?.Invoke(this, status);
            }
            catch
            {
                // 避免在状态变化事件中再次抛出异常
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                // 停止模型（如果还在运行）
                if (_isStarted)
                {
                    Stop();
                }

                // 释放定时器
                _statusTimer?.Dispose();
                _statusTimer = null;

                _isDisposed = true;
                OnStatusChanged("模型资源释放完成");
            }
            catch
            {
                // 在Dispose中不抛出异常
            }
        }
    }
}
