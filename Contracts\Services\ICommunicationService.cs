using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Contracts.Services
{
    /// <summary>
    /// 通信服务接口
    /// </summary>
    /// <remarks>
    /// 提供与外部设备或系统的通信功能，
    /// 支持多种通信协议和连接管理
    /// </remarks>
    public interface ICommunicationService
    {
        /// <summary>
        /// 连接到指定地址
        /// </summary>
        /// <param name="address">连接地址</param>
        /// <param name="parameters">连接参数</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync(string address, Dictionary<string, object> parameters = null);

        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns>断开是否成功</returns>
        Task<bool> DisconnectAsync();

        /// <summary>
        /// 检查连接状态
        /// </summary>
        /// <returns>是否已连接</returns>
        bool IsConnected { get; }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendAsync(byte[] data);

        /// <summary>
        /// 发送字符串数据
        /// </summary>
        /// <param name="message">要发送的消息</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendAsync(string message);

        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>接收到的数据</returns>
        Task<byte[]> ReceiveAsync(int timeout = 5000);

        /// <summary>
        /// 接收字符串数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <returns>接收到的消息</returns>
        Task<string> ReceiveStringAsync(int timeout = 5000);

        /// <summary>
        /// 读取设备数据
        /// </summary>
        /// <param name="address">数据地址</param>
        /// <param name="length">数据长度</param>
        /// <returns>读取到的数据</returns>
        Task<object> ReadAsync(string address, int length = 1);

        /// <summary>
        /// 写入设备数据
        /// </summary>
        /// <param name="address">数据地址</param>
        /// <param name="value">要写入的值</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteAsync(string address, object value);

        /// <summary>
        /// 批量读取数据
        /// </summary>
        /// <param name="addresses">数据地址列表</param>
        /// <returns>读取结果字典</returns>
        Task<Dictionary<string, object>> ReadMultipleAsync(IEnumerable<string> addresses);

        /// <summary>
        /// 批量写入数据
        /// </summary>
        /// <param name="values">地址值对字典</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteMultipleAsync(Dictionary<string, object> values);

        /// <summary>
        /// 获取连接信息
        /// </summary>
        ConnectionInfo ConnectionInfo { get; }

        /// <summary>
        /// 获取通信统计信息
        /// </summary>
        CommunicationStatistics Statistics { get; }

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        event EventHandler<DataReceivedEventArgs> DataReceived;

        /// <summary>
        /// 通信错误事件
        /// </summary>
        event EventHandler<CommunicationErrorEventArgs> CommunicationError;
    }

    /// <summary>
    /// 连接信息
    /// </summary>
    public class ConnectionInfo
    {
        /// <summary>
        /// 连接地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 连接类型
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// 连接参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; }

        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime ConnectedTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConnectionInfo()
        {
            Parameters = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// 通信统计信息
    /// </summary>
    public class CommunicationStatistics
    {
        /// <summary>
        /// 发送字节数
        /// </summary>
        public long BytesSent { get; set; }

        /// <summary>
        /// 接收字节数
        /// </summary>
        public long BytesReceived { get; set; }

        /// <summary>
        /// 发送消息数
        /// </summary>
        public long MessagesSent { get; set; }

        /// <summary>
        /// 接收消息数
        /// </summary>
        public long MessagesReceived { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }

        /// <summary>
        /// 重连次数
        /// </summary>
        public long ReconnectCount { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            BytesSent = 0;
            BytesReceived = 0;
            MessagesSent = 0;
            MessagesReceived = 0;
            ErrorCount = 0;
            ReconnectCount = 0;
            LastActivity = DateTime.Now;
        }
    }

    /// <summary>
    /// 连接状态变化事件参数
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 状态变化时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 错误信息（如果连接失败）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isConnected">是否已连接</param>
        /// <param name="errorMessage">错误信息</param>
        public ConnectionStatusChangedEventArgs(bool isConnected, string errorMessage = "")
        {
            IsConnected = isConnected;
            ErrorMessage = errorMessage;
        }
    }

    /// <summary>
    /// 数据接收事件参数
    /// </summary>
    public class DataReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 接收到的数据
        /// </summary>
        public byte[] Data { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="data">接收到的数据</param>
        public DataReceivedEventArgs(byte[] data)
        {
            Data = data;
        }
    }

    /// <summary>
    /// 通信错误事件参数
    /// </summary>
    public class CommunicationErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="exception">异常对象</param>
        public CommunicationErrorEventArgs(string errorMessage, Exception exception = null)
        {
            ErrorMessage = errorMessage;
            Exception = exception;
        }
    }
}
