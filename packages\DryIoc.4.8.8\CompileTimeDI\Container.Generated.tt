// <auto-generated/>
#if !PCL && !NETSTANDARD1_0 && !NETSTANDARD1_1 && !NETSTANDARD1_2 && !NETSTANDARD1_3 && !NETSTANDARD1_4 && !NETSTANDARD1_5
/*
The MIT License (MIT)

Copyright (c) 2016-2022 <PERSON><PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/
<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension=".cs" #>
<#@ CleanupBehavior processor="T4VSHost" CleanupAfterProcessingtemplate="true" #>
<#@ assembly Name="System.Core" #>
<#@ assembly Name="System.Runtime" #>
<#@ assembly Name="$(ExpressionToCodeLibAssembly)" #>
<#@ assembly Name="$(TargetPath)" #>
<#@ import Namespace="ExpressionToCodeLib" #>
<#@ import Namespace="System.Linq" #>
<#@ import Namespace="System.Linq.Expressions" #>
<#@ import Namespace="DryIoc" #>
<#@ import Namespace="ImTools" #>
<#@ include File="CompileTimeRegistrations.ttinclude" #>
<#
var container = GetContainerWithRegistrations().With(rules => rules.ForExpressionGeneration());

var includeVariants = container.Rules.VariantGenericTypesInResolvedCollection;

var result = container.GenerateResolutionExpressions(x => x.SelectMany(r =>
    SpecifyResolutionRoots(r).EmptyIfNull()).Concat(CustomResolutionRoots.EmptyIfNull()));

var exprToCode = ExpressionToCodeConfiguration.DefaultCodeGenConfiguration
    .WithObjectStringifier(ObjectStringify.WithFullTypeNames);

string RemoveUsings(string source)
{
    foreach (var x in NamespaceUsings)
        source = source.Replace(x + ".", "");
    return source;
}

string Code(object x) =>
    x == null ? "null" :
    x is Expression ? RemoveUsings(exprToCode.ToCode((Expression)x)) : 
    x is Request ? Code(container.GetRequestExpression((Request)x).ToExpression()) : 
    Code(container.GetConstantExpression(x, x.GetType(), true).ToExpression());

string GetTypeNameOnly(string typeName) => typeName.Split('`').First().Split('.').Last();

string OptArg(string arg) => arg == "null" ? "" : ", " + arg;

var rootCodes = result.Roots.Select((r, i) => 
    new { ServiceType = r.Key.ServiceType,
          ServiceTypeCode = Code(r.Key.ServiceType),
          ServiceKeyCode = Code(r.Key.ServiceKey),
          RequiredServiceTypeCode = Code(r.Key.Details.RequiredServiceType),
          ExpressionCode = Code(r.Value.Body),
          CreateMethodName = "Get_" + i + "_" + GetTypeNameOnly(r.Key.ServiceType.Name) });

var depCodes = result.ResolveDependencies.Select((r, i) => 
    new { ServiceType = Code(r.Key.ServiceType),
          ServiceKey = Code(r.Key.ServiceKey), ServiceKeyObject = r.Key.ServiceKey,
          Expression = Code(r.Value),
          RequiredServiceType = Code(r.Key.RequiredServiceType),
          PreResolveParent = Code(r.Key.Parent),
          CreateMethodName = "GetDep_" + i + "_" + GetTypeNameOnly(r.Key.ServiceType.Name) });
#>
/*
========================================================================================================
NOTE: The code below is generated automatically at compile-time and not supposed to be changed by hand.
========================================================================================================
<#  var errCount = result.Errors.Count;
    if (errCount == 0) { #>
Generation is completed successfully.
<# } else { #>
There are <#=errCount#> generation issues (may be not an error dependent on context):

The issues with run-time registrations may be solved by `container.RegisterPlaceholder<T>()` 
in Registrations.ttinclude. Then you can replace placeholders using `DryIocZero.Container.Register`
at runtime.

<# } #>
--------------------------------------------------------------------------------------------------------
<#  var eNum = 0;
    foreach(var e in result.Errors) { #>
<#=++eNum#>. <#=e.Key#>
Error: <#=e.Value.Message#>
<#  } #>
*/

using System;
using System.Linq; // for Enumerable.Cast method required by LazyEnumerable<T>
using System.Collections.Generic;
using System.Threading;
using ImTools;

// Specified `NamespaceUsings` if any:
<# foreach (var ns in NamespaceUsings) {#>
using <#=ns#>;
<#}#>

namespace DryIoc
{
    partial class Container
    {
        partial void GetLastGeneratedFactoryID(ref int lastFactoryID)
        {
            lastFactoryID = <#=Factory.GetNextID()#>; // generated: equals to the last used Factory.FactoryID 
        }

        partial void ResolveGenerated(ref object service, Type serviceType)
        {
<#
        var index = 0;
        foreach (var root in rootCodes.Where(f => f.ServiceKeyCode == "null"))
        {
            if (index++ > 0) WriteLine(@"
            else");
#>
            if (serviceType == <#=root.ServiceTypeCode#>)
                service = <#=root.CreateMethodName#>(this);
<#
        }
#>
        }

        partial void ResolveGenerated(ref object service,
            Type serviceType, object serviceKey, Type requiredServiceType, Request preRequestParent, object[] args)
        {
<#
        index = 0;
        foreach (var rootGroup in rootCodes.Where(x => x.ServiceKeyCode != "null").GroupBy(x => x.ServiceTypeCode))
        {
            if (index++ > 0) WriteLine(@"
            else");
#>
            if (serviceType == <#=rootGroup.Key#>) 
            {
<#
            var innerIndex = 0;
            foreach (var root in rootGroup)
            {
                if (innerIndex++ > 0) WriteLine(@"
                else");
#>
                if (<#=root.ServiceKeyCode#>.Equals(serviceKey))
                    service = <#=root.CreateMethodName#>(this);
<#
            }

#>
            }
<#
        }
#>
<#
        foreach (var depGroup in depCodes.GroupBy(x => x.ServiceType))
        {
            if (index++ > 0) WriteLine(@"
            else");
#>
            if (serviceType == <#=depGroup.Key#>) 
            {
<#
            var innerIndex = 0;
            foreach (var dep in depGroup)
            {
                if (innerIndex++ > 0) WriteLine(@"
                else");
#>
                if (<#=dep.ServiceKeyObject == null ? "serviceKey == null"
                     : dep.ServiceKeyObject is DefaultKey ? "(serviceKey == null || " + dep.ServiceKey + ".Equals(serviceKey))"
                     : dep.ServiceKey + ".Equals(serviceKey)"#> &&
                    requiredServiceType == <#= dep.RequiredServiceType #> &&
                    Equals(preRequestParent, <#= dep.PreResolveParent #>)) 
                    service = <#=dep.CreateMethodName#>(this);
<#
            }
#>
            }
<#
        }
#>
        }

        partial void ResolveManyGenerated(ref IEnumerable<ResolveManyResult> services, Type serviceType) 
        {
            services = ResolveManyGenerated(serviceType);
        }

        private IEnumerable<ResolveManyResult> ResolveManyGenerated(Type serviceType)
        {
<#
        if (!rootCodes.Any())
        {
#>
            yield break;
<#
        }
        else 
        {
            foreach (var rootGroup in rootCodes.GroupBy(x => x.ServiceType))
            {
#>
            if (serviceType == <#=rootGroup.First().ServiceTypeCode#>)
            {
<#
                foreach (var root in rootGroup)
                {
#>
                yield return ResolveManyResult.Of(<#=root.CreateMethodName#><#=OptArg(root.ServiceKeyCode)#><#=OptArg(root.RequiredServiceTypeCode)#>);
<#
                }

                if (includeVariants && rootGroup.Key.IsGeneric())
                {
                    var sourceType = rootGroup.Key;
                    var variants = rootCodes
                        .Where(x => x.ServiceType.IsGeneric() &&
                            x.ServiceType.GetGenericTypeDefinition() == sourceType.GetGenericTypeDefinition() &&
                            x.ServiceType != sourceType && x.ServiceType.IsAssignableTo(sourceType));
                    foreach (var variant in variants)
                    {
#>
                yield return ResolveManyResult.Of(<#=variant.CreateMethodName#><#=OptArg(variant.ServiceKeyCode)#><#=OptArg(variant.RequiredServiceTypeCode)#>); // co-variant
<#
                    }
                }
#>
            }

<#
        }
        }
#>
        }

<#
    foreach (var root in rootCodes)
    {
#>
        // <#=root.ServiceTypeCode#>
        internal static object <#=root.CreateMethodName#>(IResolverContext r)
        {
            return <#=root.ExpressionCode#>;
        }

<#        
    }
#>
<#
    foreach (var dep in depCodes)
    {
#>
        // <#=dep.ServiceType#>
        internal static object <#=dep.CreateMethodName#>(IResolverContext r)
        {
            return <#=dep.Expression#>;
        }

<#
    }
#>
    }
}
#endif
