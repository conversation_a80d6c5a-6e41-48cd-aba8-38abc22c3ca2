using System;

namespace Contracts.Events
{
    /// <summary>
    /// 事件聚合器接口
    /// </summary>
    /// <remarks>
    /// 事件聚合器是模块间通信的核心组件，
    /// 提供发布-订阅模式的事件机制，实现模块间的松耦合通信
    /// </remarks>
    public interface IEventAggregator
    {
        /// <summary>
        /// 获取指定类型的事件
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <returns>事件对象</returns>
        /// <remarks>
        /// 如果事件类型不存在，会自动创建新的事件实例
        /// </remarks>
        IEvent<T> GetEvent<T>() where T : class;
    }

    /// <summary>
    /// 事件接口
    /// </summary>
    /// <typeparam name="T">事件数据类型</typeparam>
    /// <remarks>
    /// 提供事件的发布和订阅功能，支持线程安全和弱引用
    /// </remarks>
    public interface IEvent<T> where T : class
    {
        /// <summary>
        /// 发布事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <remarks>
        /// 发布事件到所有订阅者，自动处理UI线程调度
        /// </remarks>
        void Publish(T eventData);

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <param name="threadOption">线程选项</param>
        /// <param name="keepSubscriberReferenceAlive">是否保持订阅者引用</param>
        /// <remarks>
        /// 建议设置keepSubscriberReferenceAlive为false以避免内存泄漏
        /// </remarks>
        void Subscribe(Action<T> handler, ThreadOption threadOption = ThreadOption.UIThread, bool keepSubscriberReferenceAlive = false);

        /// <summary>
        /// 取消订阅
        /// </summary>
        /// <param name="handler">事件处理器</param>
        /// <remarks>
        /// 在模块Dispose时必须调用此方法取消所有订阅
        /// </remarks>
        void Unsubscribe(Action<T> handler);
    }

    /// <summary>
    /// 线程选项枚举
    /// </summary>
    /// <remarks>
    /// 指定事件处理器在哪个线程中执行
    /// </remarks>
    public enum ThreadOption
    {
        /// <summary>
        /// 在发布者线程中执行
        /// </summary>
        PublisherThread,

        /// <summary>
        /// 在UI线程中执行
        /// </summary>
        UIThread,

        /// <summary>
        /// 在后台线程中执行
        /// </summary>
        BackgroundThread
    }
}
