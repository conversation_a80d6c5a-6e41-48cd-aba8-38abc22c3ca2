using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("DependencyCheck")]
[assembly: AssemblyDescription("工业HMI框架依赖检查工具")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Industrial HMI Framework")]
[assembly: AssemblyCopyright("Copyright ©  2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]
[assembly: Guid("c9f4e3b2-9d5e-4f6a-8b7c-0e1f2a3b4c5d")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
