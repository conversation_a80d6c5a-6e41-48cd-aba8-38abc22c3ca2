# 工业HMI框架用户操作手册

## 版本信息

- **软件版本**: 1.0.0
- **文档版本**: 1.0.0
- **发布日期**: 2025-08-13
- **适用平台**: Windows 10/11, Windows Server 2016+

## 目录

1. [系统概述](#系统概述)
2. [系统要求](#系统要求)
3. [安装指南](#安装指南)
4. [快速入门](#快速入门)
5. [功能模块介绍](#功能模块介绍)
6. [操作指南](#操作指南)
7. [故障排除](#故障排除)
8. [技术支持](#技术支持)

## 系统概述

工业HMI框架是一个基于.NET Framework 4.8的模块化工业人机界面系统，采用MVP（Model-View-Presenter）架构设计，支持动态模块加载和事件驱动通信。

### 主要特性

- **模块化架构**: 支持动态加载和卸载功能模块
- **事件驱动**: 基于事件聚合器的松耦合通信机制
- **高性能**: 优化的启动时间和内存使用
- **可扩展**: 标准化的模块开发接口
- **易维护**: 完整的日志记录和性能监控

### 核心模块

- **设备监控模块**: 实时监控设备连接状态和运行参数
- **报警管理模块**: 实时接收和管理系统报警信息
- **通信测试模块**: 验证模块间通信的稳定性和性能
- **测试框架模块**: 提供系统集成测试和性能测试功能

## 系统要求

### 硬件要求

- **处理器**: Intel Core i3 或同等性能处理器
- **内存**: 最小 4GB RAM，推荐 8GB 或更多
- **存储**: 最小 500MB 可用磁盘空间
- **显示**: 分辨率 1024x768 或更高

### 软件要求

- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **.NET Framework**: 4.8 或更高版本
- **权限**: 管理员权限（用于安装和首次运行）

### 网络要求

- 如需远程监控功能，需要稳定的网络连接
- 防火墙需要允许应用程序的网络访问

## 安装指南

### 自动安装（推荐）

1. **下载安装包**
   - 从官方渠道下载最新版本的安装包
   - 确保下载的文件完整且未被篡改

2. **运行部署脚本**

   ```powershell
   # 以管理员身份运行PowerShell
   # 导航到安装包目录
   cd "安装包路径"
   
   # 执行部署脚本
   .\Deploy.ps1
   
   # 或指定自定义安装路径
   .\Deploy.ps1 -TargetPath "D:\MyHMI"
   ```

3. **验证安装**
   - 检查桌面是否出现"工业HMI框架"快捷方式
   - 双击快捷方式启动应用程序
   - 确认所有模块正常加载

### 手动安装

1. **创建安装目录**

   ```
   C:\IndustrialHMI\
   ├── IndustrialHMI.exe
   ├── Contracts.dll
   ├── Services.dll
   ├── Modules\
   ├── logs\
   └── config\
   ```

2. **复制文件**
   - 将所有程序文件复制到安装目录
   - 确保Modules文件夹包含所有模块DLL文件

3. **配置环境**
   - 确保.NET Framework 4.8已安装
   - 检查所有依赖文件是否完整

## 快速入门

### 首次启动

1. **启动应用程序**
   - 双击桌面快捷方式或直接运行IndustrialHMI.exe
   - 系统将自动加载所有可用模块

2. **界面概览**
   - 主窗口采用标签页设计，每个模块占用一个标签页
   - 底部状态栏显示系统运行状态和模块加载信息

3. **模块导航**
   - 点击不同的标签页切换到对应的功能模块
   - 每个模块都有独立的用户界面和功能

### 基本操作

1. **设备监控**
   - 切换到"设备监控"标签页
   - 查看设备列表和连接状态
   - 使用"连接"/"断开"按钮控制设备连接

2. **报警管理**
   - 切换到"报警管理"标签页
   - 查看当前报警列表
   - 使用"确认"按钮确认报警

3. **通信测试**
   - 切换到"通信测试"标签页
   - 选择测试用例并执行
   - 查看测试结果和性能数据

## 功能模块介绍

### 设备监控模块

**功能描述**: 实时监控设备连接状态和运行参数

**主要功能**:

- 设备列表显示
- 设备连接/断开控制
- 实时状态监控
- 设备参数查看

**操作步骤**:

1. 在设备列表中选择目标设备
2. 点击"连接"按钮建立连接
3. 查看设备状态和参数信息
4. 需要时点击"断开"按钮断开连接

### 报警管理模块

**功能描述**: 实时接收和管理系统报警信息

**主要功能**:

- 报警信息显示
- 报警确认和清除
- 报警历史记录
- 报警统计分析

**操作步骤**:

1. 查看报警列表中的当前报警
2. 选择需要处理的报警项
3. 点击"确认"按钮确认报警
4. 使用"清除"功能清理已确认的报警

### 通信测试模块

**功能描述**: 验证模块间通信的稳定性和性能

**主要功能**:

- 通信测试用例执行
- 性能监控和分析
- 测试结果报告
- 事件监控功能

**操作步骤**:

1. 选择要执行的测试用例
2. 点击"运行测试"按钮开始测试
3. 观察测试进度和实时结果
4. 查看详细的测试报告

### 测试框架模块

**功能描述**: 提供系统集成测试和性能测试功能

**主要功能**:

- 集成测试套件
- 性能测试套件
- 内存泄漏检测
- 测试报告生成

**操作步骤**:

1. 选择测试类型（集成测试/性能测试/内存测试）
2. 配置测试参数
3. 执行测试并监控进度
4. 查看和导出测试报告

## 操作指南

### 日常操作

1. **系统启动**
   - 确保系统资源充足
   - 以管理员权限启动应用程序
   - 等待所有模块加载完成

2. **模块切换**
   - 使用标签页快速切换模块
   - 每个模块保持独立的状态
   - 支持多模块同时运行

3. **数据查看**
   - 实时数据自动刷新
   - 支持手动刷新功能
   - 历史数据查询和导出

4. **系统关闭**
   - 使用菜单或快捷键正常退出
   - 系统自动保存当前状态
   - 确保所有模块正确释放资源

### 高级操作

1. **性能监控**
   - 通过通信测试模块查看系统性能
   - 监控CPU、内存使用情况
   - 分析模块加载和响应时间

2. **日志查看**
   - 日志文件位置：安装目录\logs\
   - 支持按日期和级别筛选
   - 用于故障诊断和性能分析

3. **配置管理**
   - 配置文件位置：安装目录\config\
   - 支持模块参数自定义
   - 修改后需重启应用程序生效

## 故障排除

### 常见问题

**问题1: 应用程序无法启动**

- 检查.NET Framework 4.8是否已安装
- 确认所有依赖文件是否完整
- 以管理员权限运行应用程序

**问题2: 模块加载失败**

- 检查Modules文件夹中的DLL文件
- 查看日志文件中的错误信息
- 确认模块文件没有被损坏

**问题3: 性能问题**

- 检查系统资源使用情况
- 关闭不必要的后台程序
- 查看性能监控数据

**问题4: 数据显示异常**

- 检查数据源连接状态
- 刷新数据显示
- 查看相关模块的日志信息

### 日志分析

日志文件位置：`安装目录\logs\application.log`

日志级别说明：

- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息记录
- **WARNING**: 警告信息
- **ERROR**: 错误信息

## 配置管理

### 配置文件位置

系统配置文件位于以下位置：

- **默认配置**: `安装目录\IndustrialHMI.exe.config`
- **自定义配置**: `安装目录\config\application.config`
- **环境配置**: 系统环境变量

### 常用配置项

#### 日志配置

```xml
<appSettings>
  <add key="LogLevel" value="Info" />        <!-- 日志级别: Debug, Info, Warning, Error -->
  <add key="LogPath" value="logs" />         <!-- 日志文件路径 -->
</appSettings>
```

#### 性能配置

```xml
<appSettings>
  <add key="MaxModules" value="10" />                    <!-- 最大模块数量 -->
  <add key="EnableAdvancedFeatures" value="true" />      <!-- 启用高级功能 -->
  <add key="UIUpdateInterval" value="100" />             <!-- UI更新间隔(毫秒) -->
</appSettings>
```

#### 数据库配置

```xml
<appSettings>
  <add key="DatabaseConnectionString" value="Server=localhost;Database=HMI;Integrated Security=true" />
  <add key="DatabaseTimeout" value="30" />               <!-- 数据库超时时间(秒) -->
</appSettings>
```

### 环境变量配置

对于生产环境，建议使用环境变量进行配置：

#### Windows环境变量设置

1. 右键"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中添加：

| 变量名 | 示例值 | 说明 |
|--------|--------|------|
| INDUSTRIAL_HMI_LogLevel | Info | 日志级别 |
| INDUSTRIAL_HMI_Database_Host | prod-server | 数据库服务器 |
| INDUSTRIAL_HMI_Database_Port | 5432 | 数据库端口 |

#### 命令行设置环境变量

```cmd
# 设置环境变量
set INDUSTRIAL_HMI_LogLevel=Debug
set INDUSTRIAL_HMI_Database_Host=localhost

# 验证设置
echo %INDUSTRIAL_HMI_LogLevel%
```

### 配置优先级

系统按以下优先级读取配置（优先级从高到低）：

1. **环境变量** - 最高优先级，适用于生产环境
2. **自定义配置文件** - 中等优先级，适用于特定配置
3. **默认配置文件** - 最低优先级，系统默认配置

### 配置热更新

系统支持配置热更新功能：

- 修改`config\application.config`文件后，系统会自动重新加载配置
- 无需重启应用程序
- 配置变更会在几秒钟内生效

**注意事项**：

- 环境变量的修改需要重启应用程序才能生效
- 某些核心配置项可能需要重启才能完全生效

### 配置备份与恢复

#### 备份配置

```cmd
# 创建配置备份
copy "C:\IndustrialHMI\config\application.config" "C:\IndustrialHMI\config\application.config.backup"
```

#### 恢复配置

```cmd
# 恢复配置备份
copy "C:\IndustrialHMI\config\application.config.backup" "C:\IndustrialHMI\config\application.config"
```

### 配置验证

如果配置出现问题，可以通过以下方式验证：

1. **检查配置文件格式**：确保XML格式正确
2. **查看应用程序日志**：检查配置加载错误
3. **使用默认配置**：临时删除自定义配置文件，使用默认配置

### 联系技术支持

如果问题无法解决，请联系技术支持：

- 准备详细的问题描述
- 提供相关的日志文件
- 说明操作系统和软件版本信息
- 提供当前的配置文件内容

**技术支持联系方式**：

- 邮箱：<<EMAIL>>
- 电话：400-XXX-XXXX
- 在线支持：<https://support.industrialhmi.com>

## 技术支持

### 支持渠道

- **技术文档**: 查阅开发者文档和API文档
- **在线帮助**: 访问官方网站获取最新信息
- **技术论坛**: 与其他用户交流使用经验

### 版本更新

- 定期检查软件更新
- 备份重要数据后进行升级
- 查看版本更新说明

### 培训资源

- 用户培训视频
- 操作指南文档
- 最佳实践案例

---

**版权声明**: 本文档版权归工业HMI框架开发团队所有。未经许可，不得复制或传播。
