using System;

namespace Contracts
{
    /// <summary>
    /// MVP模式中的视图接口
    /// </summary>
    /// <remarks>
    /// 视图负责用户界面的显示和用户交互，
    /// 不包含业务逻辑，所有业务逻辑由Presenter处理
    /// </remarks>
    public interface IView
    {
        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="message">错误信息</param>
        /// <remarks>通常显示为消息框或状态栏提示</remarks>
        void ShowError(string message);

        /// <summary>
        /// 显示信息提示
        /// </summary>
        /// <param name="message">提示信息</param>
        /// <remarks>用于显示操作成功或一般信息</remarks>
        void ShowMessage(string message);

        /// <summary>
        /// 显示警告信息
        /// </summary>
        /// <param name="message">警告信息</param>
        /// <remarks>用于显示需要用户注意的信息</remarks>
        void ShowWarning(string message);

        /// <summary>
        /// 设置加载状态
        /// </summary>
        /// <param name="isLoading">是否正在加载</param>
        /// <param name="message">加载提示信息</param>
        /// <remarks>用于显示长时间操作的进度状态</remarks>
        void SetLoadingState(bool isLoading, string message = "");

        /// <summary>
        /// 刷新视图数据
        /// </summary>
        /// <remarks>当数据发生变化时调用此方法更新UI显示</remarks>
        void RefreshData();
    }

    /// <summary>
    /// 泛型视图接口，用于强类型的Presenter绑定
    /// </summary>
    /// <typeparam name="TPresenter">关联的Presenter类型</typeparam>
    public interface IView<TPresenter> : IView where TPresenter : IPresenter
    {
        /// <summary>
        /// 关联的Presenter
        /// </summary>
        /// <remarks>由依赖注入容器设置</remarks>
        TPresenter Presenter { get; set; }
    }
}
