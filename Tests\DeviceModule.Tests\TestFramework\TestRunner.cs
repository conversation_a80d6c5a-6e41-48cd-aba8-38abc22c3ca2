using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using static DeviceModule.Tests.TestFramework.SimpleTestFramework;

namespace DeviceModule.Tests.TestFramework
{
    /// <summary>
    /// 测试运行器
    /// </summary>
    /// <remarks>
    /// 负责发现和运行测试，生成测试报告
    /// </remarks>
    public class TestRunner
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        /// <param name="assembly">程序集</param>
        /// <returns>测试结果列表</returns>
        public static List<TestResult> RunAllTests(Assembly assembly = null)
        {
            if (assembly == null)
                assembly = Assembly.GetExecutingAssembly();

            var allResults = new List<TestResult>();

            // 查找所有测试类
            var testClasses = assembly.GetTypes()
                .Where(t => t.GetCustomAttribute<TestClassAttribute>() != null)
                .ToList();

            Console.WriteLine($"发现 {testClasses.Count} 个测试类");
            Console.WriteLine(new string('=', 60));

            foreach (var testClass in testClasses)
            {
                Console.WriteLine($"运行测试类: {testClass.Name}");
                Console.WriteLine(new string('-', 40));

                var results = SimpleTestFramework.RunTestClass(testClass);
                allResults.AddRange(results);

                // 显示测试结果
                foreach (var result in results)
                {
                    var status = result.Passed ? "✅ 通过" : "❌ 失败";
                    Console.WriteLine($"  {status} {result.TestName} ({result.Duration.TotalMilliseconds:F2}ms)");
                    
                    if (!result.Passed)
                    {
                        Console.WriteLine($"    错误: {result.ErrorMessage}");
                        if (result.Exception != null)
                        {
                            Console.WriteLine($"    异常类型: {result.Exception.GetType().Name}");
                        }
                    }
                }

                Console.WriteLine();
            }

            return allResults;
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        /// <param name="results">测试结果列表</param>
        public static void GenerateReport(List<TestResult> results)
        {
            Console.WriteLine(new string('=', 60));
            Console.WriteLine("测试报告");
            Console.WriteLine(new string('=', 60));

            var totalTests = results.Count;
            var passedTests = results.Count(r => r.Passed);
            var failedTests = totalTests - passedTests;
            var totalTime = results.Sum(r => r.Duration.TotalMilliseconds);

            Console.WriteLine($"总测试数: {totalTests}");
            Console.WriteLine($"通过: {passedTests}");
            Console.WriteLine($"失败: {failedTests}");
            Console.WriteLine($"成功率: {(totalTests > 0 ? (double)passedTests / totalTests * 100 : 0):F1}%");
            Console.WriteLine($"总耗时: {totalTime:F2}ms");

            if (failedTests > 0)
            {
                Console.WriteLine();
                Console.WriteLine("失败的测试:");
                Console.WriteLine(new string('-', 40));

                foreach (var failedTest in results.Where(r => !r.Passed))
                {
                    Console.WriteLine($"❌ {failedTest.TestClass}.{failedTest.TestName}");
                    Console.WriteLine($"   错误: {failedTest.ErrorMessage}");
                }
            }

            Console.WriteLine(new string('=', 60));

            // 计算覆盖率指标
            var coverageInfo = CalculateCoverage(results);
            Console.WriteLine($"测试覆盖率评估: {coverageInfo}");
        }

        /// <summary>
        /// 计算覆盖率信息
        /// </summary>
        /// <param name="results">测试结果</param>
        /// <returns>覆盖率信息</returns>
        private static string CalculateCoverage(List<TestResult> results)
        {
            var testClasses = results.GroupBy(r => r.TestClass).Count();
            var totalMethods = results.Count;
            var passedMethods = results.Count(r => r.Passed);

            // 简单的覆盖率估算
            var methodCoverage = totalMethods > 0 ? (double)passedMethods / totalMethods * 100 : 0;
            
            return $"方法覆盖率 {methodCoverage:F1}%, 测试类数 {testClasses}";
        }

        /// <summary>
        /// 运行特定测试类
        /// </summary>
        /// <typeparam name="T">测试类类型</typeparam>
        /// <returns>测试结果列表</returns>
        public static List<TestResult> RunTestClass<T>() where T : class
        {
            return SimpleTestFramework.RunTestClass(typeof(T));
        }

        /// <summary>
        /// 检查测试环境
        /// </summary>
        /// <returns>环境检查结果</returns>
        public static bool CheckTestEnvironment()
        {
            try
            {
                Console.WriteLine("检查测试环境...");
                
                // 检查程序集加载
                var assembly = Assembly.GetExecutingAssembly();
                Console.WriteLine($"✅ 程序集加载成功: {assembly.GetName().Name}");

                // 检查反射功能
                var types = assembly.GetTypes();
                Console.WriteLine($"✅ 反射功能正常: 发现 {types.Length} 个类型");

                // 检查测试框架
                var testFrameworkType = typeof(SimpleTestFramework);
                Console.WriteLine($"✅ 测试框架加载成功: {testFrameworkType.Name}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试环境检查失败: {ex.Message}");
                return false;
            }
        }
    }
}
