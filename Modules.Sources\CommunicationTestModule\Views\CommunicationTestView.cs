using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Contracts;
using CommunicationTestModule.Services;

namespace CommunicationTestModule.Views
{
    /// <summary>
    /// 通信测试视图
    /// </summary>
    /// <remarks>
    /// 提供通信测试的用户界面，包括事件监控、测试执行和性能监控
    /// </remarks>
    public partial class CommunicationTestView : UserControl, IView
    {
        private readonly ILogger _logger;

        // 控件声明
        private TabControl tabControl;
        private TabPage tabEventMonitor;
        private TabPage tabTestExecution;
        private TabPage tabPerformanceMonitor;
        private TabPage tabTestResults;

        // 事件监控页面控件
        private GroupBox grpEventControl;
        private Button btnStartEventMonitor;
        private Button btnStopEventMonitor;
        private Button btnClearEvents;
        private DataGridView dgvEvents;
        private Label lblEventStats;

        // 测试执行页面控件
        private GroupBox grpTestControl;
        private ComboBox cmbTestCategory;
        private Button btnRunAllTests;
        private Button btnRunCategoryTests;
        private Button btnStopTests;
        private ProgressBar progressBar;
        private Label lblTestProgress;
        private DataGridView dgvTestCases;

        // 性能监控页面控件
        private GroupBox grpPerformanceControl;
        private Button btnStartPerformanceMonitor;
        private Button btnStopPerformanceMonitor;
        private Button btnClearPerformance;
        private DataGridView dgvPerformance;
        private Label lblPerformanceStats;

        // 测试结果页面控件
        private GroupBox grpResultControl;
        private Button btnGenerateReport;
        private Button btnClearResults;
        private DataGridView dgvTestResults;
        private TextBox txtTestSummary;

        /// <summary>
        /// 事件监控操作事件
        /// </summary>
        public event EventHandler<EventMonitorActionEventArgs> EventMonitorActionRequested;

        /// <summary>
        /// 测试执行操作事件
        /// </summary>
        public event EventHandler<TestExecutionActionEventArgs> TestExecutionActionRequested;

        /// <summary>
        /// 性能监控操作事件
        /// </summary>
        public event EventHandler<PerformanceMonitorActionEventArgs> PerformanceMonitorActionRequested;

        /// <summary>
        /// 结果操作事件
        /// </summary>
        public event EventHandler<ResultActionEventArgs> ResultActionRequested;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public CommunicationTestView(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            InitializeComponent();
            SetupEventHandlers();
            _logger.Debug("CommunicationTestView 初始化完成");
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            SuspendLayout();

            // 主容器
            this.Size = new Size(1000, 700);
            this.BackColor = Color.White;

            // 创建选项卡控件
            CreateTabControl();
            CreateEventMonitorTab();
            CreateTestExecutionTab();
            CreatePerformanceMonitorTab();
            CreateTestResultsTab();

            ResumeLayout(false);
        }

        /// <summary>
        /// 创建选项卡控件
        /// </summary>
        private void CreateTabControl()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9F)
            };

            tabEventMonitor = new TabPage("事件监控");
            tabTestExecution = new TabPage("测试执行");
            tabPerformanceMonitor = new TabPage("性能监控");
            tabTestResults = new TabPage("测试结果");

            tabControl.TabPages.AddRange(new TabPage[] {
                tabEventMonitor,
                tabTestExecution,
                tabPerformanceMonitor,
                tabTestResults
            });

            this.Controls.Add(tabControl);
        }

        /// <summary>
        /// 创建事件监控选项卡
        /// </summary>
        private void CreateEventMonitorTab()
        {
            // 控制面板
            grpEventControl = new GroupBox
            {
                Text = "事件监控控制",
                Location = new Point(10, 10),
                Size = new Size(960, 80),
                Font = new Font("Microsoft YaHei", 9F)
            };

            btnStartEventMonitor = new Button
            {
                Text = "开始监控",
                Location = new Point(20, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightGreen
            };

            btnStopEventMonitor = new Button
            {
                Text = "停止监控",
                Location = new Point(140, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightCoral,
                Enabled = false
            };

            btnClearEvents = new Button
            {
                Text = "清除记录",
                Location = new Point(260, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightBlue
            };

            lblEventStats = new Label
            {
                Text = "事件统计: 总数=0, 最近1小时=0, 最近1分钟=0",
                Location = new Point(380, 35),
                Size = new Size(400, 20),
                Font = new Font("Microsoft YaHei", 9F)
            };

            grpEventControl.Controls.AddRange(new Control[] {
                btnStartEventMonitor, btnStopEventMonitor, btnClearEvents, lblEventStats
            });

            // 事件列表
            dgvEvents = new DataGridView
            {
                Location = new Point(10, 100),
                Size = new Size(960, 520),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Microsoft YaHei", 8F)
            };

            // 设置事件列表列
            dgvEvents.Columns.AddRange(new DataGridViewColumn[] {
                new DataGridViewTextBoxColumn { Name = "Timestamp", HeaderText = "时间", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "EventType", HeaderText = "事件类型", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Source", HeaderText = "事件源", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "描述", Width = 300 }
            });

            tabEventMonitor.Controls.AddRange(new Control[] { grpEventControl, dgvEvents });
        }

        /// <summary>
        /// 创建测试执行选项卡
        /// </summary>
        private void CreateTestExecutionTab()
        {
            // 控制面板
            grpTestControl = new GroupBox
            {
                Text = "测试控制",
                Location = new Point(10, 10),
                Size = new Size(960, 120),
                Font = new Font("Microsoft YaHei", 9F)
            };

            cmbTestCategory = new ComboBox
            {
                Location = new Point(20, 25),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            btnRunAllTests = new Button
            {
                Text = "运行所有测试",
                Location = new Point(190, 25),
                Size = new Size(120, 35),
                BackColor = Color.LightGreen
            };

            btnRunCategoryTests = new Button
            {
                Text = "运行分类测试",
                Location = new Point(330, 25),
                Size = new Size(120, 35),
                BackColor = Color.LightBlue
            };

            btnStopTests = new Button
            {
                Text = "停止测试",
                Location = new Point(470, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightCoral,
                Enabled = false
            };

            progressBar = new ProgressBar
            {
                Location = new Point(20, 70),
                Size = new Size(400, 25),
                Style = ProgressBarStyle.Continuous
            };

            lblTestProgress = new Label
            {
                Text = "就绪",
                Location = new Point(440, 75),
                Size = new Size(300, 20),
                Font = new Font("Microsoft YaHei", 9F)
            };

            grpTestControl.Controls.AddRange(new Control[] {
                cmbTestCategory, btnRunAllTests, btnRunCategoryTests, btnStopTests,
                progressBar, lblTestProgress
            });

            // 测试用例列表
            dgvTestCases = new DataGridView
            {
                Location = new Point(10, 140),
                Size = new Size(960, 480),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Microsoft YaHei", 8F)
            };

            // 设置测试用例列表列
            dgvTestCases.Columns.AddRange(new DataGridViewColumn[] {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "测试名称", Width = 250 },
                new DataGridViewTextBoxColumn { Name = "Category", HeaderText = "分类", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "描述", Width = 350 }
            });

            tabTestExecution.Controls.AddRange(new Control[] { grpTestControl, dgvTestCases });
        }

        /// <summary>
        /// 创建性能监控选项卡
        /// </summary>
        private void CreatePerformanceMonitorTab()
        {
            // 控制面板
            grpPerformanceControl = new GroupBox
            {
                Text = "性能监控控制",
                Location = new Point(10, 10),
                Size = new Size(960, 80),
                Font = new Font("Microsoft YaHei", 9F)
            };

            btnStartPerformanceMonitor = new Button
            {
                Text = "开始监控",
                Location = new Point(20, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightGreen
            };

            btnStopPerformanceMonitor = new Button
            {
                Text = "停止监控",
                Location = new Point(140, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightCoral,
                Enabled = false
            };

            btnClearPerformance = new Button
            {
                Text = "清除数据",
                Location = new Point(260, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightBlue
            };

            lblPerformanceStats = new Label
            {
                Text = "性能统计: CPU=0%, 内存=0MB, 线程=0",
                Location = new Point(380, 35),
                Size = new Size(400, 20),
                Font = new Font("Microsoft YaHei", 9F)
            };

            grpPerformanceControl.Controls.AddRange(new Control[] {
                btnStartPerformanceMonitor, btnStopPerformanceMonitor, btnClearPerformance, lblPerformanceStats
            });

            // 性能数据列表
            dgvPerformance = new DataGridView
            {
                Location = new Point(10, 100),
                Size = new Size(960, 520),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Microsoft YaHei", 8F)
            };

            // 设置性能数据列表列
            dgvPerformance.Columns.AddRange(new DataGridViewColumn[] {
                new DataGridViewTextBoxColumn { Name = "Timestamp", HeaderText = "时间", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "CpuUsage", HeaderText = "CPU使用率(%)", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "MemoryUsage", HeaderText = "内存使用(MB)", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ThreadCount", HeaderText = "线程数", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "HandleCount", HeaderText = "句柄数", Width = 100 }
            });

            tabPerformanceMonitor.Controls.AddRange(new Control[] { grpPerformanceControl, dgvPerformance });
        }

        /// <summary>
        /// 创建测试结果选项卡
        /// </summary>
        private void CreateTestResultsTab()
        {
            // 控制面板
            grpResultControl = new GroupBox
            {
                Text = "结果控制",
                Location = new Point(10, 10),
                Size = new Size(960, 80),
                Font = new Font("Microsoft YaHei", 9F)
            };

            btnGenerateReport = new Button
            {
                Text = "生成报告",
                Location = new Point(20, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightGreen
            };

            btnClearResults = new Button
            {
                Text = "清除结果",
                Location = new Point(140, 25),
                Size = new Size(100, 35),
                BackColor = Color.LightBlue
            };

            grpResultControl.Controls.AddRange(new Control[] {
                btnGenerateReport, btnClearResults
            });

            // 测试结果列表
            dgvTestResults = new DataGridView
            {
                Location = new Point(10, 100),
                Size = new Size(960, 300),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Microsoft YaHei", 8F)
            };

            // 设置测试结果列表列
            dgvTestResults.Columns.AddRange(new DataGridViewColumn[] {
                new DataGridViewTextBoxColumn { Name = "TestCaseId", HeaderText = "测试ID", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "TestCaseName", HeaderText = "测试名称", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "状态", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Duration", HeaderText = "耗时", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Message", HeaderText = "消息", Width = 250 }
            });

            // 测试摘要
            txtTestSummary = new TextBox
            {
                Location = new Point(10, 410),
                Size = new Size(960, 210),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 9F)
            };

            tabTestResults.Controls.AddRange(new Control[] { grpResultControl, dgvTestResults, txtTestSummary });
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            // 事件监控事件
            btnStartEventMonitor.Click += (s, e) => SafeInvokeAction(() =>
                EventMonitorActionRequested?.Invoke(this, new EventMonitorActionEventArgs("Start")));

            btnStopEventMonitor.Click += (s, e) => SafeInvokeAction(() =>
                EventMonitorActionRequested?.Invoke(this, new EventMonitorActionEventArgs("Stop")));

            btnClearEvents.Click += (s, e) => SafeInvokeAction(() =>
                EventMonitorActionRequested?.Invoke(this, new EventMonitorActionEventArgs("Clear")));

            // 测试执行事件
            btnRunAllTests.Click += (s, e) => SafeInvokeAction(() =>
                TestExecutionActionRequested?.Invoke(this, new TestExecutionActionEventArgs("RunAll", null)));

            btnRunCategoryTests.Click += (s, e) => SafeInvokeAction(() =>
            {
                var category = cmbTestCategory.SelectedItem?.ToString();
                TestExecutionActionRequested?.Invoke(this, new TestExecutionActionEventArgs("RunCategory", category));
            });

            btnStopTests.Click += (s, e) => SafeInvokeAction(() =>
                TestExecutionActionRequested?.Invoke(this, new TestExecutionActionEventArgs("Stop", null)));

            // 性能监控事件
            btnStartPerformanceMonitor.Click += (s, e) => SafeInvokeAction(() =>
                PerformanceMonitorActionRequested?.Invoke(this, new PerformanceMonitorActionEventArgs("Start")));

            btnStopPerformanceMonitor.Click += (s, e) => SafeInvokeAction(() =>
                PerformanceMonitorActionRequested?.Invoke(this, new PerformanceMonitorActionEventArgs("Stop")));

            btnClearPerformance.Click += (s, e) => SafeInvokeAction(() =>
                PerformanceMonitorActionRequested?.Invoke(this, new PerformanceMonitorActionEventArgs("Clear")));

            // 结果操作事件
            btnGenerateReport.Click += (s, e) => SafeInvokeAction(() =>
                ResultActionRequested?.Invoke(this, new ResultActionEventArgs("GenerateReport")));

            btnClearResults.Click += (s, e) => SafeInvokeAction(() =>
                ResultActionRequested?.Invoke(this, new ResultActionEventArgs("ClearResults")));
        }

        /// <summary>
        /// 安全执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        private void SafeInvokeAction(Action action)
        {
            try
            {
                action?.Invoke();
            }
            catch (Exception ex)
            {
                _logger?.Error("执行UI操作时发生错误", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 线程安全的UI更新
        /// </summary>
        /// <param name="action">UI更新操作</param>
        private void SafeUpdateUI(Action action)
        {
            if (InvokeRequired)
            {
                BeginInvoke(action);
            }
            else
            {
                try
                {
                    action?.Invoke();
                }
                catch (Exception ex)
                {
                    _logger?.Error("更新UI时发生错误", ex);
                }
            }
        }

        // UI更新方法
        /// <summary>
        /// 更新事件监控状态
        /// </summary>
        /// <param name="isMonitoring">是否正在监控</param>
        public void UpdateEventMonitoringStatus(bool isMonitoring)
        {
            SafeUpdateUI(() =>
            {
                btnStartEventMonitor.Enabled = !isMonitoring;
                btnStopEventMonitor.Enabled = isMonitoring;
                btnStartEventMonitor.BackColor = isMonitoring ? Color.LightGray : Color.LightGreen;
                btnStopEventMonitor.BackColor = isMonitoring ? Color.LightCoral : Color.LightGray;
            });
        }

        /// <summary>
        /// 更新性能监控状态
        /// </summary>
        /// <param name="isMonitoring">是否正在监控</param>
        public void UpdatePerformanceMonitoringStatus(bool isMonitoring)
        {
            SafeUpdateUI(() =>
            {
                btnStartPerformanceMonitor.Enabled = !isMonitoring;
                btnStopPerformanceMonitor.Enabled = isMonitoring;
                btnStartPerformanceMonitor.BackColor = isMonitoring ? Color.LightGray : Color.LightGreen;
                btnStopPerformanceMonitor.BackColor = isMonitoring ? Color.LightCoral : Color.LightGray;
            });
        }

        /// <summary>
        /// 更新测试执行状态
        /// </summary>
        /// <param name="isRunning">是否正在运行测试</param>
        public void UpdateTestExecutionStatus(bool isRunning)
        {
            SafeUpdateUI(() =>
            {
                btnRunAllTests.Enabled = !isRunning;
                btnRunCategoryTests.Enabled = !isRunning;
                btnStopTests.Enabled = isRunning;
                cmbTestCategory.Enabled = !isRunning;
            });
        }

        /// <summary>
        /// 更新测试进度
        /// </summary>
        /// <param name="progress">进度百分比</param>
        /// <param name="message">进度消息</param>
        public void UpdateTestProgress(int progress, string message)
        {
            SafeUpdateUI(() =>
            {
                progressBar.Value = Math.Max(0, Math.Min(100, progress));
                lblTestProgress.Text = message ?? "进行中...";
            });
        }

        /// <summary>
        /// 更新事件记录列表
        /// </summary>
        /// <param name="eventRecords">事件记录列表</param>
        public void UpdateEventRecords(List<EventRecord> eventRecords)
        {
            SafeUpdateUI(() =>
            {
                dgvEvents.Rows.Clear();
                if (eventRecords != null)
                {
                    var startIndex = Math.Max(0, eventRecords.Count - 100);
                    var recentRecords = eventRecords.Skip(startIndex);
                    foreach (var record in recentRecords) // 只显示最近100条
                    {
                        var row = new DataGridViewRow();
                        row.CreateCells(dgvEvents);
                        row.Cells[0].Value = record.Timestamp.ToString("HH:mm:ss.fff");
                        row.Cells[1].Value = record.EventType;
                        row.Cells[2].Value = record.Source;
                        row.Cells[3].Value = GetEventDescription(record);
                        dgvEvents.Rows.Add(row);
                    }

                    // 滚动到最后一行
                    if (dgvEvents.Rows.Count > 0)
                    {
                        dgvEvents.FirstDisplayedScrollingRowIndex = dgvEvents.Rows.Count - 1;
                    }
                }
            });
        }

        /// <summary>
        /// 更新事件统计信息
        /// </summary>
        /// <param name="statistics">事件统计信息</param>
        public void UpdateEventStatistics(EventStatistics statistics)
        {
            SafeUpdateUI(() =>
            {
                if (statistics != null)
                {
                    lblEventStats.Text = $"事件统计: 总数={statistics.TotalEventCount}, " +
                                        $"最近1小时={statistics.LastHourEventCount}, " +
                                        $"最近1分钟={statistics.LastMinuteEventCount}";
                }
            });
        }

        /// <summary>
        /// 更新测试用例列表
        /// </summary>
        /// <param name="testCases">测试用例列表</param>
        public void UpdateTestCases(List<TestCase> testCases)
        {
            SafeUpdateUI(() =>
            {
                dgvTestCases.Rows.Clear();
                if (testCases != null)
                {
                    foreach (var testCase in testCases)
                    {
                        var row = new DataGridViewRow();
                        row.CreateCells(dgvTestCases);
                        row.Cells[0].Value = testCase.Id;
                        row.Cells[1].Value = testCase.Name;
                        row.Cells[2].Value = testCase.Category;
                        row.Cells[3].Value = testCase.Description;
                        dgvTestCases.Rows.Add(row);
                    }
                }
            });
        }

        /// <summary>
        /// 更新测试分类下拉框
        /// </summary>
        /// <param name="categories">分类列表</param>
        public void UpdateTestCategories(List<string> categories)
        {
            SafeUpdateUI(() =>
            {
                cmbTestCategory.Items.Clear();
                if (categories != null)
                {
                    cmbTestCategory.Items.AddRange(categories.ToArray());
                    if (cmbTestCategory.Items.Count > 0)
                    {
                        cmbTestCategory.SelectedIndex = 0;
                    }
                }
            });
        }

        /// <summary>
        /// 更新测试结果列表
        /// </summary>
        /// <param name="testResults">测试结果列表</param>
        public void UpdateTestResults(List<TestResult> testResults)
        {
            SafeUpdateUI(() =>
            {
                dgvTestResults.Rows.Clear();
                if (testResults != null)
                {
                    foreach (var result in testResults)
                    {
                        var row = new DataGridViewRow();
                        row.CreateCells(dgvTestResults);
                        row.Cells[0].Value = result.TestCaseId;
                        row.Cells[1].Value = result.TestCaseName;
                        row.Cells[2].Value = result.Status.ToString();
                        row.Cells[3].Value = $"{result.Duration.TotalMilliseconds:F0}ms";
                        row.Cells[4].Value = result.Message;

                        // 根据状态设置行颜色
                        switch (result.Status)
                        {
                            case TestStatus.Passed:
                                row.DefaultCellStyle.BackColor = Color.LightGreen;
                                break;
                            case TestStatus.Failed:
                                row.DefaultCellStyle.BackColor = Color.LightCoral;
                                break;
                            case TestStatus.Error:
                                row.DefaultCellStyle.BackColor = Color.Orange;
                                break;
                            case TestStatus.Cancelled:
                                row.DefaultCellStyle.BackColor = Color.LightGray;
                                break;
                        }

                        dgvTestResults.Rows.Add(row);
                    }
                }
            });
        }

        /// <summary>
        /// 更新性能数据列表
        /// </summary>
        /// <param name="snapshots">性能快照列表</param>
        public void UpdatePerformanceData(List<PerformanceSnapshot> snapshots)
        {
            SafeUpdateUI(() =>
            {
                dgvPerformance.Rows.Clear();
                if (snapshots != null)
                {
                    var startIndex = Math.Max(0, snapshots.Count - 50);
                    var recentSnapshots = snapshots.Skip(startIndex);
                    foreach (var snapshot in recentSnapshots) // 只显示最近50条
                    {
                        var row = new DataGridViewRow();
                        row.CreateCells(dgvPerformance);
                        row.Cells[0].Value = snapshot.Timestamp.ToString("HH:mm:ss");
                        row.Cells[1].Value = $"{snapshot.CpuUsage:F1}";
                        row.Cells[2].Value = $"{snapshot.MemoryUsage / 1024.0 / 1024.0:F1}";
                        row.Cells[3].Value = snapshot.ThreadCount.ToString();
                        row.Cells[4].Value = snapshot.HandleCount.ToString();
                        dgvPerformance.Rows.Add(row);
                    }

                    // 滚动到最后一行
                    if (dgvPerformance.Rows.Count > 0)
                    {
                        dgvPerformance.FirstDisplayedScrollingRowIndex = dgvPerformance.Rows.Count - 1;
                    }
                }
            });
        }

        /// <summary>
        /// 更新性能统计信息
        /// </summary>
        /// <param name="statistics">性能统计信息</param>
        public void UpdatePerformanceStatistics(PerformanceStatistics statistics)
        {
            SafeUpdateUI(() =>
            {
                if (statistics != null)
                {
                    lblPerformanceStats.Text = $"性能统计: CPU={statistics.CurrentCpuUsage:F1}%, " +
                                             $"内存={statistics.CurrentMemoryUsage / 1024.0 / 1024.0:F1}MB, " +
                                             $"线程={statistics.CurrentThreadCount}";
                }
            });
        }

        /// <summary>
        /// 更新测试摘要
        /// </summary>
        /// <param name="summary">测试摘要</param>
        public void UpdateTestSummary(TestSummary summary)
        {
            SafeUpdateUI(() =>
            {
                if (summary != null)
                {
                    var summaryText = $"测试摘要报告\r\n" +
                                    $"==========================================\r\n" +
                                    $"总测试数: {summary.TotalTests}\r\n" +
                                    $"通过: {summary.PassedTests} ({summary.SuccessRate:F1}%)\r\n" +
                                    $"失败: {summary.FailedTests}\r\n" +
                                    $"错误: {summary.ErrorTests}\r\n" +
                                    $"取消: {summary.CancelledTests}\r\n" +
                                    $"总耗时: {summary.TotalDuration.TotalSeconds:F2}秒\r\n" +
                                    $"==========================================\r\n";

                    if (summary.TestResults != null)
                    {
                        summaryText += "\r\n详细结果:\r\n";
                        foreach (var result in summary.TestResults)
                        {
                            summaryText += $"[{result.Status}] {result.TestCaseName} - {result.Duration.TotalMilliseconds:F0}ms\r\n";
                            if (!string.IsNullOrEmpty(result.Message) && result.Status != TestStatus.Passed)
                            {
                                summaryText += $"    {result.Message}\r\n";
                            }
                        }
                    }

                    txtTestSummary.Text = summaryText;
                }
            });
        }

        /// <summary>
        /// 获取事件描述
        /// </summary>
        /// <param name="record">事件记录</param>
        /// <returns>事件描述</returns>
        private string GetEventDescription(EventRecord record)
        {
            if (record.EventData == null) return "无数据";

            try
            {
                switch (record.EventType)
                {
                    case "DeviceDataUpdateEvent":
                        return "设备数据更新";
                    case "DeviceConnectionEvent":
                        return "设备连接状态变化";
                    case "AlarmEvent":
                        return "报警事件";
                    case "SystemStartupEvent":
                        return "系统启动";
                    case "SystemShutdownEvent":
                        return "系统关闭";
                    default:
                        return record.EventType;
                }
            }
            catch
            {
                return "解析失败";
            }
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">消息标题</param>
        /// <param name="icon">消息图标</param>
        public void ShowMessage(string message, string title = "信息", MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
            });
        }

        // IView接口方法实现
        /// <summary>
        /// 显示消息（IView接口方法）
        /// </summary>
        /// <param name="message">消息内容</param>
        public void ShowMessage(string message)
        {
            ShowMessage(message, "信息", MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示错误消息（IView接口方法）
        /// </summary>
        /// <param name="message">错误消息</param>
        public void ShowError(string message)
        {
            ShowMessage(message, "错误", MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示警告消息（IView接口方法）
        /// </summary>
        /// <param name="message">警告消息</param>
        public void ShowWarning(string message)
        {
            ShowMessage(message, "警告", MessageBoxIcon.Warning);
        }

        /// <summary>
        /// 设置加载状态（IView接口方法）
        /// </summary>
        /// <param name="isLoading">是否正在加载</param>
        /// <param name="message">加载消息</param>
        public void SetLoadingState(bool isLoading, string message = null)
        {
            // 可以在这里实现加载状态的UI更新
            if (isLoading)
            {
                UpdateTestProgress(0, message ?? "加载中...");
            }
        }

        /// <summary>
        /// 刷新数据（IView接口方法）
        /// </summary>
        public void RefreshData()
        {
            // 刷新所有数据显示
            // 这个方法通常由表示器调用来更新视图
        }
    }

    /// <summary>
    /// 事件监控操作事件参数
    /// </summary>
    public class EventMonitorActionEventArgs : EventArgs
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Action { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="action">操作类型</param>
        public EventMonitorActionEventArgs(string action)
        {
            Action = action;
        }
    }

    /// <summary>
    /// 测试执行操作事件参数
    /// </summary>
    public class TestExecutionActionEventArgs : EventArgs
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Action { get; }

        /// <summary>
        /// 参数
        /// </summary>
        public string Parameter { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="action">操作类型</param>
        /// <param name="parameter">参数</param>
        public TestExecutionActionEventArgs(string action, string parameter)
        {
            Action = action;
            Parameter = parameter;
        }
    }

    /// <summary>
    /// 性能监控操作事件参数
    /// </summary>
    public class PerformanceMonitorActionEventArgs : EventArgs
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Action { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="action">操作类型</param>
        public PerformanceMonitorActionEventArgs(string action)
        {
            Action = action;
        }
    }

    /// <summary>
    /// 结果操作事件参数
    /// </summary>
    public class ResultActionEventArgs : EventArgs
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Action { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="action">操作类型</param>
        public ResultActionEventArgs(string action)
        {
            Action = action;
        }
    }
}
