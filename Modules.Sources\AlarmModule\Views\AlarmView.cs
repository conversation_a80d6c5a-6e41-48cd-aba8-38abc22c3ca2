using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Contracts.Services;
using AlarmModule.Models;

namespace AlarmModule.Views
{
    /// <summary>
    /// 报警管理视图
    /// </summary>
    /// <remarks>
    /// 提供报警列表显示、确认操作和统计信息的用户界面
    /// </remarks>
    public partial class AlarmView : UserControl, IView
    {
        private SplitContainer _mainSplitContainer;
        private SplitContainer _leftSplitContainer;
        private DataGridView _activeAlarmsDataGridView;
        private DataGridView _alarmHistoryDataGridView;
        private Panel _controlPanel;
        private Panel _statisticsPanel;
        private Button _refreshButton;
        private Button _acknowledgeButton;
        private Button _acknowledgeAllButton;
        private Button _clearButton;
        private Button _clearAcknowledgedButton;
        private Label _statusLabel;
        private GroupBox _statisticsGroupBox;
        private Label _activeCountLabel;
        private Label _todayCountLabel;
        private Label _weekCountLabel;
        private Label _monthCountLabel;

        /// <summary>
        /// 刷新请求事件
        /// </summary>
        public event EventHandler RefreshRequested;

        /// <summary>
        /// 确认报警事件
        /// </summary>
        public event EventHandler<AlarmActionEventArgs> AcknowledgeAlarmRequested;

        /// <summary>
        /// 确认所有报警事件
        /// </summary>
        public event EventHandler AcknowledgeAllAlarmsRequested;

        /// <summary>
        /// 清除报警事件
        /// </summary>
        public event EventHandler<AlarmActionEventArgs> ClearAlarmRequested;

        /// <summary>
        /// 清除已确认报警事件
        /// </summary>
        public event EventHandler ClearAcknowledgedAlarmsRequested;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AlarmView()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            SuspendLayout();

            // 设置主控件属性
            Name = "AlarmView";
            Size = new Size(1000, 700);
            BackColor = Color.White;

            // 创建主分割容器
            CreateMainSplitContainer();

            // 创建左侧分割容器（报警列表）
            CreateLeftSplitContainer();

            // 创建活动报警列表
            CreateActiveAlarmsDataGridView();

            // 创建历史报警列表
            CreateAlarmHistoryDataGridView();

            // 创建控制面板
            CreateControlPanel();

            // 创建统计面板
            CreateStatisticsPanel();

            ResumeLayout(false);
        }

        /// <summary>
        /// 创建主分割容器
        /// </summary>
        private void CreateMainSplitContainer()
        {
            _mainSplitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Vertical,
                SplitterDistance = 750,
                FixedPanel = FixedPanel.Panel2
            };

            Controls.Add(_mainSplitContainer);
        }

        /// <summary>
        /// 创建左侧分割容器
        /// </summary>
        private void CreateLeftSplitContainer()
        {
            _leftSplitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 350
            };

            _mainSplitContainer.Panel1.Controls.Add(_leftSplitContainer);
        }

        /// <summary>
        /// 创建活动报警数据表格
        /// </summary>
        private void CreateActiveAlarmsDataGridView()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var label = new Label
            {
                Text = "当前活动报警",
                Dock = DockStyle.Top,
                Height = 25,
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold),
                ForeColor = Color.DarkRed
            };

            _activeAlarmsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };

            // 添加列
            _activeAlarmsDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "报警时间",
                    DataPropertyName = "Timestamp",
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm:ss" },
                    FillWeight = 20
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DeviceId",
                    HeaderText = "设备ID",
                    DataPropertyName = "DeviceId",
                    FillWeight = 15
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AlarmType",
                    HeaderText = "报警类型",
                    DataPropertyName = "AlarmType",
                    FillWeight = 15
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Level",
                    HeaderText = "级别",
                    DataPropertyName = "Level",
                    FillWeight = 10
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Message",
                    HeaderText = "报警消息",
                    DataPropertyName = "Message",
                    FillWeight = 30
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Status",
                    HeaderText = "状态",
                    DataPropertyName = "Status",
                    FillWeight = 10
                }
            });

            panel.Controls.Add(_activeAlarmsDataGridView);
            panel.Controls.Add(label);
            _leftSplitContainer.Panel1.Controls.Add(panel);
        }

        /// <summary>
        /// 创建历史报警数据表格
        /// </summary>
        private void CreateAlarmHistoryDataGridView()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            var label = new Label
            {
                Text = "历史报警记录",
                Dock = DockStyle.Top,
                Height = 25,
                Font = new Font("Microsoft YaHei", 10, FontStyle.Bold),
                ForeColor = Color.DarkBlue
            };

            _alarmHistoryDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };

            // 添加列
            _alarmHistoryDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "报警时间",
                    DataPropertyName = "Timestamp",
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm:ss" },
                    FillWeight = 18
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DeviceId",
                    HeaderText = "设备ID",
                    DataPropertyName = "DeviceId",
                    FillWeight = 12
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AlarmType",
                    HeaderText = "报警类型",
                    DataPropertyName = "AlarmType",
                    FillWeight = 12
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Level",
                    HeaderText = "级别",
                    DataPropertyName = "Level",
                    FillWeight = 8
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Message",
                    HeaderText = "报警消息",
                    DataPropertyName = "Message",
                    FillWeight = 25
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AcknowledgedBy",
                    HeaderText = "确认人",
                    DataPropertyName = "AcknowledgedBy",
                    FillWeight = 10
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AcknowledgedTime",
                    HeaderText = "确认时间",
                    DataPropertyName = "AcknowledgedTime",
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm:ss" },
                    FillWeight = 15
                }
            });

            panel.Controls.Add(_alarmHistoryDataGridView);
            panel.Controls.Add(label);
            _leftSplitContainer.Panel2.Controls.Add(panel);
        }

        /// <summary>
        /// 创建控制面板
        /// </summary>
        private void CreateControlPanel()
        {
            _controlPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                BackColor = Color.LightGray,
                Padding = new Padding(10)
            };

            var groupBox = new GroupBox
            {
                Text = "报警操作",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9, FontStyle.Bold)
            };

            // 刷新按钮
            _refreshButton = new Button
            {
                Text = "刷新列表",
                Size = new Size(80, 30),
                Location = new Point(15, 25),
                UseVisualStyleBackColor = true
            };

            // 确认报警按钮
            _acknowledgeButton = new Button
            {
                Text = "确认报警",
                Size = new Size(80, 30),
                Location = new Point(105, 25),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen
            };

            // 确认所有按钮
            _acknowledgeAllButton = new Button
            {
                Text = "确认所有",
                Size = new Size(80, 30),
                Location = new Point(195, 25),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen
            };

            // 清除报警按钮
            _clearButton = new Button
            {
                Text = "清除报警",
                Size = new Size(80, 30),
                Location = new Point(285, 25),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightCoral
            };

            // 清除已确认按钮
            _clearAcknowledgedButton = new Button
            {
                Text = "清除已确认",
                Size = new Size(90, 30),
                Location = new Point(375, 25),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightCoral
            };

            // 状态标签
            _statusLabel = new Label
            {
                Text = "就绪",
                AutoSize = false,
                Size = new Size(400, 20),
                Location = new Point(15, 65),
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Microsoft YaHei", 9)
            };

            groupBox.Controls.AddRange(new Control[]
            {
                _refreshButton, _acknowledgeButton, _acknowledgeAllButton,
                _clearButton, _clearAcknowledgedButton, _statusLabel
            });

            _controlPanel.Controls.Add(groupBox);
            _mainSplitContainer.Panel2.Controls.Add(_controlPanel);
        }

        /// <summary>
        /// 创建统计面板
        /// </summary>
        private void CreateStatisticsPanel()
        {
            _statisticsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.WhiteSmoke,
                Padding = new Padding(10)
            };

            _statisticsGroupBox = new GroupBox
            {
                Text = "报警统计",
                Dock = DockStyle.Fill,
                Font = new Font("Microsoft YaHei", 9, FontStyle.Bold)
            };

            _activeCountLabel = new Label
            {
                Text = "当前活动: 0",
                Location = new Point(15, 25),
                Size = new Size(100, 20),
                Font = new Font("Microsoft YaHei", 9),
                ForeColor = Color.Red
            };

            _todayCountLabel = new Label
            {
                Text = "今日报警: 0",
                Location = new Point(125, 25),
                Size = new Size(100, 20),
                Font = new Font("Microsoft YaHei", 9)
            };

            _weekCountLabel = new Label
            {
                Text = "本周报警: 0",
                Location = new Point(15, 50),
                Size = new Size(100, 20),
                Font = new Font("Microsoft YaHei", 9)
            };

            _monthCountLabel = new Label
            {
                Text = "本月报警: 0",
                Location = new Point(125, 50),
                Size = new Size(100, 20),
                Font = new Font("Microsoft YaHei", 9)
            };

            _statisticsGroupBox.Controls.AddRange(new Control[]
            {
                _activeCountLabel, _todayCountLabel, _weekCountLabel, _monthCountLabel
            });

            _statisticsPanel.Controls.Add(_statisticsGroupBox);
            _mainSplitContainer.Panel2.Controls.Add(_statisticsPanel);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            _refreshButton.Click += (s, e) => RefreshRequested?.Invoke(this, EventArgs.Empty);
            _acknowledgeButton.Click += OnAcknowledgeButtonClick;
            _acknowledgeAllButton.Click += (s, e) => AcknowledgeAllAlarmsRequested?.Invoke(this, EventArgs.Empty);
            _clearButton.Click += OnClearButtonClick;
            _clearAcknowledgedButton.Click += (s, e) => ClearAcknowledgedAlarmsRequested?.Invoke(this, EventArgs.Empty);

            _activeAlarmsDataGridView.SelectionChanged += OnActiveAlarmsSelectionChanged;
        }

        /// <summary>
        /// 确认按钮点击事件处理
        /// </summary>
        private void OnAcknowledgeButtonClick(object sender, EventArgs e)
        {
            if (_activeAlarmsDataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = _activeAlarmsDataGridView.SelectedRows[0];
                var alarm = selectedRow.DataBoundItem as AlarmViewModel;
                if (alarm != null)
                {
                    AcknowledgeAlarmRequested?.Invoke(this, new AlarmActionEventArgs(alarm.Id));
                }
            }
        }

        /// <summary>
        /// 清除按钮点击事件处理
        /// </summary>
        private void OnClearButtonClick(object sender, EventArgs e)
        {
            if (_activeAlarmsDataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = _activeAlarmsDataGridView.SelectedRows[0];
                var alarm = selectedRow.DataBoundItem as AlarmViewModel;
                if (alarm != null)
                {
                    ClearAlarmRequested?.Invoke(this, new AlarmActionEventArgs(alarm.Id));
                }
            }
        }

        /// <summary>
        /// 活动报警选择变化事件处理
        /// </summary>
        private void OnActiveAlarmsSelectionChanged(object sender, EventArgs e)
        {
            var hasSelection = _activeAlarmsDataGridView.SelectedRows.Count > 0;
            _acknowledgeButton.Enabled = hasSelection;
            _clearButton.Enabled = hasSelection;
        }

        /// <summary>
        /// 显示活动报警列表
        /// </summary>
        /// <param name="alarms">报警列表</param>
        public void ShowActiveAlarms(List<AlarmViewModel> alarms)
        {
            SafeUpdateUI(() =>
            {
                _activeAlarmsDataGridView.DataSource = null;
                _activeAlarmsDataGridView.DataSource = alarms;

                // 设置报警级别的颜色
                foreach (DataGridViewRow row in _activeAlarmsDataGridView.Rows)
                {
                    var alarm = row.DataBoundItem as AlarmViewModel;
                    if (alarm != null)
                    {
                        SetAlarmRowColor(row, alarm.Level, alarm.Status);
                    }
                }

                UpdateStatus($"显示 {alarms.Count} 个活动报警");
            });
        }

        /// <summary>
        /// 显示历史报警列表
        /// </summary>
        /// <param name="alarms">报警列表</param>
        public void ShowAlarmHistory(List<AlarmViewModel> alarms)
        {
            SafeUpdateUI(() =>
            {
                _alarmHistoryDataGridView.DataSource = null;
                _alarmHistoryDataGridView.DataSource = alarms;

                // 设置报警级别的颜色
                foreach (DataGridViewRow row in _alarmHistoryDataGridView.Rows)
                {
                    var alarm = row.DataBoundItem as AlarmViewModel;
                    if (alarm != null)
                    {
                        SetAlarmRowColor(row, alarm.Level, alarm.Status);
                    }
                }
            });
        }

        /// <summary>
        /// 设置报警行颜色
        /// </summary>
        /// <param name="row">数据行</param>
        /// <param name="level">报警级别</param>
        /// <param name="status">报警状态</param>
        private void SetAlarmRowColor(DataGridViewRow row, AlarmLevel level, AlarmStatus status)
        {
            Color backColor = Color.White;
            Color foreColor = Color.Black;

            // 根据状态设置基础颜色
            switch (status)
            {
                case AlarmStatus.Active:
                    // 根据级别设置颜色
                    switch (level)
                    {
                        case AlarmLevel.Critical:
                            backColor = Color.MistyRose;
                            foreColor = Color.DarkRed;
                            break;
                        case AlarmLevel.Error:
                            backColor = Color.LightPink;
                            foreColor = Color.Red;
                            break;
                        case AlarmLevel.Warning:
                            backColor = Color.LightYellow;
                            foreColor = Color.DarkOrange;
                            break;
                        case AlarmLevel.Info:
                            backColor = Color.LightBlue;
                            foreColor = Color.DarkBlue;
                            break;
                    }
                    break;
                case AlarmStatus.Acknowledged:
                    backColor = Color.LightGray;
                    foreColor = Color.DarkGray;
                    break;
                case AlarmStatus.Cleared:
                    backColor = Color.White;
                    foreColor = Color.Gray;
                    break;
            }

            row.DefaultCellStyle.BackColor = backColor;
            row.DefaultCellStyle.ForeColor = foreColor;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        /// <param name="statistics">统计信息</param>
        public void UpdateStatistics(AlarmStatistics statistics)
        {
            SafeUpdateUI(() =>
            {
                if (statistics != null)
                {
                    _activeCountLabel.Text = $"当前活动: {statistics.ActiveAlarmCount}";
                    _todayCountLabel.Text = $"今日报警: {statistics.TodayAlarmCount}";
                    _weekCountLabel.Text = $"本周报警: {statistics.WeekAlarmCount}";
                    _monthCountLabel.Text = $"本月报警: {statistics.MonthAlarmCount}";

                    // 设置活动报警数量的颜色
                    _activeCountLabel.ForeColor = statistics.ActiveAlarmCount > 0 ? Color.Red : Color.Green;
                }
            });
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        /// <param name="message">状态消息</param>
        public void UpdateStatus(string message)
        {
            SafeUpdateUI(() =>
            {
                _statusLabel.Text = message;
            });
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="message">错误消息</param>
        public void ShowError(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"错误: {message}");
            });
        }

        /// <summary>
        /// 显示消息（IView接口要求）
        /// </summary>
        /// <param name="message">消息内容</param>
        public void ShowMessage(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                UpdateStatus(message);
            });
        }

        /// <summary>
        /// 显示警告（IView接口要求）
        /// </summary>
        /// <param name="message">警告消息</param>
        public void ShowWarning(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                UpdateStatus($"警告: {message}");
            });
        }

        /// <summary>
        /// 设置加载状态（IView接口要求）
        /// </summary>
        /// <param name="isLoading">是否正在加载</param>
        /// <param name="message">加载消息</param>
        public void SetLoadingState(bool isLoading, string message = "")
        {
            SafeUpdateUI(() =>
            {
                _refreshButton.Enabled = !isLoading;
                if (!string.IsNullOrEmpty(message))
                {
                    UpdateStatus(message);
                }
            });
        }

        /// <summary>
        /// 刷新数据（IView接口要求）
        /// </summary>
        public void RefreshData()
        {
            RefreshRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 线程安全的UI更新
        /// </summary>
        /// <param name="action">更新操作</param>
        private void SafeUpdateUI(Action action)
        {
            if (InvokeRequired)
            {
                Invoke(action);
            }
            else
            {
                action();
            }
        }
    }

    /// <summary>
    /// 报警操作事件参数
    /// </summary>
    public class AlarmActionEventArgs : EventArgs
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        public string AlarmId { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        public AlarmActionEventArgs(string alarmId)
        {
            AlarmId = alarmId;
        }
    }
}
