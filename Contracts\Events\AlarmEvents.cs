using System;

namespace Contracts.Events
{
    /// <summary>
    /// 报警事件
    /// </summary>
    /// <remarks>
    /// 当系统产生报警时发布此事件
    /// </remarks>
    public class AlarmEvent
    {
        /// <summary>
        /// 报警信息
        /// </summary>
        public AlarmInfo AlarmInfo { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        public AlarmEvent(AlarmInfo alarmInfo)
        {
            AlarmInfo = alarmInfo;
        }
    }

    /// <summary>
    /// 报警信息
    /// </summary>
    public class AlarmInfo
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        public string AlarmId { get; set; }

        /// <summary>
        /// 报警名称
        /// </summary>
        public string AlarmName { get; set; }

        /// <summary>
        /// 报警描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 报警级别
        /// </summary>
        public AlarmLevel Level { get; set; }

        /// <summary>
        /// 报警状态
        /// </summary>
        public AlarmStatus Status { get; set; }

        /// <summary>
        /// 报警源（设备ID或模块名称）
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 报警发生时间
        /// </summary>
        public DateTime OccurredTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 报警确认时间
        /// </summary>
        public DateTime? AcknowledgedTime { get; set; }

        /// <summary>
        /// 报警恢复时间
        /// </summary>
        public DateTime? RecoveredTime { get; set; }

        /// <summary>
        /// 确认用户
        /// </summary>
        public string AcknowledgedBy { get; set; }

        /// <summary>
        /// 报警值
        /// </summary>
        public object AlarmValue { get; set; }

        /// <summary>
        /// 报警限值
        /// </summary>
        public object LimitValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="alarmName">报警名称</param>
        /// <param name="description">报警描述</param>
        /// <param name="level">报警级别</param>
        /// <param name="source">报警源</param>
        public AlarmInfo(string alarmId, string alarmName, string description, AlarmLevel level, string source)
        {
            AlarmId = alarmId;
            AlarmName = alarmName;
            Description = description;
            Level = level;
            Source = source;
            Status = AlarmStatus.Active;
        }
    }

    /// <summary>
    /// 报警级别枚举
    /// </summary>
    public enum AlarmLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,

        /// <summary>
        /// 严重
        /// </summary>
        Critical = 3,

        /// <summary>
        /// 致命
        /// </summary>
        Fatal = 4
    }

    /// <summary>
    /// 报警状态枚举
    /// </summary>
    public enum AlarmStatus
    {
        /// <summary>
        /// 活动（未确认）
        /// </summary>
        Active,

        /// <summary>
        /// 已确认
        /// </summary>
        Acknowledged,

        /// <summary>
        /// 已恢复
        /// </summary>
        Recovered,

        /// <summary>
        /// 已清除
        /// </summary>
        Cleared
    }

    /// <summary>
    /// 报警确认事件
    /// </summary>
    /// <remarks>
    /// 当报警被确认时发布此事件
    /// </remarks>
    public class AlarmAcknowledgedEvent
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        public string AlarmId { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime AcknowledgedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 确认用户
        /// </summary>
        public string AcknowledgedBy { get; set; }

        /// <summary>
        /// 确认备注
        /// </summary>
        public string Comment { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="acknowledgedBy">确认用户</param>
        /// <param name="comment">确认备注</param>
        public AlarmAcknowledgedEvent(string alarmId, string acknowledgedBy, string comment = "")
        {
            AlarmId = alarmId;
            AcknowledgedBy = acknowledgedBy;
            Comment = comment;
        }
    }

    /// <summary>
    /// 报警清除事件
    /// </summary>
    /// <remarks>
    /// 当报警被清除时发布此事件
    /// </remarks>
    public class AlarmClearedEvent
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        public string AlarmId { get; set; }

        /// <summary>
        /// 清除时间
        /// </summary>
        public DateTime ClearedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 清除原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="reason">清除原因</param>
        public AlarmClearedEvent(string alarmId, string reason = "")
        {
            AlarmId = alarmId;
            Reason = reason;
        }
    }
}
