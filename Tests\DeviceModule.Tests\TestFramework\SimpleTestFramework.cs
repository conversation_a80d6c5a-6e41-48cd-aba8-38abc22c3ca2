using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;

namespace DeviceModule.Tests.TestFramework
{
    /// <summary>
    /// 简单的测试框架
    /// </summary>
    /// <remarks>
    /// 提供基本的单元测试功能，不依赖外部测试框架
    /// </remarks>
    public static class SimpleTestFramework
    {
        /// <summary>
        /// 测试方法特性
        /// </summary>
        [AttributeUsage(AttributeTargets.Method)]
        public class TestMethodAttribute : Attribute
        {
            /// <summary>
            /// 测试名称
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// 测试描述
            /// </summary>
            public string Description { get; set; }

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="name">测试名称</param>
            /// <param name="description">测试描述</param>
            public TestMethodAttribute(string name = null, string description = null)
            {
                Name = name;
                Description = description;
            }
        }

        /// <summary>
        /// 测试类特性
        /// </summary>
        [AttributeUsage(AttributeTargets.Class)]
        public class TestClassAttribute : Attribute
        {
            /// <summary>
            /// 测试类名称
            /// </summary>
            public string Name { get; set; }

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="name">测试类名称</param>
            public TestClassAttribute(string name = null)
            {
                Name = name;
            }
        }

        /// <summary>
        /// 测试初始化特性
        /// </summary>
        [AttributeUsage(AttributeTargets.Method)]
        public class TestInitializeAttribute : Attribute { }

        /// <summary>
        /// 测试清理特性
        /// </summary>
        [AttributeUsage(AttributeTargets.Method)]
        public class TestCleanupAttribute : Attribute { }

        /// <summary>
        /// 断言类
        /// </summary>
        public static class Assert
        {
            /// <summary>
            /// 断言为真
            /// </summary>
            /// <param name="condition">条件</param>
            /// <param name="message">错误消息</param>
            public static void IsTrue(bool condition, string message = null)
            {
                if (!condition)
                {
                    throw new AssertFailedException(message ?? "断言失败: 期望为真，实际为假");
                }
            }

            /// <summary>
            /// 断言为假
            /// </summary>
            /// <param name="condition">条件</param>
            /// <param name="message">错误消息</param>
            public static void IsFalse(bool condition, string message = null)
            {
                if (condition)
                {
                    throw new AssertFailedException(message ?? "断言失败: 期望为假，实际为真");
                }
            }

            /// <summary>
            /// 断言相等
            /// </summary>
            /// <param name="expected">期望值</param>
            /// <param name="actual">实际值</param>
            /// <param name="message">错误消息</param>
            public static void AreEqual<T>(T expected, T actual, string message = null)
            {
                if (!Equals(expected, actual))
                {
                    throw new AssertFailedException(message ?? $"断言失败: 期望值={expected}, 实际值={actual}");
                }
            }

            /// <summary>
            /// 断言不相等
            /// </summary>
            /// <param name="notExpected">不期望的值</param>
            /// <param name="actual">实际值</param>
            /// <param name="message">错误消息</param>
            public static void AreNotEqual<T>(T notExpected, T actual, string message = null)
            {
                if (Equals(notExpected, actual))
                {
                    throw new AssertFailedException(message ?? $"断言失败: 不期望值={notExpected}, 实际值={actual}");
                }
            }

            /// <summary>
            /// 断言为空
            /// </summary>
            /// <param name="value">值</param>
            /// <param name="message">错误消息</param>
            public static void IsNull(object value, string message = null)
            {
                if (value != null)
                {
                    throw new AssertFailedException(message ?? "断言失败: 期望为null，实际不为null");
                }
            }

            /// <summary>
            /// 断言不为空
            /// </summary>
            /// <param name="value">值</param>
            /// <param name="message">错误消息</param>
            public static void IsNotNull(object value, string message = null)
            {
                if (value == null)
                {
                    throw new AssertFailedException(message ?? "断言失败: 期望不为null，实际为null");
                }
            }

            /// <summary>
            /// 断言抛出异常
            /// </summary>
            /// <typeparam name="T">异常类型</typeparam>
            /// <param name="action">操作</param>
            /// <param name="message">错误消息</param>
            public static void ThrowsException<T>(Action action, string message = null) where T : Exception
            {
                try
                {
                    action();
                    throw new AssertFailedException(message ?? $"断言失败: 期望抛出{typeof(T).Name}异常，但没有抛出异常");
                }
                catch (T)
                {
                    // 期望的异常，测试通过
                }
                catch (Exception ex)
                {
                    throw new AssertFailedException(message ?? $"断言失败: 期望抛出{typeof(T).Name}异常，实际抛出{ex.GetType().Name}异常");
                }
            }

            /// <summary>
            /// 断言引用相同
            /// </summary>
            /// <param name="expected">期望的对象</param>
            /// <param name="actual">实际的对象</param>
            /// <param name="message">错误消息</param>
            public static void AreSame(object expected, object actual, string message = null)
            {
                if (!ReferenceEquals(expected, actual))
                {
                    throw new AssertFailedException(message ?? "断言失败: 期望引用相同，实际引用不同");
                }
            }

            /// <summary>
            /// 断言引用不同
            /// </summary>
            /// <param name="notExpected">不期望的对象</param>
            /// <param name="actual">实际的对象</param>
            /// <param name="message">错误消息</param>
            public static void AreNotSame(object notExpected, object actual, string message = null)
            {
                if (ReferenceEquals(notExpected, actual))
                {
                    throw new AssertFailedException(message ?? "断言失败: 期望引用不同，实际引用相同");
                }
            }
        }

        /// <summary>
        /// 断言失败异常
        /// </summary>
        public class AssertFailedException : Exception
        {
            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="message">错误消息</param>
            public AssertFailedException(string message) : base(message) { }

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="message">错误消息</param>
            /// <param name="innerException">内部异常</param>
            public AssertFailedException(string message, Exception innerException) : base(message, innerException) { }
        }

        /// <summary>
        /// 测试结果
        /// </summary>
        public class TestResult
        {
            /// <summary>
            /// 测试名称
            /// </summary>
            public string TestName { get; set; }

            /// <summary>
            /// 测试类名
            /// </summary>
            public string TestClass { get; set; }

            /// <summary>
            /// 是否通过
            /// </summary>
            public bool Passed { get; set; }

            /// <summary>
            /// 错误消息
            /// </summary>
            public string ErrorMessage { get; set; }

            /// <summary>
            /// 异常信息
            /// </summary>
            public Exception Exception { get; set; }

            /// <summary>
            /// 执行时间
            /// </summary>
            public TimeSpan Duration { get; set; }
        }

        /// <summary>
        /// 运行测试类
        /// </summary>
        /// <param name="testClass">测试类类型</param>
        /// <returns>测试结果列表</returns>
        public static List<TestResult> RunTestClass(Type testClass)
        {
            var results = new List<TestResult>();
            var instance = Activator.CreateInstance(testClass);

            // 获取测试方法
            var testMethods = testClass.GetMethods(BindingFlags.Public | BindingFlags.Instance);
            var initializeMethod = GetMethodWithAttribute<TestInitializeAttribute>(testClass);
            var cleanupMethod = GetMethodWithAttribute<TestCleanupAttribute>(testClass);

            foreach (var method in testMethods)
            {
                var testAttr = method.GetCustomAttribute<TestMethodAttribute>();
                if (testAttr == null) continue;

                var result = new TestResult
                {
                    TestName = testAttr.Name ?? method.Name,
                    TestClass = testClass.Name
                };

                var stopwatch = Stopwatch.StartNew();

                try
                {
                    // 执行初始化方法
                    initializeMethod?.Invoke(instance, null);

                    // 执行测试方法
                    method.Invoke(instance, null);

                    result.Passed = true;
                }
                catch (Exception ex)
                {
                    result.Passed = false;
                    result.Exception = ex.InnerException ?? ex;
                    result.ErrorMessage = result.Exception.Message;
                }
                finally
                {
                    try
                    {
                        // 执行清理方法
                        cleanupMethod?.Invoke(instance, null);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"清理方法执行失败: {ex.Message}");
                    }

                    stopwatch.Stop();
                    result.Duration = stopwatch.Elapsed;
                }

                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 获取带有指定特性的方法
        /// </summary>
        /// <typeparam name="T">特性类型</typeparam>
        /// <param name="type">类型</param>
        /// <returns>方法信息</returns>
        private static MethodInfo GetMethodWithAttribute<T>(Type type) where T : Attribute
        {
            var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance);
            foreach (var method in methods)
            {
                if (method.GetCustomAttribute<T>() != null)
                {
                    return method;
                }
            }
            return null;
        }
    }
}
