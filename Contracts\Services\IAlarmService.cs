using System;
using System.Collections.Generic;
using Contracts.Events;

namespace Contracts.Services
{
    /// <summary>
    /// 报警服务接口
    /// </summary>
    /// <remarks>
    /// 提供报警管理、规则评估和历史记录功能
    /// </remarks>
    public interface IAlarmService
    {
        /// <summary>
        /// 获取当前活动报警列表
        /// </summary>
        /// <returns>活动报警列表</returns>
        /// <remarks>返回所有未确认或未清除的报警</remarks>
        IList<AlarmInfo> GetActiveAlarms();

        /// <summary>
        /// 获取历史报警记录
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>历史报警列表</returns>
        IList<AlarmInfo> GetAlarmHistory(DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 确认报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="acknowledgedBy">确认人</param>
        /// <returns>确认是否成功</returns>
        bool AcknowledgeAlarm(string alarmId, string acknowledgedBy = "System");

        /// <summary>
        /// 确认所有活动报警
        /// </summary>
        /// <param name="acknowledgedBy">确认人</param>
        /// <returns>确认的报警数量</returns>
        int AcknowledgeAllAlarms(string acknowledgedBy = "System");

        /// <summary>
        /// 清除报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <returns>清除是否成功</returns>
        bool ClearAlarm(string alarmId);

        /// <summary>
        /// 清除所有已确认的报警
        /// </summary>
        /// <returns>清除的报警数量</returns>
        int ClearAcknowledgedAlarms();

        /// <summary>
        /// 添加报警规则
        /// </summary>
        /// <param name="rule">报警规则</param>
        /// <returns>添加是否成功</returns>
        bool AddAlarmRule(AlarmRule rule);

        /// <summary>
        /// 移除报警规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <returns>移除是否成功</returns>
        bool RemoveAlarmRule(string ruleId);

        /// <summary>
        /// 获取所有报警规则
        /// </summary>
        /// <returns>报警规则列表</returns>
        IList<AlarmRule> GetAlarmRules();

        /// <summary>
        /// 评估报警规则
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="dataPoint">数据点名称</param>
        /// <param name="value">数据值</param>
        /// <remarks>根据设备数据评估是否触发报警</remarks>
        void EvaluateRules(string deviceId, string dataPoint, object value);

        /// <summary>
        /// 手动触发报警
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        /// <returns>触发是否成功</returns>
        bool TriggerAlarm(AlarmInfo alarmInfo);

        /// <summary>
        /// 获取报警统计信息
        /// </summary>
        /// <returns>报警统计</returns>
        AlarmStatistics GetAlarmStatistics();

        /// <summary>
        /// 启动报警监控
        /// </summary>
        /// <remarks>开始监控设备数据并评估报警规则</remarks>
        void StartMonitoring();

        /// <summary>
        /// 停止报警监控
        /// </summary>
        void StopMonitoring();
    }

    /// <summary>
    /// 报警规则
    /// </summary>
    public class AlarmRule
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 规则描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 设备ID（为空表示适用于所有设备）
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 数据点名称
        /// </summary>
        public string DataPoint { get; set; }

        /// <summary>
        /// 报警级别
        /// </summary>
        public AlarmLevel Level { get; set; }

        /// <summary>
        /// 规则类型
        /// </summary>
        public AlarmRuleType RuleType { get; set; }

        /// <summary>
        /// 阈值（用于数值比较）
        /// </summary>
        public double? Threshold { get; set; }

        /// <summary>
        /// 比较操作符
        /// </summary>
        public ComparisonOperator Operator { get; set; }

        /// <summary>
        /// 期望值（用于状态比较）
        /// </summary>
        public object ExpectedValue { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 报警消息模板
        /// </summary>
        public string MessageTemplate { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public AlarmRule()
        {
            Id = Guid.NewGuid().ToString();
            IsEnabled = true;
            CreatedTime = DateTime.Now;
            Level = AlarmLevel.Warning;
            RuleType = AlarmRuleType.Threshold;
            Operator = ComparisonOperator.GreaterThan;
        }
    }

    /// <summary>
    /// 报警规则类型
    /// </summary>
    public enum AlarmRuleType
    {
        /// <summary>
        /// 阈值报警
        /// </summary>
        Threshold,

        /// <summary>
        /// 状态报警
        /// </summary>
        Status,

        /// <summary>
        /// 范围报警
        /// </summary>
        Range,

        /// <summary>
        /// 变化率报警
        /// </summary>
        ChangeRate
    }

    /// <summary>
    /// 比较操作符
    /// </summary>
    public enum ComparisonOperator
    {
        /// <summary>
        /// 等于
        /// </summary>
        Equal,

        /// <summary>
        /// 不等于
        /// </summary>
        NotEqual,

        /// <summary>
        /// 大于
        /// </summary>
        GreaterThan,

        /// <summary>
        /// 大于等于
        /// </summary>
        GreaterThanOrEqual,

        /// <summary>
        /// 小于
        /// </summary>
        LessThan,

        /// <summary>
        /// 小于等于
        /// </summary>
        LessThanOrEqual
    }

    /// <summary>
    /// 报警统计信息
    /// </summary>
    public class AlarmStatistics
    {
        /// <summary>
        /// 当前活动报警数量
        /// </summary>
        public int ActiveAlarmCount { get; set; }

        /// <summary>
        /// 今日报警数量
        /// </summary>
        public int TodayAlarmCount { get; set; }

        /// <summary>
        /// 本周报警数量
        /// </summary>
        public int WeekAlarmCount { get; set; }

        /// <summary>
        /// 本月报警数量
        /// </summary>
        public int MonthAlarmCount { get; set; }

        /// <summary>
        /// 各级别报警数量
        /// </summary>
        public Dictionary<AlarmLevel, int> AlarmCountByLevel { get; set; }

        /// <summary>
        /// 最近报警时间
        /// </summary>
        public DateTime? LastAlarmTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public AlarmStatistics()
        {
            AlarmCountByLevel = new Dictionary<AlarmLevel, int>();
        }
    }
}
