using System;
using System.Linq;
using DeviceModule.Tests.TestFramework;
using static DeviceModule.Tests.TestFramework.SimpleTestFramework;
using DeviceModule.Services;

namespace DeviceModule.Tests
{
    /// <summary>
    /// MockDeviceService测试类
    /// </summary>
    /// <remarks>
    /// 测试模拟设备服务的所有功能
    /// </remarks>
    [TestClass("MockDeviceService测试")]
    public class MockDeviceServiceTests
    {
        private MockDeviceService _deviceService;

        /// <summary>
        /// 测试初始化
        /// </summary>
        [TestInitialize]
        public void TestInitialize()
        {
            _deviceService = new MockDeviceService();
        }

        /// <summary>
        /// 测试清理
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            try
            {
                _deviceService?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试服务创建
        /// </summary>
        [TestMethod("服务创建测试", "验证MockDeviceService能够正确创建")]
        public void MockDeviceService_ShouldBeCreated()
        {
            // Assert
            Assert.IsNotNull(_deviceService, "设备服务应该能够创建");
        }

        /// <summary>
        /// 测试获取所有设备
        /// </summary>
        [TestMethod("获取所有设备测试", "验证能够正确获取所有设备")]
        public void GetAllDevices_ShouldReturnDevices()
        {
            // Act
            var devices = _deviceService.GetAllDevices();

            // Assert
            Assert.IsNotNull(devices, "设备列表不应该为null");
            Assert.AreEqual(5, devices.Count, "应该返回5个设备");

            // 验证设备信息
            var tempSensor = devices.FirstOrDefault(d => d.Id == "TEMP_001");
            Assert.IsNotNull(tempSensor, "应该包含温度传感器");
            Assert.AreEqual("温度传感器01", tempSensor.Name, "温度传感器名称应该正确");
            Assert.AreEqual("Temperature", tempSensor.Type, "温度传感器类型应该正确");
        }

        /// <summary>
        /// 测试获取设备数据
        /// </summary>
        [TestMethod("获取设备数据测试", "验证能够正确获取设备数据")]
        public void GetDeviceData_ShouldReturnData()
        {
            // Arrange
            var deviceId = "TEMP_001";

            // Act
            var data = _deviceService.GetDeviceData(deviceId);

            // Assert
            Assert.IsNotNull(data, "设备数据不应该为null");
            Assert.AreEqual(deviceId, data.DeviceId, "设备ID应该匹配");
            Assert.IsTrue(data.Value >= 20 && data.Value <= 80, "温度值应该在合理范围内");
            Assert.AreEqual("°C", data.Unit, "温度单位应该正确");
        }

        /// <summary>
        /// 测试启动设备
        /// </summary>
        [TestMethod("启动设备测试", "验证能够正确启动设备")]
        public void StartDevice_ShouldReturnTrue()
        {
            // Arrange
            var deviceId = "TEMP_001";

            // Act
            var result = _deviceService.StartDevice(deviceId);

            // Assert
            Assert.IsTrue(result, "启动设备应该成功");
        }

        /// <summary>
        /// 测试停止设备
        /// </summary>
        [TestMethod("停止设备测试", "验证能够正确停止设备")]
        public void StopDevice_ShouldReturnTrue()
        {
            // Arrange
            var deviceId = "TEMP_001";

            // Act
            var result = _deviceService.StopDevice(deviceId);

            // Assert
            Assert.IsTrue(result, "停止设备应该成功");
        }

        /// <summary>
        /// 测试重置设备
        /// </summary>
        [TestMethod("重置设备测试", "验证能够正确重置设备")]
        public void ResetDevice_ShouldReturnTrue()
        {
            // Arrange
            var deviceId = "TEMP_001";

            // Act
            var result = _deviceService.ResetDevice(deviceId);

            // Assert
            Assert.IsTrue(result, "重置设备应该成功");
        }

        /// <summary>
        /// 测试无效设备ID
        /// </summary>
        [TestMethod("无效设备ID测试", "验证对无效设备ID的处理")]
        public void InvalidDeviceId_ShouldReturnFalse()
        {
            // Arrange
            var invalidId = "INVALID_ID";

            // Act
            var startResult = _deviceService.StartDevice(invalidId);
            var stopResult = _deviceService.StopDevice(invalidId);
            var resetResult = _deviceService.ResetDevice(invalidId);
            var data = _deviceService.GetDeviceData(invalidId);

            // Assert
            Assert.IsFalse(startResult, "启动无效设备应该返回false");
            Assert.IsFalse(stopResult, "停止无效设备应该返回false");
            Assert.IsFalse(resetResult, "重置无效设备应该返回false");
            Assert.IsNull(data, "获取无效设备数据应该返回null");
        }

        /// <summary>
        /// 测试空设备ID
        /// </summary>
        [TestMethod("空设备ID测试", "验证对空设备ID的处理")]
        public void NullDeviceId_ShouldReturnFalse()
        {
            // Act
            var startResult = _deviceService.StartDevice(null);
            var stopResult = _deviceService.StopDevice(null);
            var resetResult = _deviceService.ResetDevice(null);
            var data = _deviceService.GetDeviceData(null);

            // Assert
            Assert.IsFalse(startResult, "启动null设备应该返回false");
            Assert.IsFalse(stopResult, "停止null设备应该返回false");
            Assert.IsFalse(resetResult, "重置null设备应该返回false");
            Assert.IsNull(data, "获取null设备数据应该返回null");
        }

        /// <summary>
        /// 测试所有设备类型
        /// </summary>
        [TestMethod("所有设备类型测试", "验证所有设备类型都能正确处理")]
        public void AllDeviceTypes_ShouldWork()
        {
            // Arrange
            var devices = _deviceService.GetAllDevices();

            // Act & Assert
            foreach (var device in devices)
            {
                // 测试每个设备的操作
                var startResult = _deviceService.StartDevice(device.Id);
                Assert.IsTrue(startResult, $"启动设备{device.Id}应该成功");

                var data = _deviceService.GetDeviceData(device.Id);
                Assert.IsNotNull(data, $"获取设备{device.Id}数据应该成功");
                Assert.AreEqual(device.Id, data.DeviceId, $"设备{device.Id}的数据ID应该匹配");

                var stopResult = _deviceService.StopDevice(device.Id);
                Assert.IsTrue(stopResult, $"停止设备{device.Id}应该成功");

                var resetResult = _deviceService.ResetDevice(device.Id);
                Assert.IsTrue(resetResult, $"重置设备{device.Id}应该成功");
            }
        }

        /// <summary>
        /// 测试数据值范围
        /// </summary>
        [TestMethod("数据值范围测试", "验证不同设备类型的数据值在合理范围内")]
        public void DataValueRanges_ShouldBeReasonable()
        {
            // Arrange & Act
            var tempData = _deviceService.GetDeviceData("TEMP_001");
            var pressureData = _deviceService.GetDeviceData("PRESS_001");
            var flowData = _deviceService.GetDeviceData("FLOW_001");
            var valveData = _deviceService.GetDeviceData("VALVE_001");
            var motorData = _deviceService.GetDeviceData("MOTOR_001");

            // Assert
            Assert.IsTrue(tempData.Value >= 20 && tempData.Value <= 80, "温度值应该在20-80°C范围内");
            Assert.IsTrue(pressureData.Value >= 0 && pressureData.Value <= 10, "压力值应该在0-10bar范围内");
            Assert.IsTrue(flowData.Value >= 0 && flowData.Value <= 100, "流量值应该在0-100L/min范围内");
            Assert.IsTrue(valveData.Value >= 0 && valveData.Value <= 100, "阀门开度应该在0-100%范围内");
            Assert.IsTrue(motorData.Value >= 0 && motorData.Value <= 3000, "电机转速应该在0-3000rpm范围内");
        }

        /// <summary>
        /// 测试数据单位
        /// </summary>
        [TestMethod("数据单位测试", "验证不同设备类型的数据单位正确")]
        public void DataUnits_ShouldBeCorrect()
        {
            // Arrange & Act
            var tempData = _deviceService.GetDeviceData("TEMP_001");
            var pressureData = _deviceService.GetDeviceData("PRESS_001");
            var flowData = _deviceService.GetDeviceData("FLOW_001");
            var valveData = _deviceService.GetDeviceData("VALVE_001");
            var motorData = _deviceService.GetDeviceData("MOTOR_001");

            // Assert
            Assert.AreEqual("°C", tempData.Unit, "温度单位应该是°C");
            Assert.AreEqual("bar", pressureData.Unit, "压力单位应该是bar");
            Assert.AreEqual("L/min", flowData.Unit, "流量单位应该是L/min");
            Assert.AreEqual("%", valveData.Unit, "阀门开度单位应该是%");
            Assert.AreEqual("rpm", motorData.Unit, "电机转速单位应该是rpm");
        }

        /// <summary>
        /// 测试服务释放
        /// </summary>
        [TestMethod("服务释放测试", "验证服务能够正确释放资源")]
        public void Dispose_ShouldWork()
        {
            // Act & Assert - 应该不抛出异常
            try
            {
                _deviceService.Dispose();
                Assert.IsTrue(true, "服务释放应该成功");
            }
            catch (Exception ex)
            {
                Assert.IsTrue(false, $"服务释放不应该抛出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试重复操作
        /// </summary>
        [TestMethod("重复操作测试", "验证重复操作的稳定性")]
        public void RepeatedOperations_ShouldBeStable()
        {
            // Arrange
            var deviceId = "TEMP_001";

            // Act & Assert - 重复操作应该稳定
            for (int i = 0; i < 10; i++)
            {
                var startResult = _deviceService.StartDevice(deviceId);
                Assert.IsTrue(startResult, $"第{i + 1}次启动应该成功");

                var data = _deviceService.GetDeviceData(deviceId);
                Assert.IsNotNull(data, $"第{i + 1}次获取数据应该成功");

                var stopResult = _deviceService.StopDevice(deviceId);
                Assert.IsTrue(stopResult, $"第{i + 1}次停止应该成功");
            }
        }
    }
}
