# 工业HMI框架API参考文档

## 版本信息

- **API版本**: 1.0.0
- **文档版本**: 1.0.0
- **发布日期**: 2025-08-13

## 目录

1. [核心接口API](#核心接口api)
2. [服务类API](#服务类api)
3. [事件系统API](#事件系统api)
4. [模块API](#模块api)
5. [工具类API](#工具类api)

## 核心接口API

### IModule接口

模块基础接口，所有功能模块必须实现此接口。

#### 属性

| 属性名 | 类型 | 描述 | 是否只读 |
|--------|------|------|----------|
| Name | string | 模块名称 | 是 |
| Description | string | 模块描述 | 是 |
| EventAggregator | IEventAggregator | 事件聚合器 | 否 |
| Logger | ILogger | 日志记录器 | 否 |

#### 方法

**Initialize()**

```csharp
void Initialize()
```

- **描述**: 初始化模块，在模块加载后调用
- **参数**: 无
- **返回值**: 无
- **异常**: 可抛出初始化相关异常
- **注意**: 必须验证依赖注入是否完成

**Start()**

```csharp
void Start()
```

- **描述**: 启动模块，在初始化完成后调用
- **参数**: 无
- **返回值**: 无
- **异常**: 可抛出启动相关异常

**Stop()**

```csharp
void Stop()
```

- **描述**: 停止模块
- **参数**: 无
- **返回值**: 无
- **异常**: 不应抛出异常

**GetUserControl()**

```csharp
UserControl GetUserControl()
```

- **描述**: 获取模块的用户界面控件
- **参数**: 无
- **返回值**: UserControl实例
- **异常**: 如果UI未初始化可能返回null

**Dispose()**

```csharp
void Dispose()
```

- **描述**: 释放模块资源
- **参数**: 无
- **返回值**: 无
- **异常**: 不应抛出异常

### IEventAggregator接口

事件聚合器接口，用于模块间通信。

#### 方法

**Publish<T>(T eventObj)**

```csharp
void Publish<T>(T eventObj)
```

- **描述**: 发布事件到所有订阅者
- **类型参数**: T - 事件类型
- **参数**: eventObj - 事件对象
- **返回值**: 无
- **异常**: 不会因订阅者异常而失败

**Subscribe<T>(Action<T> handler, bool keepSubscriberReferenceAlive)**

```csharp
void Subscribe<T>(Action<T> handler, bool keepSubscriberReferenceAlive = true)
```

- **描述**: 订阅特定类型的事件
- **类型参数**: T - 事件类型
- **参数**:
  - handler - 事件处理器
  - keepSubscriberReferenceAlive - 是否保持订阅者引用
- **返回值**: 无
- **建议**: 模块中设置keepSubscriberReferenceAlive为false

**Unsubscribe<T>(Action<T> handler)**

```csharp
void Unsubscribe<T>(Action<T> handler)
```

- **描述**: 取消事件订阅
- **类型参数**: T - 事件类型
- **参数**: handler - 要取消的事件处理器
- **返回值**: 无
- **注意**: 必须在Dispose中调用

### ILogger接口

日志记录器接口，提供分级日志记录功能。

#### 方法

**Debug(string message)**

```csharp
void Debug(string message)
void Debug(string message, Exception exception)
```

- **描述**: 记录调试级别日志
- **参数**:
  - message - 日志消息
  - exception - 异常对象（可选）
- **使用场景**: 详细的调试信息

**Info(string message)**

```csharp
void Info(string message)
void Info(string message, Exception exception)
```

- **描述**: 记录信息级别日志
- **参数**:
  - message - 日志消息
  - exception - 异常对象（可选）
- **使用场景**: 一般信息记录

**Warning(string message)**

```csharp
void Warning(string message)
void Warning(string message, Exception exception)
```

- **描述**: 记录警告级别日志
- **参数**:
  - message - 日志消息
  - exception - 异常对象（可选）
- **使用场景**: 警告信息

**Error(string message)**

```csharp
void Error(string message)
void Error(string message, Exception exception)
```

- **描述**: 记录错误级别日志
- **参数**:
  - message - 日志消息
  - exception - 异常对象（可选）
- **使用场景**: 错误信息

**Fatal(string message)**

```csharp
void Fatal(string message)
void Fatal(string message, Exception exception)
```

- **描述**: 记录致命错误级别日志
- **参数**:
  - message - 日志消息
  - exception - 异常对象（可选）
- **使用场景**: 致命错误

## 服务类API

### ModuleLoader类

模块加载器，负责动态加载和管理模块。

#### 构造函数

**ModuleLoader(IEventAggregator eventAggregator, ILogger logger)**

```csharp
public ModuleLoader(IEventAggregator eventAggregator, ILogger logger)
```

- **描述**: 创建模块加载器实例
- **参数**:
  - eventAggregator - 事件聚合器
  - logger - 日志记录器

#### 方法

**LoadModulesFromDirectory(string directoryPath)**

```csharp
public List<IModule> LoadModulesFromDirectory(string directoryPath)
```

- **描述**: 从指定目录加载所有模块
- **参数**: directoryPath - 模块目录路径
- **返回值**: 加载成功的模块列表
- **异常**: DirectoryNotFoundException, FileLoadException

**LoadModulesFromAssembly(Assembly assembly)**

```csharp
public List<IModule> LoadModulesFromAssembly(Assembly assembly)
```

- **描述**: 从程序集加载模块
- **参数**: assembly - 程序集对象
- **返回值**: 加载成功的模块列表
- **异常**: ReflectionTypeLoadException

### EventAggregator类

事件聚合器实现类。

#### 构造函数

**EventAggregator()**

```csharp
public EventAggregator()
```

- **描述**: 创建事件聚合器实例

#### 属性

**SubscriberCount**

```csharp
public int SubscriberCount { get; }
```

- **描述**: 获取当前订阅者总数
- **类型**: int
- **只读**: 是

#### 方法

**GetSubscriberCount<T>()**

```csharp
public int GetSubscriberCount<T>()
```

- **描述**: 获取特定事件类型的订阅者数量
- **类型参数**: T - 事件类型
- **返回值**: 订阅者数量

**Clear()**

```csharp
public void Clear()
```

- **描述**: 清除所有订阅
- **返回值**: 无
- **注意**: 谨慎使用，会影响所有模块

### SerilogLogger类

Serilog日志记录器实现类。

#### 构造函数

**SerilogLogger(string logFilePath)**

```csharp
public SerilogLogger(string logFilePath = null)
```

- **描述**: 创建日志记录器实例
- **参数**: logFilePath - 日志文件路径（可选）
- **默认**: 使用应用程序目录下的logs文件夹

## 事件系统API

### 标准事件类型

#### DeviceConnectionEvent

设备连接状态变化事件。

```csharp
public class DeviceConnectionEvent
{
    public string DeviceId { get; set; }        // 设备ID
    public bool IsConnected { get; set; }       // 连接状态
    public DateTime Timestamp { get; set; }     // 时间戳
    public string Message { get; set; }         // 消息
}
```

#### DeviceDataUpdateEvent

设备数据更新事件。

```csharp
public class DeviceDataUpdateEvent
{
    public string DeviceId { get; set; }        // 设备ID
    public Dictionary<string, object> Data { get; set; }  // 数据字典
    public DateTime Timestamp { get; set; }     // 时间戳
}
```

#### AlarmEvent

报警事件。

```csharp
public class AlarmEvent
{
    public string AlarmId { get; set; }         // 报警ID
    public string DeviceId { get; set; }        // 设备ID
    public AlarmLevel Level { get; set; }       // 报警级别
    public string Message { get; set; }         // 报警消息
    public DateTime Timestamp { get; set; }     // 时间戳
    public bool IsActive { get; set; }          // 是否激活
}
```

#### SystemStatusEvent

系统状态事件。

```csharp
public class SystemStatusEvent
{
    public SystemStatus Status { get; set; }    // 系统状态
    public string Message { get; set; }         // 状态消息
    public DateTime Timestamp { get; set; }     // 时间戳
}
```

### 事件使用示例

**发布事件**:

```csharp
var connectionEvent = new DeviceConnectionEvent
{
    DeviceId = "Device001",
    IsConnected = true,
    Timestamp = DateTime.Now,
    Message = "设备连接成功"
};

EventAggregator?.Publish(connectionEvent);
```

**订阅事件**:

```csharp
EventAggregator?.Subscribe<DeviceConnectionEvent>(OnDeviceConnection, false);

private void OnDeviceConnection(DeviceConnectionEvent eventObj)
{
    Logger?.Info($"设备 {eventObj.DeviceId} 连接状态: {eventObj.IsConnected}");
}
```

## 模块API

### 设备监控模块 (DeviceModule)

#### DeviceModel类

**构造函数**:

```csharp
public DeviceModel(IDeviceService deviceService, IEventAggregator eventAggregator, ILogger logger)
```

**主要方法**:

- `LoadDeviceList()` - 加载设备列表
- `ConnectDevice(string deviceId)` - 连接设备
- `DisconnectDevice(string deviceId)` - 断开设备
- `StartMonitoring()` - 开始监控
- `StopMonitoring()` - 停止监控
- `GetDevices()` - 获取设备列表

#### DevicePresenter类

**构造函数**:

```csharp
public DevicePresenter(IDeviceView view, DeviceModel model, IEventAggregator eventAggregator, ILogger logger)
```

**主要方法**:

- `Start()` - 启动表示器
- `Stop()` - 停止表示器
- `RefreshDeviceList()` - 刷新设备列表

### 报警管理模块 (AlarmModule)

#### AlarmModel类

**主要方法**:

- `GetActiveAlarms()` - 获取活动报警
- `AcknowledgeAlarm(string alarmId)` - 确认报警
- `ClearAlarm(string alarmId)` - 清除报警

### 通信测试模块 (CommunicationTestModule)

#### PerformanceMonitor类

**主要方法**:

- `StartMonitoring()` - 开始性能监控
- `StopMonitoring()` - 停止性能监控
- `GetCurrentSnapshot()` - 获取当前性能快照
- `RecordSystemMetric(string name, double value, string unit)` - 记录系统指标
- `GenerateSystemPerformanceReport()` - 生成性能报告

## 工具类API

### 测试框架 (SimpleTestFramework)

#### 测试特性

**TestClassAttribute**:

```csharp
[TestClass("测试类描述")]
public class MyTests { }
```

**TestMethodAttribute**:

```csharp
[TestMethod("测试方法名", "测试描述")]
public void MyTest() { }
```

**TestInitializeAttribute**:

```csharp
[TestInitialize]
public void Setup() { }
```

**TestCleanupAttribute**:

```csharp
[TestCleanup]
public void Cleanup() { }
```

#### 断言方法

**Assert类**:

- `IsTrue(bool condition, string message)` - 断言为真
- `IsFalse(bool condition, string message)` - 断言为假
- `AreEqual(object expected, object actual, string message)` - 断言相等
- `AreNotEqual(object expected, object actual, string message)` - 断言不相等
- `IsNull(object value, string message)` - 断言为null
- `IsNotNull(object value, string message)` - 断言不为null
- `AreSame(object expected, object actual, string message)` - 断言是同一对象
- `AreNotSame(object expected, object actual, string message)` - 断言不是同一对象

## 错误代码

### 模块加载错误

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| ML001 | 模块文件不存在 | 检查模块文件路径 |
| ML002 | 模块加载失败 | 检查依赖项和程序集 |
| ML003 | 模块初始化失败 | 检查模块构造函数 |
| ML004 | 依赖注入失败 | 确保正确实现IModule接口 |

### 事件系统错误

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| ES001 | 事件发布失败 | 检查事件对象是否为null |
| ES002 | 事件订阅失败 | 检查处理器是否为null |
| ES003 | 事件处理异常 | 在处理器中添加异常处理 |

### 日志系统错误

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| LS001 | 日志文件创建失败 | 检查文件路径和权限 |
| LS002 | 日志写入失败 | 检查磁盘空间和权限 |

### 配置系统错误

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| CS001 | 配置文件不存在 | 检查配置文件路径 |
| CS002 | 配置文件格式错误 | 验证XML格式 |
| CS003 | 配置源加载失败 | 检查配置源实现 |
| CS004 | 配置热更新失败 | 检查文件监控权限 |

## 配置管理API

### IConfigurationService 接口

配置服务的核心接口，提供统一的配置管理功能。

#### 基本配置读取

```csharp
// 获取字符串配置
string GetString(string key, string defaultValue = "")

// 获取整数配置
int GetInt(string key, int defaultValue = 0)

// 获取布尔配置
bool GetBool(string key, bool defaultValue = false)

// 获取双精度浮点配置
double GetDouble(string key, double defaultValue = 0.0)

// 获取泛型配置
T GetValue<T>(string key, T defaultValue = default(T))
```

**参数说明**:

- `key`: 配置键名
- `defaultValue`: 默认值（当配置不存在时返回）

**返回值**: 配置值或默认值

**示例**:

```csharp
var logLevel = configService.GetString("LogLevel", "Info");
var maxConnections = configService.GetInt("MaxConnections", 10);
var enableFeature = configService.GetBool("EnableAdvancedFeatures", false);
```

#### 配置管理

```csharp
// 设置配置值
void SetValue(string key, object value)

// 检查配置是否存在
bool ContainsKey(string key)

// 删除配置项
bool RemoveKey(string key)

// 获取所有配置键
IEnumerable<string> GetAllKeys()

// 获取配置节
Dictionary<string, object> GetSection(string prefix)
```

**示例**:

```csharp
// 设置配置
configService.SetValue("NewSetting", "value");

// 检查配置存在
if (configService.ContainsKey("DatabaseConnection"))
{
    var connection = configService.GetString("DatabaseConnection");
}

// 获取配置节
var dbSettings = configService.GetSection("database");
```

#### 配置源管理

```csharp
// 添加配置源
void AddSource(IConfigurationSource source)

// 移除配置源
void RemoveSource(IConfigurationSource source)

// 获取所有配置源
IEnumerable<IConfigurationSource> GetSources()
```

**示例**:

```csharp
// 添加文件配置源
var fileSource = new FileConfigurationSource("config/app.config", 50, true);
configService.AddSource(fileSource);

// 添加环境变量配置源
var envSource = new EnvironmentConfigurationSource("APP_", 100);
configService.AddSource(envSource);
```

#### 热更新管理

```csharp
// 启用热更新
void EnableHotReload()

// 禁用热更新
void DisableHotReload()

// 检查热更新状态
bool IsHotReloadEnabled { get; }

// 重新加载配置
bool Reload()

// 保存配置
bool Save()
```

#### 配置变更事件

```csharp
// 配置变更事件
event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged
```

**事件参数**:

```csharp
public class ConfigurationChangedEventArgs : EventArgs
{
    public string Key { get; set; }           // 变更的配置键
    public object OldValue { get; set; }      // 旧值
    public object NewValue { get; set; }      // 新值
    public string SourceName { get; set; }    // 配置源名称
    public DateTime Timestamp { get; set; }   // 变更时间
}
```

### IConfigurationSource 接口

配置源接口，用于实现自定义配置源。

#### 属性

```csharp
string Name { get; }                    // 配置源名称
int Priority { get; }                   // 优先级（数值越大优先级越高）
bool CanWrite { get; }                  // 是否支持写入
bool SupportsHotReload { get; }         // 是否支持热更新
```

#### 方法

```csharp
// 加载配置
Dictionary<string, object> Load()

// 保存配置
void Save(Dictionary<string, object> configurations)

// 开始监控变更
void StartWatching()

// 停止监控变更
void StopWatching()

// 释放资源
void Dispose()
```

#### 事件

```csharp
// 配置变更事件
event EventHandler<ConfigurationChangedEventArgs> Changed
```

### 内置配置源

#### FileConfigurationSource

基于XML文件的配置源。

**构造函数**:

```csharp
FileConfigurationSource(string filePath, int priority = 0, bool canWrite = true, string name = null)
```

**特性**:

- 支持XML格式配置文件
- 支持热更新（文件变更监控）
- 支持读写操作
- 自动解析appSettings和自定义配置节

#### EnvironmentConfigurationSource

基于环境变量的配置源。

**构造函数**:

```csharp
EnvironmentConfigurationSource(string prefix = null, int priority = 100, bool removePrefix = true, string name = null)
```

**特性**:

- 读取系统环境变量
- 支持前缀过滤
- 自动转换键名格式（下划线转点号）
- 只读配置源

### 配置优先级

配置源按优先级从高到低读取，数值越大优先级越高：

1. **环境变量配置源** (默认优先级: 100)
2. **自定义文件配置源** (默认优先级: 50)
3. **默认文件配置源** (默认优先级: 10)

### 配置文件格式

#### 标准appSettings格式

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="LogLevel" value="Debug" />
    <add key="MaxConnections" value="100" />
    <add key="EnableFeature" value="true" />
  </appSettings>
</configuration>
```

#### 自定义配置节格式

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <database>
    <connectionString value="Server=localhost;Database=HMI" />
    <timeout value="30" />
    <pool maxSize="10" minSize="2" />
  </database>

  <logging>
    <level value="Debug" />
    <file path="logs/app.log" maxSize="10MB" />
  </logging>
</configuration>
```

### 环境变量命名规则

环境变量使用以下命名规则：

```bash
# 格式: PREFIX_Section_Key
INDUSTRIAL_HMI_Database_Host=localhost
INDUSTRIAL_HMI_Database_Port=5432
INDUSTRIAL_HMI_Logging_Level=Debug

# 转换为配置键:
# Database.Host = localhost
# Database.Port = 5432
# Logging.Level = Debug
```

---

**版本历史**:

- v1.0.0 (2025-08-13): 初始版本

**相关文档**:

- [开发者文档](./开发者文档.md)
- [用户操作手册](../用户手册/用户操作手册.md)
