using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using Contracts;
using Contracts.Events;

namespace Services
{
    /// <summary>
    /// 模块加载器
    /// </summary>
    /// <remarks>
    /// 负责扫描、加载和管理模块，提供模块生命周期管理和异常隔离
    /// </remarks>
    public class ModuleLoader : IDisposable
    {
        private readonly IEventAggregator _eventAggregator;
        private readonly ILogger _logger;
        private readonly object _serviceContainer; // 通用容器接口
        private readonly List<IModule> _loadedModules = new List<IModule>();
        private readonly object _lock = new object();
        private bool _disposed = false;

        /// <summary>
        /// 已加载的模块列表
        /// </summary>
        public IReadOnlyList<IModule> LoadedModules
        {
            get
            {
                lock (_lock)
                {
                    return _loadedModules.ToList();
                }
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventAggregator">事件聚合器</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="serviceContainer">服务容器（可选）</param>
        public ModuleLoader(IEventAggregator eventAggregator, ILogger logger, object serviceContainer = null)
        {
            _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceContainer = serviceContainer;
        }

        /// <summary>
        /// 从指定目录加载所有模块
        /// </summary>
        /// <param name="modulesDirectory">模块目录路径</param>
        /// <returns>加载成功的模块数量</returns>
        public int LoadModulesFromDirectory(string modulesDirectory)
        {
            if (string.IsNullOrEmpty(modulesDirectory) || !Directory.Exists(modulesDirectory))
            {
                _logger.Warning($"模块目录不存在: {modulesDirectory}");
                return 0;
            }

            _logger.Info($"开始从目录加载模块: {modulesDirectory}");

            var loadedCount = 0;
            var dllFiles = Directory.GetFiles(modulesDirectory, "*.dll", SearchOption.TopDirectoryOnly);

            foreach (var dllFile in dllFiles)
            {
                try
                {
                    var loadedModules = LoadModulesFromAssembly(dllFile);
                    loadedCount += loadedModules;
                }
                catch (Exception ex)
                {
                    _logger.Error($"加载程序集失败: {dllFile}", ex);
                }
            }

            _logger.Info($"模块加载完成，共加载 {loadedCount} 个模块");
            return loadedCount;
        }

        /// <summary>
        /// 从程序集加载模块
        /// </summary>
        /// <param name="assemblyPath">程序集路径</param>
        /// <returns>加载成功的模块数量</returns>
        public int LoadModulesFromAssembly(string assemblyPath)
        {
            _logger.Debug($"开始加载程序集: {assemblyPath}");

            try
            {
                var assembly = Assembly.LoadFrom(assemblyPath);
                _logger.Debug($"程序集加载成功: {assembly.FullName}");

                // 使用接口名称匹配来避免类型身份问题
                var moduleTypes = assembly.GetTypes()
                    .Where(t => !t.IsInterface && !t.IsAbstract &&
                               t.GetInterfaces().Any(i => i.Name == "IModule" || i.FullName == "Contracts.IModule"))
                    .ToList();

                _logger.Debug($"发现模块类型: {string.Join(", ", moduleTypes.Select(t => t.Name))}");

                // 详细记录每个类型的接口信息
                foreach (var type in assembly.GetTypes().Where(t => !t.IsInterface && !t.IsAbstract))
                {
                    var interfaces = type.GetInterfaces();
                    if (interfaces.Any())
                    {
                        _logger.Debug($"类型 {type.Name} 实现的接口: {string.Join(", ", interfaces.Select(i => i.FullName))}");
                    }
                }

                var loadedCount = 0;
                foreach (var moduleType in moduleTypes)
                {
                    try
                    {
                        var module = LoadModule(moduleType);
                        if (module != null)
                        {
                            loadedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"加载模块失败: {moduleType.Name}", ex);
                    }
                }

                return loadedCount;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载程序集失败: {assemblyPath}", ex);
                return 0;
            }
        }

        /// <summary>
        /// 加载单个模块
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>加载的模块实例</returns>
        private IModule LoadModule(Type moduleType)
        {
            _logger.Debug($"开始加载模块: {moduleType.Name}");

            try
            {
                // 检查模块是否有无参构造函数
                var constructor = moduleType.GetConstructor(Type.EmptyTypes);
                if (constructor == null)
                {
                    _logger.Error($"模块 {moduleType.Name} 没有无参构造函数");
                    return null;
                }

                // 创建模块实例
                var moduleInstance = Activator.CreateInstance(moduleType);

                // 进行依赖注入
                InjectDependencies(moduleInstance);

                // 使用反射调用模块方法
                var initializeMethod = moduleType.GetMethod("Initialize");
                var startMethod = moduleType.GetMethod("Start");
                var nameProperty = moduleType.GetProperty("Name");
                var descriptionProperty = moduleType.GetProperty("Description");

                if (initializeMethod == null || startMethod == null || nameProperty == null || descriptionProperty == null)
                {
                    _logger.Error($"模块 {moduleType.Name} 缺少必要的方法或属性");
                    return null;
                }

                // 初始化模块
                initializeMethod.Invoke(moduleInstance, null);

                // 启动模块
                startMethod.Invoke(moduleInstance, null);

                // 获取模块信息
                var moduleName = nameProperty.GetValue(moduleInstance)?.ToString() ?? moduleType.Name;
                var moduleDescription = descriptionProperty.GetValue(moduleInstance)?.ToString() ?? "无描述";

                // 创建模块包装器
                var moduleWrapper = new ModuleWrapper(moduleInstance, moduleType);

                lock (_lock)
                {
                    _loadedModules.Add(moduleWrapper);
                }

                _logger.Info($"模块加载成功: {moduleName} - {moduleDescription}");

                // 发布模块加载事件
                _eventAggregator.GetEvent<ModuleLoadedEvent>()
                    .Publish(new ModuleLoadedEvent(moduleName, moduleDescription));

                return moduleWrapper;
            }
            catch (Exception ex)
            {
                _logger.Error($"加载模块失败: {moduleType.Name}", ex);
                return null;
            }
        }

        /// <summary>
        /// 为模块注入依赖
        /// </summary>
        /// <param name="moduleInstance">模块实例</param>
        private void InjectDependencies(object moduleInstance)
        {
            var moduleType = moduleInstance.GetType();

            // 注入EventAggregator
            var eventAggregatorProperty = moduleType.GetProperty("EventAggregator");
            if (eventAggregatorProperty != null && eventAggregatorProperty.CanWrite)
            {
                eventAggregatorProperty.SetValue(moduleInstance, _eventAggregator);
                _logger.Debug($"为模块 {moduleType.Name} 注入EventAggregator");
            }

            // 注入Logger
            var loggerProperty = moduleType.GetProperty("Logger");
            if (loggerProperty != null && loggerProperty.CanWrite)
            {
                loggerProperty.SetValue(moduleInstance, _logger);
                _logger.Debug($"为模块 {moduleType.Name} 注入Logger");
            }

            // 如果有服务容器，尝试注入其他服务
            if (_serviceContainer != null)
            {
                InjectAdditionalServices(moduleInstance, moduleType);
            }

            _logger.Debug($"为模块 {moduleType.Name} 完成依赖注入");
        }

        /// <summary>
        /// 注入额外的服务
        /// </summary>
        /// <param name="moduleInstance">模块实例</param>
        /// <param name="moduleType">模块类型</param>
        private void InjectAdditionalServices(object moduleInstance, Type moduleType)
        {
            try
            {
                // 获取所有可写的接口属性
                var properties = moduleType.GetProperties()
                    .Where(p => p.CanWrite &&
                               p.PropertyType.IsInterface &&
                               p.PropertyType != typeof(IModule) &&
                               p.PropertyType != typeof(IEventAggregator) &&
                               p.PropertyType != typeof(ILogger))
                    .ToList();

                foreach (var property in properties)
                {
                    try
                    {
                        // 尝试从容器解析服务
                        var service = ResolveServiceFromContainer(property.PropertyType);
                        if (service != null)
                        {
                            property.SetValue(moduleInstance, service);
                            _logger.Debug($"为模块 {moduleType.Name} 注入服务: {property.PropertyType.Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning($"为模块 {moduleType.Name} 注入服务 {property.PropertyType.Name} 失败", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning($"为模块 {moduleType.Name} 注入额外服务时发生错误", ex);
            }
        }

        /// <summary>
        /// 从容器解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        private object ResolveServiceFromContainer(Type serviceType)
        {
            if (_serviceContainer == null) return null;

            try
            {
                // 使用反射调用容器的Resolve方法
                var resolveMethod = _serviceContainer.GetType().GetMethod("Resolve", new[] { typeof(Type) });
                if (resolveMethod != null)
                {
                    return resolveMethod.Invoke(_serviceContainer, new object[] { serviceType });
                }

                // 尝试泛型Resolve方法
                var genericResolveMethod = _serviceContainer.GetType().GetMethod("Resolve", Type.EmptyTypes);
                if (genericResolveMethod != null)
                {
                    var genericMethod = genericResolveMethod.MakeGenericMethod(serviceType);
                    return genericMethod.Invoke(_serviceContainer, null);
                }
            }
            catch (Exception ex)
            {
                _logger.Debug($"从容器解析服务 {serviceType.Name} 失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 卸载模块
        /// </summary>
        /// <param name="module">要卸载的模块</param>
        /// <returns>是否卸载成功</returns>
        public bool UnloadModule(IModule module)
        {
            if (module == null) return false;

            try
            {
                _logger.Info($"开始卸载模块: {module.Name}");

                // 停止模块
                module.Stop();

                // 释放模块资源
                module.Dispose();

                lock (_lock)
                {
                    _loadedModules.Remove(module);
                }

                // 发布模块卸载事件
                _eventAggregator.GetEvent<ModuleUnloadedEvent>()
                    .Publish(new ModuleUnloadedEvent(module.Name, "正常卸载"));

                _logger.Info($"模块卸载成功: {module.Name}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"卸载模块失败: {module.Name}", ex);
                return false;
            }
        }

        /// <summary>
        /// 卸载所有模块
        /// </summary>
        public void UnloadAllModules()
        {
            _logger.Info("开始卸载所有模块");

            List<IModule> modulesToUnload;
            lock (_lock)
            {
                modulesToUnload = _loadedModules.ToList();
            }

            foreach (var module in modulesToUnload)
            {
                UnloadModule(module);
            }

            _logger.Info("所有模块卸载完成");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                UnloadAllModules();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 模块包装器，用于包装通过反射加载的模块实例
    /// </summary>
    public class ModuleWrapper : IModule
    {
        private readonly object _moduleInstance;
        private readonly Type _moduleType;
        private readonly MethodInfo _initializeMethod;
        private readonly MethodInfo _startMethod;
        private readonly MethodInfo _stopMethod;
        private readonly MethodInfo _disposeMethod;
        private readonly MethodInfo _getUserControlMethod;
        private readonly PropertyInfo _nameProperty;
        private readonly PropertyInfo _descriptionProperty;
        private readonly PropertyInfo _versionProperty;
        private readonly PropertyInfo _eventAggregatorProperty;
        private readonly PropertyInfo _loggerProperty;

        public ModuleWrapper(object moduleInstance, Type moduleType)
        {
            _moduleInstance = moduleInstance ?? throw new ArgumentNullException(nameof(moduleInstance));
            _moduleType = moduleType ?? throw new ArgumentNullException(nameof(moduleType));

            // 缓存反射信息
            _initializeMethod = _moduleType.GetMethod("Initialize");
            _startMethod = _moduleType.GetMethod("Start");
            _stopMethod = _moduleType.GetMethod("Stop");
            _disposeMethod = _moduleType.GetMethod("Dispose");
            _getUserControlMethod = _moduleType.GetMethod("GetUserControl");
            _nameProperty = _moduleType.GetProperty("Name");
            _descriptionProperty = _moduleType.GetProperty("Description");
            _versionProperty = _moduleType.GetProperty("Version");
            _eventAggregatorProperty = _moduleType.GetProperty("EventAggregator");
            _loggerProperty = _moduleType.GetProperty("Logger");
        }

        public string Name => _nameProperty?.GetValue(_moduleInstance)?.ToString() ?? _moduleType.Name;

        public string Description => _descriptionProperty?.GetValue(_moduleInstance)?.ToString() ?? "无描述";

        public string Version => _versionProperty?.GetValue(_moduleInstance)?.ToString() ?? "1.0.0";

        public IEventAggregator EventAggregator
        {
            get => _eventAggregatorProperty?.GetValue(_moduleInstance) as IEventAggregator;
            set => _eventAggregatorProperty?.SetValue(_moduleInstance, value);
        }

        public ILogger Logger
        {
            get => _loggerProperty?.GetValue(_moduleInstance) as ILogger;
            set => _loggerProperty?.SetValue(_moduleInstance, value);
        }

        public void Initialize()
        {
            _initializeMethod?.Invoke(_moduleInstance, null);
        }

        public void Start()
        {
            _startMethod?.Invoke(_moduleInstance, null);
        }

        public void Stop()
        {
            _stopMethod?.Invoke(_moduleInstance, null);
        }

        public System.Windows.Forms.UserControl GetUserControl()
        {
            return _getUserControlMethod?.Invoke(_moduleInstance, null) as System.Windows.Forms.UserControl;
        }

        public void Dispose()
        {
            _disposeMethod?.Invoke(_moduleInstance, null);
        }
    }
}
