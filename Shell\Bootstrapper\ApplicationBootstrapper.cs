using System;
using System.IO;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;
using Services;
using DryIoc;

namespace Shell.Bootstrapper
{
    /// <summary>
    /// 应用程序引导程序
    /// </summary>
    /// <remarks>
    /// 负责协调整个应用程序的启动过程，包括容器初始化、模块加载等
    /// </remarks>
    public class ApplicationBootstrapper : IDisposable
    {
        private IContainer _container;
        private ModuleLoader _moduleLoader;
        private MainForm _mainForm;
        private readonly ILogger _logger;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public ApplicationBootstrapper(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 初始化应用程序
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public bool Initialize()
        {
            try
            {
                _logger.Info("开始初始化应用程序");

                // 1. 创建和配置服务容器
                _logger.Info("步骤1: 创建服务容器");
                _container = ContainerBootstrapper.CreateContainer(_logger);

                // 2. 创建主窗体
                _logger.Info("步骤2: 创建主窗体");
                _mainForm = _container.Resolve<MainForm>();
                if (_mainForm == null)
                {
                    _logger.Error("无法创建主窗体");
                    return false;
                }

                // 3. 创建模块加载器
                _logger.Info("步骤3: 创建模块加载器");
                _moduleLoader = _container.Resolve<ModuleLoader>();
                if (_moduleLoader == null)
                {
                    _logger.Error("无法创建模块加载器");
                    return false;
                }

                // 4. 加载模块
                _logger.Info("步骤4: 加载模块");
                LoadModules();

                // 5. 初始化主窗体
                _logger.Info("步骤5: 初始化主窗体");
                InitializeMainForm();

                _logger.Info("应用程序初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("应用程序初始化失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 加载模块
        /// </summary>
        private void LoadModules()
        {
            try
            {
                // 确保模块目录存在
                var modulesDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Modules");
                if (!Directory.Exists(modulesDirectory))
                {
                    Directory.CreateDirectory(modulesDirectory);
                    _logger.Info($"创建模块目录: {modulesDirectory}");
                }

                // 从模块目录加载模块
                var loadedCount = _moduleLoader.LoadModulesFromDirectory(modulesDirectory);
                _logger.Info($"从目录 {modulesDirectory} 加载了 {loadedCount} 个模块");

                // 将加载的模块添加到主窗体
                foreach (var module in _moduleLoader.LoadedModules)
                {
                    try
                    {
                        var userControl = module.GetUserControl();
                        if (userControl != null)
                        {
                            _mainForm.AddModuleTab(module.Name, userControl);
                            _logger.Debug($"为模块 {module.Name} 添加了UI标签页");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"为模块 {module.Name} 添加UI失败", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error("加载模块过程中发生错误", ex);
            }
        }

        /// <summary>
        /// 初始化主窗体
        /// </summary>
        private void InitializeMainForm()
        {
            try
            {
                // 手动注入依赖
                _mainForm.EventAggregator = _container.Resolve<IEventAggregator>();
                _mainForm.Logger = _container.Resolve<ILogger>();

                // 设置窗体关闭事件处理
                _mainForm.FormClosing += MainForm_FormClosing;

                // 设置窗体标题
                _mainForm.Text = $"工业HMI系统 - v{Application.ProductVersion}";

                _logger.Debug("主窗体初始化完成");
            }
            catch (Exception ex)
            {
                _logger.Error("初始化主窗体失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 主窗体关闭事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                _logger.Info("用户请求关闭应用程序");

                // 发布系统关闭事件，允许模块进行清理
                var eventAggregator = _container?.Resolve<IEventAggregator>();
                if (eventAggregator != null)
                {
                    var shutdownEvent = new SystemShutdownEvent(ShutdownReason.UserRequest, true);
                    eventAggregator.GetEvent<SystemShutdownEvent>().Publish(shutdownEvent);

                    // 检查是否有模块取消了关闭操作
                    if (shutdownEvent.Cancel)
                    {
                        _logger.Info("关闭操作被取消");
                        e.Cancel = true;
                        return;
                    }
                }

                // 卸载所有模块
                _moduleLoader?.UnloadAllModules();

                _logger.Info("应用程序关闭流程完成");
            }
            catch (Exception ex)
            {
                _logger.Error("处理窗体关闭事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取主窗体
        /// </summary>
        /// <returns>主窗体实例</returns>
        public MainForm GetMainForm()
        {
            return _mainForm;
        }

        /// <summary>
        /// 获取事件聚合器
        /// </summary>
        /// <returns>事件聚合器实例</returns>
        public IEventAggregator GetEventAggregator()
        {
            return _container?.Resolve<IEventAggregator>();
        }

        /// <summary>
        /// 获取容器
        /// </summary>
        /// <returns>服务容器实例</returns>
        public IContainer GetContainer()
        {
            return _container;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    _logger.Info("开始释放应用程序资源");

                    // 卸载模块
                    _moduleLoader?.Dispose();

                    // 关闭主窗体
                    if (_mainForm != null && !_mainForm.IsDisposed)
                    {
                        _mainForm.FormClosing -= MainForm_FormClosing;
                        _mainForm.Dispose();
                    }

                    // 清理容器
                    _container = null;

                    _logger.Info("应用程序资源释放完成");
                }
                catch (Exception ex)
                {
                    _logger.Error("释放应用程序资源时发生错误", ex);
                }
                finally
                {
                    _disposed = true;
                }
            }
        }
    }
}
