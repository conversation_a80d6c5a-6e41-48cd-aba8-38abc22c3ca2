using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Contracts;
using DeviceModule.Models;

namespace DeviceModule.Views
{
    /// <summary>
    /// 设备监控视图
    /// </summary>
    /// <remarks>
    /// 提供设备列表显示、状态监控和操作控制的用户界面
    /// </remarks>
    public partial class DeviceView : UserControl, IView
    {
        private DataGridView _deviceDataGridView;
        private Button _refreshButton;
        private Button _connectAllButton;
        private Button _disconnectAllButton;
        private Button _startMonitoringButton;
        private Button _stopMonitoringButton;
        private Label _statusLabel;
        private ProgressBar _progressBar;
        private Panel _controlPanel;
        private Panel _statusPanel;

        /// <summary>
        /// 设备列表更新事件
        /// </summary>
        public event EventHandler RefreshRequested;

        /// <summary>
        /// 连接所有设备事件
        /// </summary>
        public event EventHandler ConnectAllRequested;

        /// <summary>
        /// 断开所有设备事件
        /// </summary>
        public event EventHandler DisconnectAllRequested;

        /// <summary>
        /// 开始监控事件
        /// </summary>
        public event EventHandler StartMonitoringRequested;

        /// <summary>
        /// 停止监控事件
        /// </summary>
        public event EventHandler StopMonitoringRequested;

        /// <summary>
        /// 设备连接事件
        /// </summary>
        public event EventHandler<DeviceActionEventArgs> DeviceConnectRequested;

        /// <summary>
        /// 设备断开事件
        /// </summary>
        public event EventHandler<DeviceActionEventArgs> DeviceDisconnectRequested;

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceView()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            SuspendLayout();

            // 设置主控件属性
            Name = "DeviceView";
            Size = new Size(800, 600);
            BackColor = Color.White;

            // 创建控制面板
            CreateControlPanel();

            // 创建设备列表
            CreateDeviceDataGridView();

            // 创建状态面板
            CreateStatusPanel();

            ResumeLayout(false);
        }

        /// <summary>
        /// 创建控制面板
        /// </summary>
        private void CreateControlPanel()
        {
            _controlPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.LightGray,
                Padding = new Padding(10, 10, 10, 10)
            };

            // 刷新按钮
            _refreshButton = new Button
            {
                Text = "刷新设备",
                Size = new Size(80, 30),
                Location = new Point(10, 10),
                UseVisualStyleBackColor = true
            };

            // 连接所有按钮
            _connectAllButton = new Button
            {
                Text = "连接所有",
                Size = new Size(80, 30),
                Location = new Point(100, 10),
                UseVisualStyleBackColor = true
            };

            // 断开所有按钮
            _disconnectAllButton = new Button
            {
                Text = "断开所有",
                Size = new Size(80, 30),
                Location = new Point(190, 10),
                UseVisualStyleBackColor = true
            };

            // 开始监控按钮
            _startMonitoringButton = new Button
            {
                Text = "开始监控",
                Size = new Size(80, 30),
                Location = new Point(280, 10),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen
            };

            // 停止监控按钮
            _stopMonitoringButton = new Button
            {
                Text = "停止监控",
                Size = new Size(80, 30),
                Location = new Point(370, 10),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightCoral,
                Enabled = false
            };

            _controlPanel.Controls.AddRange(new Control[]
            {
                _refreshButton, _connectAllButton, _disconnectAllButton,
                _startMonitoringButton, _stopMonitoringButton
            });

            Controls.Add(_controlPanel);
        }

        /// <summary>
        /// 创建设备数据表格
        /// </summary>
        private void CreateDeviceDataGridView()
        {
            _deviceDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };

            // 添加列
            _deviceDataGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "DeviceId",
                    HeaderText = "设备ID",
                    DataPropertyName = "DeviceId",
                    FillWeight = 15
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Name",
                    HeaderText = "设备名称",
                    DataPropertyName = "Name",
                    FillWeight = 20
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Type",
                    HeaderText = "设备类型",
                    DataPropertyName = "Type",
                    FillWeight = 15
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Address",
                    HeaderText = "连接地址",
                    DataPropertyName = "Address",
                    FillWeight = 20
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Status",
                    HeaderText = "连接状态",
                    DataPropertyName = "Status",
                    FillWeight = 15
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CurrentValue",
                    HeaderText = "当前值",
                    DataPropertyName = "CurrentValue",
                    FillWeight = 15
                }
            });

            // 添加操作按钮列
            var connectButtonColumn = new DataGridViewButtonColumn
            {
                Name = "ConnectButton",
                HeaderText = "连接",
                Text = "连接",
                UseColumnTextForButtonValue = true,
                FillWeight = 10
            };

            var disconnectButtonColumn = new DataGridViewButtonColumn
            {
                Name = "DisconnectButton",
                HeaderText = "断开",
                Text = "断开",
                UseColumnTextForButtonValue = true,
                FillWeight = 10
            };

            _deviceDataGridView.Columns.Add(connectButtonColumn);
            _deviceDataGridView.Columns.Add(disconnectButtonColumn);

            Controls.Add(_deviceDataGridView);
        }

        /// <summary>
        /// 创建状态面板
        /// </summary>
        private void CreateStatusPanel()
        {
            _statusPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 30,
                BackColor = Color.LightGray
            };

            _statusLabel = new Label
            {
                Text = "就绪",
                AutoSize = false,
                Size = new Size(200, 20),
                Location = new Point(10, 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _progressBar = new ProgressBar
            {
                Size = new Size(200, 20),
                Location = new Point(220, 5),
                Visible = false
            };

            _statusPanel.Controls.AddRange(new Control[] { _statusLabel, _progressBar });
            Controls.Add(_statusPanel);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            _refreshButton.Click += (s, e) => RefreshRequested?.Invoke(this, EventArgs.Empty);
            _connectAllButton.Click += (s, e) => ConnectAllRequested?.Invoke(this, EventArgs.Empty);
            _disconnectAllButton.Click += (s, e) => DisconnectAllRequested?.Invoke(this, EventArgs.Empty);
            _startMonitoringButton.Click += (s, e) => StartMonitoringRequested?.Invoke(this, EventArgs.Empty);
            _stopMonitoringButton.Click += (s, e) => StopMonitoringRequested?.Invoke(this, EventArgs.Empty);

            _deviceDataGridView.CellClick += OnDeviceDataGridViewCellClick;
        }

        /// <summary>
        /// 数据表格单元格点击事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnDeviceDataGridViewCellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

            var deviceId = _deviceDataGridView.Rows[e.RowIndex].Cells["DeviceId"].Value?.ToString();
            if (string.IsNullOrEmpty(deviceId)) return;

            var columnName = _deviceDataGridView.Columns[e.ColumnIndex].Name;

            switch (columnName)
            {
                case "ConnectButton":
                    DeviceConnectRequested?.Invoke(this, new DeviceActionEventArgs(deviceId));
                    break;
                case "DisconnectButton":
                    DeviceDisconnectRequested?.Invoke(this, new DeviceActionEventArgs(deviceId));
                    break;
            }
        }

        /// <summary>
        /// 显示设备列表
        /// </summary>
        /// <param name="devices">设备列表</param>
        public void ShowDevices(List<DeviceViewModel> devices)
        {
            SafeUpdateUI(() =>
            {
                _deviceDataGridView.DataSource = null;
                _deviceDataGridView.DataSource = devices;
                
                // 设置状态列的颜色
                foreach (DataGridViewRow row in _deviceDataGridView.Rows)
                {
                    var status = row.Cells["Status"].Value?.ToString();
                    if (status == "已连接")
                    {
                        row.Cells["Status"].Style.BackColor = Color.LightGreen;
                    }
                    else if (status == "已断开" || status == "未连接")
                    {
                        row.Cells["Status"].Style.BackColor = Color.LightCoral;
                    }
                }
                
                UpdateStatus($"显示 {devices.Count} 个设备");
            });
        }

        /// <summary>
        /// 更新设备状态
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="status">状态</param>
        public void UpdateDeviceStatus(string deviceId, string status)
        {
            SafeUpdateUI(() =>
            {
                foreach (DataGridViewRow row in _deviceDataGridView.Rows)
                {
                    if (row.Cells["DeviceId"].Value?.ToString() == deviceId)
                    {
                        row.Cells["Status"].Value = status;
                        
                        // 设置状态颜色
                        if (status == "已连接")
                        {
                            row.Cells["Status"].Style.BackColor = Color.LightGreen;
                        }
                        else
                        {
                            row.Cells["Status"].Style.BackColor = Color.LightCoral;
                        }
                        break;
                    }
                }
            });
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        /// <param name="message">状态消息</param>
        public void UpdateStatus(string message)
        {
            SafeUpdateUI(() =>
            {
                _statusLabel.Text = message;
            });
        }

        /// <summary>
        /// 显示进度
        /// </summary>
        /// <param name="show">是否显示</param>
        /// <param name="value">进度值</param>
        public void ShowProgress(bool show, int value = 0)
        {
            SafeUpdateUI(() =>
            {
                _progressBar.Visible = show;
                if (show)
                {
                    _progressBar.Value = Math.Max(0, Math.Min(100, value));
                }
            });
        }

        /// <summary>
        /// 设置监控状态
        /// </summary>
        /// <param name="isMonitoring">是否正在监控</param>
        public void SetMonitoringState(bool isMonitoring)
        {
            SafeUpdateUI(() =>
            {
                _startMonitoringButton.Enabled = !isMonitoring;
                _stopMonitoringButton.Enabled = isMonitoring;
                
                if (isMonitoring)
                {
                    UpdateStatus("设备监控中...");
                }
                else
                {
                    UpdateStatus("设备监控已停止");
                }
            });
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="message">错误消息</param>
        public void ShowError(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus($"错误: {message}");
            });
        }

        /// <summary>
        /// 显示信息
        /// </summary>
        /// <param name="message">信息消息</param>
        public void ShowInfo(string message)
        {
            SafeUpdateUI(() =>
            {
                UpdateStatus(message);
            });
        }

        /// <summary>
        /// 显示消息（IView接口要求）
        /// </summary>
        /// <param name="message">消息内容</param>
        public void ShowMessage(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                UpdateStatus(message);
            });
        }

        /// <summary>
        /// 显示警告（IView接口要求）
        /// </summary>
        /// <param name="message">警告消息</param>
        public void ShowWarning(string message)
        {
            SafeUpdateUI(() =>
            {
                MessageBox.Show(message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                UpdateStatus($"警告: {message}");
            });
        }

        /// <summary>
        /// 设置加载状态（IView接口要求）
        /// </summary>
        /// <param name="isLoading">是否正在加载</param>
        /// <param name="message">加载消息</param>
        public void SetLoadingState(bool isLoading, string message = "")
        {
            ShowProgress(isLoading);
            if (!string.IsNullOrEmpty(message))
            {
                UpdateStatus(message);
            }
        }

        /// <summary>
        /// 刷新数据（IView接口要求）
        /// </summary>
        public void RefreshData()
        {
            RefreshRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 线程安全的UI更新
        /// </summary>
        /// <param name="action">更新操作</param>
        private void SafeUpdateUI(Action action)
        {
            if (InvokeRequired)
            {
                Invoke(action);
            }
            else
            {
                action();
            }
        }
    }

    /// <summary>
    /// 设备操作事件参数
    /// </summary>
    public class DeviceActionEventArgs : EventArgs
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        public DeviceActionEventArgs(string deviceId)
        {
            DeviceId = deviceId;
        }
    }
}
