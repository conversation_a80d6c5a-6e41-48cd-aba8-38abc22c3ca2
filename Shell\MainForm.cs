using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using Contracts;
using Contracts.Events;

namespace Shell
{
    /// <summary>
    /// 主窗体
    /// </summary>
    /// <remarks>
    /// 应用程序的主界面，负责显示和管理各个模块的UI
    /// </remarks>
    public partial class MainForm : Form
    {
        private TabControl _moduleTabControl;
        private StatusStrip _statusStrip;
        private ToolStripStatusLabel _statusLabel;
        private ToolStripStatusLabel _moduleCountLabel;
        private ToolStripStatusLabel _memoryUsageLabel;
        private MenuStrip _menuStrip;
        private ToolStrip _toolStrip;
        private IEventAggregator _eventAggregator;
        private ILogger _logger;
        private Timer _statusUpdateTimer;

        /// <summary>
        /// 事件聚合器属性（用于依赖注入）
        /// </summary>
        public IEventAggregator EventAggregator { get; set; }

        /// <summary>
        /// 日志记录器属性（用于依赖注入）
        /// </summary>
        public ILogger Logger { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
            InitializeMainForm();
        }

        /// <summary>
        /// 初始化主窗体
        /// </summary>
        private void InitializeMainForm()
        {
            // 设置窗体属性
            this.Text = "工业HMI系统";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            // 创建菜单栏
            CreateMenuStrip();

            // 创建工具栏
            CreateToolStrip();

            // 创建模块标签页控件
            CreateModuleTabControl();

            // 创建状态栏
            CreateStatusStrip();

            // 启动状态更新定时器
            StartStatusUpdateTimer();

            // 订阅事件（在依赖注入完成后）
            this.Load += MainForm_Load;
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 获取注入的依赖
            _eventAggregator = EventAggregator;
            _logger = Logger;

            if (_eventAggregator != null)
            {
                // 订阅系统事件
                _eventAggregator.GetEvent<SystemStartupEvent>()
                    .Subscribe(OnSystemStartup, ThreadOption.UIThread, false);

                _eventAggregator.GetEvent<SystemShutdownEvent>()
                    .Subscribe(OnSystemShutdown, ThreadOption.UIThread, false);

                _eventAggregator.GetEvent<ModuleLoadedEvent>()
                    .Subscribe(OnModuleLoaded, ThreadOption.UIThread, false);

                _logger?.Debug("主窗体事件订阅完成");
            }

            UpdateStatusMessage("系统就绪");
        }

        /// <summary>
        /// 创建菜单栏
        /// </summary>
        private void CreateMenuStrip()
        {
            _menuStrip = new MenuStrip();

            // 文件菜单
            var fileMenu = new ToolStripMenuItem("文件(&F)");
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("重新加载模块(&L)", null, (s, e) => ReloadModules()));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("退出(&X)", null, (s, e) => this.Close()));

            // 视图菜单
            var viewMenu = new ToolStripMenuItem("视图(&V)");
            viewMenu.DropDownItems.Add(new ToolStripMenuItem("刷新模块(&R)", null, (s, e) => RefreshModules()));
            viewMenu.DropDownItems.Add(new ToolStripSeparator());
            viewMenu.DropDownItems.Add(new ToolStripMenuItem("全屏(&F)", null, (s, e) => ToggleFullScreen()));

            // 工具菜单
            var toolsMenu = new ToolStripMenuItem("工具(&T)");
            toolsMenu.DropDownItems.Add(new ToolStripMenuItem("系统信息(&S)", null, (s, e) => ShowSystemInfo()));
            toolsMenu.DropDownItems.Add(new ToolStripMenuItem("内存清理(&M)", null, (s, e) => ForceGarbageCollection()));

            // 帮助菜单
            var helpMenu = new ToolStripMenuItem("帮助(&H)");
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("用户手册(&U)", null, (s, e) => ShowUserManual()));
            helpMenu.DropDownItems.Add(new ToolStripSeparator());
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("关于(&A)", null, (s, e) => ShowAboutDialog()));

            _menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, viewMenu, toolsMenu, helpMenu });
            this.MainMenuStrip = _menuStrip;
            this.Controls.Add(_menuStrip);
        }

        /// <summary>
        /// 创建工具栏
        /// </summary>
        private void CreateToolStrip()
        {
            _toolStrip = new ToolStrip();
            _toolStrip.ImageScalingSize = new Size(24, 24);

            // 刷新按钮
            var refreshButton = new ToolStripButton("刷新模块");
            refreshButton.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            refreshButton.Click += (s, e) => RefreshModules();

            // 重新加载按钮
            var reloadButton = new ToolStripButton("重新加载");
            reloadButton.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            reloadButton.Click += (s, e) => ReloadModules();

            // 分隔符
            var separator1 = new ToolStripSeparator();

            // 系统信息按钮
            var systemInfoButton = new ToolStripButton("系统信息");
            systemInfoButton.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            systemInfoButton.Click += (s, e) => ShowSystemInfo();

            // 内存清理按钮
            var gcButton = new ToolStripButton("内存清理");
            gcButton.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            gcButton.Click += (s, e) => ForceGarbageCollection();

            _toolStrip.Items.AddRange(new ToolStripItem[]
            {
                refreshButton, reloadButton, separator1, systemInfoButton, gcButton
            });

            this.Controls.Add(_toolStrip);
        }

        /// <summary>
        /// 创建模块标签页控件
        /// </summary>
        private void CreateModuleTabControl()
        {
            _moduleTabControl = new TabControl();
            _moduleTabControl.Dock = DockStyle.Fill;
            _moduleTabControl.Font = new Font("Microsoft YaHei", 9F);

            // 添加欢迎页面
            var welcomeTab = new TabPage("欢迎");
            var welcomeLabel = new Label
            {
                Text = "欢迎使用工业HMI系统\n\n系统正在加载模块，请稍候...",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Microsoft YaHei", 12F)
            };
            welcomeTab.Controls.Add(welcomeLabel);
            _moduleTabControl.TabPages.Add(welcomeTab);

            this.Controls.Add(_moduleTabControl);
        }

        /// <summary>
        /// 创建状态栏
        /// </summary>
        private void CreateStatusStrip()
        {
            _statusStrip = new StatusStrip();

            // 主状态标签
            _statusLabel = new ToolStripStatusLabel("正在初始化...");
            _statusLabel.Spring = true;
            _statusLabel.TextAlign = ContentAlignment.MiddleLeft;

            // 模块数量标签
            _moduleCountLabel = new ToolStripStatusLabel("模块: 0");
            _moduleCountLabel.BorderSides = ToolStripStatusLabelBorderSides.Left;

            // 内存使用标签
            _memoryUsageLabel = new ToolStripStatusLabel("内存: 0 MB");
            _memoryUsageLabel.BorderSides = ToolStripStatusLabelBorderSides.Left;

            // 时间标签
            var timeLabel = new ToolStripStatusLabel();
            timeLabel.BorderSides = ToolStripStatusLabelBorderSides.Left;
            var timer = new Timer { Interval = 1000 };
            timer.Tick += (s, e) => timeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            timer.Start();

            _statusStrip.Items.AddRange(new ToolStripItem[]
            {
                _statusLabel, _moduleCountLabel, _memoryUsageLabel, timeLabel
            });
            this.Controls.Add(_statusStrip);
        }

        /// <summary>
        /// 启动状态更新定时器
        /// </summary>
        private void StartStatusUpdateTimer()
        {
            _statusUpdateTimer = new Timer { Interval = 5000 }; // 每5秒更新一次
            _statusUpdateTimer.Tick += (s, e) => UpdateSystemStatus();
            _statusUpdateTimer.Start();
        }

        /// <summary>
        /// 添加模块标签页
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="userControl">模块UI控件</param>
        public void AddModuleTab(string moduleName, UserControl userControl)
        {
            if (string.IsNullOrEmpty(moduleName) || userControl == null)
                return;

            SafeUpdateUI(() =>
            {
                // 移除欢迎页面（如果存在）
                if (_moduleTabControl.TabPages.Count > 0 && _moduleTabControl.TabPages[0].Text == "欢迎")
                {
                    _moduleTabControl.TabPages.RemoveAt(0);
                }

                // 创建新的标签页
                var tabPage = new TabPage(moduleName);
                userControl.Dock = DockStyle.Fill;
                tabPage.Controls.Add(userControl);
                _moduleTabControl.TabPages.Add(tabPage);

                // 更新模块计数
                UpdateModuleCount();

                _logger?.Debug($"添加模块标签页: {moduleName}");
            });
        }

        /// <summary>
        /// 移除模块标签页
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        public void RemoveModuleTab(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
                return;

            SafeUpdateUI(() =>
            {
                for (int i = _moduleTabControl.TabPages.Count - 1; i >= 0; i--)
                {
                    if (_moduleTabControl.TabPages[i].Text == moduleName)
                    {
                        _moduleTabControl.TabPages.RemoveAt(i);

                        // 更新模块计数
                        UpdateModuleCount();

                        _logger?.Debug($"移除模块标签页: {moduleName}");
                        break;
                    }
                }
            });
        }

        /// <summary>
        /// 更新状态消息
        /// </summary>
        /// <param name="message">状态消息</param>
        public void UpdateStatusMessage(string message)
        {
            SafeUpdateUI(() =>
            {
                if (_statusLabel != null)
                {
                    _statusLabel.Text = message;
                }
            });
        }

        /// <summary>
        /// 安全更新UI
        /// </summary>
        /// <param name="action">要执行的UI更新操作</param>
        private void SafeUpdateUI(Action action)
        {
            if (InvokeRequired)
            {
                BeginInvoke(action);
            }
            else
            {
                action();
            }
        }

        /// <summary>
        /// 系统启动事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemStartup(SystemStartupEvent eventData)
        {
            UpdateStatusMessage($"系统启动完成 - 版本 {eventData.Version}");
            _logger?.Info($"收到系统启动事件，版本: {eventData.Version}");
        }

        /// <summary>
        /// 系统关闭事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemShutdown(SystemShutdownEvent eventData)
        {
            UpdateStatusMessage("系统正在关闭...");
            _logger?.Info($"收到系统关闭事件，原因: {eventData.Reason}");
        }

        /// <summary>
        /// 模块加载事件处理
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnModuleLoaded(ModuleLoadedEvent eventData)
        {
            UpdateStatusMessage($"模块已加载: {eventData.ModuleName}");
            _logger?.Info($"收到模块加载事件: {eventData.ModuleName}");
        }

        /// <summary>
        /// 更新模块计数
        /// </summary>
        private void UpdateModuleCount()
        {
            SafeUpdateUI(() =>
            {
                var moduleCount = _moduleTabControl.TabPages.Count;
                // 如果有欢迎页面，不计入模块数量
                if (moduleCount > 0 && _moduleTabControl.TabPages[0].Text == "欢迎")
                {
                    moduleCount--;
                }
                _moduleCountLabel.Text = $"模块: {moduleCount}";
            });
        }

        /// <summary>
        /// 更新系统状态
        /// </summary>
        private void UpdateSystemStatus()
        {
            SafeUpdateUI(() =>
            {
                try
                {
                    // 更新内存使用情况
                    var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
                    _memoryUsageLabel.Text = $"内存: {memoryUsage} MB";
                }
                catch (Exception ex)
                {
                    _logger?.Warning($"更新系统状态失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 刷新模块
        /// </summary>
        private void RefreshModules()
        {
            UpdateStatusMessage("刷新模块...");
            try
            {
                // 刷新当前选中的模块
                if (_moduleTabControl.SelectedTab != null)
                {
                    var selectedTab = _moduleTabControl.SelectedTab;
                    if (selectedTab.Controls.Count > 0 && selectedTab.Controls[0] is UserControl userControl)
                    {
                        // 如果模块实现了刷新接口，调用刷新方法
                        if (userControl is Contracts.IView view)
                        {
                            view.RefreshData();
                        }
                    }
                }
                UpdateStatusMessage("模块刷新完成");
                _logger?.Info("模块刷新完成");
            }
            catch (Exception ex)
            {
                UpdateStatusMessage("模块刷新失败");
                _logger?.Error("模块刷新失败", ex);
            }
        }

        /// <summary>
        /// 重新加载模块
        /// </summary>
        private void ReloadModules()
        {
            UpdateStatusMessage("重新加载模块...");
            try
            {
                // 发布重新加载模块事件
                _eventAggregator?.GetEvent<SystemEvent>()
                    .Publish(new SystemEvent("ReloadModules", "用户请求重新加载模块"));

                UpdateStatusMessage("模块重新加载请求已发送");
                _logger?.Info("发送模块重新加载请求");
            }
            catch (Exception ex)
            {
                UpdateStatusMessage("重新加载模块失败");
                _logger?.Error("重新加载模块失败", ex);
            }
        }

        /// <summary>
        /// 切换全屏模式
        /// </summary>
        private void ToggleFullScreen()
        {
            try
            {
                if (this.WindowState == FormWindowState.Maximized && this.FormBorderStyle == FormBorderStyle.None)
                {
                    // 退出全屏
                    this.FormBorderStyle = FormBorderStyle.Sizable;
                    this.WindowState = FormWindowState.Maximized;
                    _menuStrip.Visible = true;
                    _toolStrip.Visible = true;
                    _statusStrip.Visible = true;
                }
                else
                {
                    // 进入全屏
                    this.FormBorderStyle = FormBorderStyle.None;
                    this.WindowState = FormWindowState.Maximized;
                    _menuStrip.Visible = false;
                    _toolStrip.Visible = false;
                    _statusStrip.Visible = false;
                }
                _logger?.Info($"切换全屏模式: {(this.FormBorderStyle == FormBorderStyle.None ? "全屏" : "窗口")}");
            }
            catch (Exception ex)
            {
                _logger?.Error("切换全屏模式失败", ex);
            }
        }

        /// <summary>
        /// 显示系统信息
        /// </summary>
        private void ShowSystemInfo()
        {
            try
            {
                var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024);
                var moduleCount = _moduleTabControl.TabPages.Count;
                if (moduleCount > 0 && _moduleTabControl.TabPages[0].Text == "欢迎")
                {
                    moduleCount--;
                }

                var systemInfo = $"系统信息\n\n" +
                    $"应用程序版本: {Application.ProductVersion}\n" +
                    $"运行时版本: {Environment.Version}\n" +
                    $"操作系统: {Environment.OSVersion}\n" +
                    $"处理器数量: {Environment.ProcessorCount}\n" +
                    $"工作集内存: {Environment.WorkingSet / (1024 * 1024)} MB\n" +
                    $"托管内存: {memoryUsage} MB\n" +
                    $"已加载模块: {moduleCount}\n" +
                    $"启动时间: {DateTime.Now.Subtract(Process.GetCurrentProcess().StartTime):hh\\:mm\\:ss}";

                MessageBox.Show(systemInfo, "系统信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                _logger?.Info("显示系统信息");
            }
            catch (Exception ex)
            {
                _logger?.Error("显示系统信息失败", ex);
                MessageBox.Show("获取系统信息失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 强制垃圾回收
        /// </summary>
        private void ForceGarbageCollection()
        {
            try
            {
                var beforeMemory = GC.GetTotalMemory(false) / (1024 * 1024);

                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var afterMemory = GC.GetTotalMemory(false) / (1024 * 1024);
                var freedMemory = beforeMemory - afterMemory;

                UpdateStatusMessage($"内存清理完成，释放了 {freedMemory} MB 内存");
                _logger?.Info($"执行内存清理，释放了 {freedMemory} MB 内存");

                // 立即更新内存显示
                UpdateSystemStatus();
            }
            catch (Exception ex)
            {
                _logger?.Error("内存清理失败", ex);
                UpdateStatusMessage("内存清理失败");
            }
        }

        /// <summary>
        /// 显示用户手册
        /// </summary>
        private void ShowUserManual()
        {
            try
            {
                var manualPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents", "UserManual.pdf");
                if (File.Exists(manualPath))
                {
                    Process.Start(manualPath);
                    _logger?.Info("打开用户手册");
                }
                else
                {
                    MessageBox.Show("用户手册文件不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _logger?.Warning("用户手册文件不存在");
                }
            }
            catch (Exception ex)
            {
                _logger?.Error("打开用户手册失败", ex);
                MessageBox.Show("无法打开用户手册", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示关于对话框
        /// </summary>
        private void ShowAboutDialog()
        {
            try
            {
                var aboutText = $"工业HMI系统\n\n" +
                    $"版本: {Application.ProductVersion}\n" +
                    $"构建日期: {File.GetLastWriteTime(Application.ExecutablePath):yyyy-MM-dd}\n" +
                    $"运行时: .NET Framework {Environment.Version}\n\n" +
                    $"基于模块化架构的工业上位机软件\n" +
                    $"支持动态模块加载和依赖注入\n\n" +
                    $"技术栈:\n" +
                    $"• WinForms UI框架\n" +
                    $"• DryIoc依赖注入容器\n" +
                    $"• Serilog日志框架\n" +
                    $"• MVP架构模式";

                MessageBox.Show(aboutText, "关于工业HMI系统", MessageBoxButtons.OK, MessageBoxIcon.Information);
                _logger?.Info("显示关于对话框");
            }
            catch (Exception ex)
            {
                _logger?.Error("显示关于对话框失败", ex);
            }
        }

        /// <summary>
        /// 窗体关闭时取消事件订阅
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                // 停止状态更新定时器
                _statusUpdateTimer?.Stop();
                _statusUpdateTimer?.Dispose();

                // 取消事件订阅
                if (_eventAggregator != null)
                {
                    _eventAggregator.GetEvent<SystemStartupEvent>().Unsubscribe(OnSystemStartup);
                    _eventAggregator.GetEvent<SystemShutdownEvent>().Unsubscribe(OnSystemShutdown);
                    _eventAggregator.GetEvent<ModuleLoadedEvent>().Unsubscribe(OnModuleLoaded);
                }

                _logger?.Info("主窗体已关闭，资源清理完成");
            }
            catch (Exception ex)
            {
                _logger?.Error("主窗体关闭时清理资源失败", ex);
            }

            base.OnFormClosed(e);
        }
    }
}
